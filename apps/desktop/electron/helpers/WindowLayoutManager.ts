/**
 * WindowLayoutManager.ts
 * 
 * Manages intelligent positioning of multiple windows to prevent overlapping.
 * Based on Glass app's approach - calculates optimal positions based on screen space.
 */

import { BrowserWindow, screen, Display } from 'electron'

interface Rectangle {
  x: number
  y: number
  width: number
  height: number
}

interface WindowVisibility {
  askAI?: boolean
  microphone?: boolean
  settings?: boolean
}

interface LayoutStrategy {
  name: string
  primary: 'above' | 'below' | 'left' | 'right'
  secondary: 'above' | 'below' | 'left' | 'right'
}

/**
 * Gets the display that contains the center of the given window
 */
function getCurrentDisplay(window: BrowserWindow): Display {
  if (!window || window.isDestroyed()) {
    return screen.getPrimaryDisplay()
  }

  const windowBounds = window.getBounds()
  const windowCenter = {
    x: windowBounds.x + windowBounds.width / 2,
    y: windowBounds.y + windowBounds.height / 2,
  }

  return screen.getDisplayNearestPoint(windowCenter)
}

export class WindowLayoutManager {
  private windowPool: Map<string, BrowserWindow>
  private readonly PADDING = 8

  constructor(windowPool: Map<string, BrowserWindow>) {
    this.windowPool = windowPool
  }

  /**
   * Determines the best layout strategy based on available screen space
   */
  private determineLayoutStrategy(
    headerBounds: Rectangle,
    screenWidth: number,
    screenHeight: number,
    relativeX: number,
    relativeY: number,
    workAreaX: number,
    workAreaY: number
  ): LayoutStrategy {
    const headerRelX = headerBounds.x - workAreaX
    const headerRelY = headerBounds.y - workAreaY

    const spaceBelow = screenHeight - (headerRelY + headerBounds.height)
    const spaceAbove = headerRelY
    const spaceLeft = headerRelX
    const spaceRight = screenWidth - (headerRelX + headerBounds.width)

    // Prefer below if there's enough space
    if (spaceBelow >= 400) {
      return { 
        name: 'below', 
        primary: 'below', 
        secondary: relativeX < 0.5 ? 'right' : 'left' 
      }
    } 
    // Try above if below doesn't work
    else if (spaceAbove >= 400) {
      return { 
        name: 'above', 
        primary: 'above', 
        secondary: relativeX < 0.5 ? 'right' : 'left' 
      }
    } 
    // Try right side if header is on the left
    else if (relativeX < 0.3 && spaceRight >= 600) {
      return { 
        name: 'right-side', 
        primary: 'right', 
        secondary: spaceBelow > spaceAbove ? 'below' : 'above' 
      }
    } 
    // Try left side if header is on the right
    else if (relativeX > 0.7 && spaceLeft >= 600) {
      return { 
        name: 'left-side', 
        primary: 'left', 
        secondary: spaceBelow > spaceAbove ? 'below' : 'above' 
      }
    } 
    // Fallback to adaptive positioning
    else {
      return { 
        name: 'adaptive', 
        primary: spaceBelow > spaceAbove ? 'below' : 'above', 
        secondary: spaceRight > spaceLeft ? 'right' : 'left' 
      }
    }
  }

  /**
   * Calculates optimal positions for all feature windows based on visibility
   */
  public calculateFeatureWindowLayout(
    visibility: WindowVisibility,
    headerBoundsOverride?: Rectangle
  ): Record<string, Rectangle> {
    const header = this.windowPool.get('header')
    const headerBounds = headerBoundsOverride || (header ? header.getBounds() : null)

    if (!headerBounds) return {}

    // Get display information
    let display: Display
    if (headerBoundsOverride) {
      const boundsCenter = {
        x: headerBounds.x + headerBounds.width / 2,
        y: headerBounds.y + headerBounds.height / 2,
      }
      display = screen.getDisplayNearestPoint(boundsCenter)
    } else {
      display = getCurrentDisplay(header!)
    }

    const { width: screenWidth, height: screenHeight, x: workAreaX, y: workAreaY } = display.workArea

    // Get window references and check visibility
    const askAI = this.windowPool.get('askAI')
    const microphone = this.windowPool.get('microphone')
    const settings = this.windowPool.get('settings')

    const askAIVisible = visibility.askAI && askAI && !askAI.isDestroyed()
    const microphoneVisible = visibility.microphone && microphone && !microphone.isDestroyed()
    const settingsVisible = visibility.settings && settings && !settings.isDestroyed()

    if (!askAIVisible && !microphoneVisible && !settingsVisible) {
      return {}
    }

    // Calculate relative position and strategy
    const headerCenterXRel = headerBounds.x - workAreaX + headerBounds.width / 2
    const relativeX = headerCenterXRel / screenWidth
    const relativeY = (headerBounds.y - workAreaY) / screenHeight
    
    const strategy = this.determineLayoutStrategy(
      headerBounds, screenWidth, screenHeight, relativeX, relativeY, workAreaX, workAreaY
    )

    const layout: Record<string, Rectangle> = {}

    // Default window sizes - same as main window dimensions
    const askAISize = { width: 650, height: 700 } // APP_WIDTH x APP_EXPANDED_HEIGHT
    const microphoneSize = { width: 650, height: 360 } // APP_WIDTH x APP_INLINE_RECORDING_HEIGHT
    const settingsSize = { width: 650, height: 300 } // APP_WIDTH x APP_SETTINGS_HEIGHT

    // Calculate positions based on strategy - Glass-style behavior
    if (strategy.primary === 'below') {
      const baseY = headerBounds.y + headerBounds.height + this.PADDING
      const centerX = headerBounds.x + headerBounds.width / 2

      if (askAIVisible && microphoneVisible) {
        // Both visible: side by side below header
        const totalWidth = askAISize.width + microphoneSize.width + this.PADDING
        const startX = centerX - totalWidth / 2

        layout.askAI = {
          x: startX,
          y: baseY,
          ...askAISize
        }

        layout.microphone = {
          x: startX + askAISize.width + this.PADDING,
          y: baseY,
          ...microphoneSize
        }
      } else if (askAIVisible) {
        // Only Ask AI: center below header
        layout.askAI = {
          x: centerX - askAISize.width / 2,
          y: baseY,
          ...askAISize
        }
      } else if (microphoneVisible) {
        // Only microphone: center below header
        layout.microphone = {
          x: centerX - microphoneSize.width / 2,
          y: baseY,
          ...microphoneSize
        }
      }

      // Settings positioned to the right of header (like Glass)
      if (settingsVisible) {
        layout.settings = {
          x: Math.min(
            workAreaX + screenWidth - settingsSize.width - this.PADDING,
            headerBounds.x + headerBounds.width - settingsSize.width + 170
          ),
          y: headerBounds.y + headerBounds.height + this.PADDING,
          ...settingsSize
        }
      }
    }

    // Add fallback positioning for other strategies
    else if (strategy.primary === 'right') {
      const baseX = headerBounds.x + headerBounds.width + this.PADDING * 4

      if (askAIVisible) {
        layout.askAI = {
          x: baseX,
          y: headerBounds.y,
          ...askAISize
        }
      }

      if (microphoneVisible) {
        layout.microphone = {
          x: baseX + (askAIVisible ? askAISize.width + this.PADDING : 0),
          y: headerBounds.y,
          ...microphoneSize
        }
      }

      if (settingsVisible) {
        layout.settings = {
          x: baseX,
          y: headerBounds.y + headerBounds.height + this.PADDING,
          ...settingsSize
        }
      }
    }

    // Left side positioning
    else if (strategy.primary === 'left') {
      const baseX = headerBounds.x - this.PADDING * 4

      if (askAIVisible) {
        layout.askAI = {
          x: baseX - askAISize.width,
          y: headerBounds.y,
          ...askAISize
        }
      }

      if (microphoneVisible) {
        layout.microphone = {
          x: baseX - microphoneSize.width - (askAIVisible ? askAISize.width + this.PADDING : 0),
          y: headerBounds.y,
          ...microphoneSize
        }
      }

      if (settingsVisible) {
        layout.settings = {
          x: baseX - settingsSize.width,
          y: headerBounds.y + headerBounds.height + this.PADDING,
          ...settingsSize
        }
      }
    }
    // Add more positioning strategies as needed (above, left, right)

    // Ensure all windows stay within screen bounds with more generous padding
    Object.keys(layout).forEach(windowName => {
      const bounds = layout[windowName]

      // Allow windows to go slightly outside screen bounds if needed for visibility
      const minX = workAreaX - bounds.width * 0.3 // Allow 30% off-screen
      const maxX = workAreaX + screenWidth - bounds.width * 0.7 // Keep 70% on-screen
      const minY = workAreaY + this.PADDING
      const maxY = workAreaY + screenHeight - bounds.height - this.PADDING

      bounds.x = Math.max(minX, Math.min(bounds.x, maxX))
      bounds.y = Math.max(minY, Math.min(bounds.y, maxY))

      console.log(`[WindowLayoutManager] Positioned ${windowName} at (${bounds.x}, ${bounds.y})`)
    })

    return layout
  }

  /**
   * Calculates new position when header window moves
   */
  public calculateClampedPosition(
    window: BrowserWindow, 
    targetPosition: { x: number; y: number }
  ): { x: number; y: number } {
    if (!window) return targetPosition

    const targetDisplay = screen.getDisplayNearestPoint(targetPosition)
    const { x: workAreaX, y: workAreaY, width, height } = targetDisplay.workArea
    const windowBounds = window.getBounds()

    const clampedX = Math.max(workAreaX, 
      Math.min(targetPosition.x, workAreaX + width - windowBounds.width))
    const clampedY = Math.max(workAreaY, 
      Math.min(targetPosition.y, workAreaY + height - windowBounds.height))

    return { x: clampedX, y: clampedY }
  }
}
