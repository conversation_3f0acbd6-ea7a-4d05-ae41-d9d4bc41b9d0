/**
 * RAGQueryHelper.ts
 *
 * Manages communication with the Closezly backend RAG (Knowledge) service.
 * Handles document search, retrieval, and knowledge base operations.
 * Provides integration between desktop app and web-portal uploaded documents.
 */

import { EventEmitter } from 'events'
import fetch from 'node-fetch'
import <PERSON>th<PERSON><PERSON>per from './AuthHelper'

interface Document {
  id: string;
  title: string;
  filename: string;
  mimetype: string;
  size: number;
  uploadedAt: string;
  processedAt?: string;
  processingStatus: 'queued' | 'processing' | 'completed' | 'failed';
  chunkCount?: number;
  errorMessage?: string;
}

interface SearchResult {
  id: string;
  content: string;
  similarity: number;
  documentId: string;
  filename: string;
  chunkIndex: number;
  metadata: {
    mimetype: string;
    uploadedAt: string;
    processedAt?: string;
  };
}

interface SearchResponse {
  success: boolean;
  data?: {
    results: SearchResult[];
    query: string;
    totalResults: number;
    processingTime: number;
  };
  error?: string;
}

interface DocumentsResponse {
  success: boolean;
  data?: {
    documents: Document[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  error?: string;
}

interface HealthResponse {
  success: boolean;
  data?: {
    status: string;
    details: {
      database: string;
      redis: string;
      openai: string;
      timestamp: string;
    };
  };
  error?: string;
}

class RAGQueryHelper extends EventEmitter {
  private static instance: RAGQueryHelper
  private backendUrl: string
  private requestTimeout: number = 30000 // 30 seconds

  private constructor() {
    super()
    this.backendUrl = process.env.BACKEND_URL || 'http://localhost:4000'
  }

  public static getInstance(): RAGQueryHelper {
    if (!RAGQueryHelper.instance) {
      RAGQueryHelper.instance = new RAGQueryHelper()
    }
    return RAGQueryHelper.instance
  }

  /**
   * Search through the knowledge base
   */
  public async searchKnowledge(
    query: string, 
    options: {
      limit?: number;
      threshold?: number;
      documentIds?: string[];
    } = {}
  ): Promise<SearchResult[]> {
    try {
      console.log(`[RAGQuery] Searching knowledge base for: "${query}"`)

      // Get authentication tokens (consistent with getDocuments method)
      const tokens = AuthHelper.getTokens()
      if (!tokens || !tokens.accessToken) {
        throw new Error('No valid authentication token available')
      }

      const requestBody = {
        query,
        limit: options.limit || 10,
        threshold: options.threshold || 0.3,
        documentIds: options.documentIds
      }

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout)

      const response = await fetch(`${this.backendUrl}/api/knowledge/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokens.accessToken}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        if (response.status === 401) {
          // Try to refresh token and retry
          const refreshed = await AuthHelper.refreshUserProfile()
          if (refreshed) {
            return await this.searchKnowledge(query, options) // Retry once
          }
          throw new Error('Authentication failed')
        }

        const errorText = await response.text()
        throw new Error(`Search request failed: ${response.status} ${errorText}`)
      }

      const result = await response.json() as SearchResponse
      
      if (!result.success || !result.data) {
        throw new Error(result.error || 'Search failed')
      }

      console.log(`[RAGQuery] Found ${result.data.totalResults} results in ${result.data.processingTime}ms`)
      
      this.emit('search-completed', {
        query,
        results: result.data.results,
        totalResults: result.data.totalResults,
        processingTime: result.data.processingTime
      })

      return result.data.results
    } catch (error: any) {
      console.error(`[RAGQuery] Search failed: ${error.message}`)
      this.emit('search-failed', { query, error: error.message })
      throw error
    }
  }

  /**
   * Get all documents in the knowledge base
   */
  public async getDocuments(options: {
    page?: number;
    limit?: number;
    status?: string;
  } = {}): Promise<{ documents: Document[]; pagination: any }> {
    try {
      console.log('[RAGQuery] Fetching documents from knowledge base')

      const tokens = AuthHelper.getTokens()
      if (!tokens || !tokens.accessToken) {
        throw new Error('No valid authentication token available')
      }

      const params = new URLSearchParams()
      if (options.page) params.append('page', options.page.toString())
      if (options.limit) params.append('limit', options.limit.toString())
      if (options.status) params.append('status', options.status)

      const url = `${this.backendUrl}/api/knowledge/documents${params.toString() ? '?' + params.toString() : ''}`

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokens.accessToken}`
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        if (response.status === 401) {
          // Try to refresh token and retry
          const refreshed = await AuthHelper.refreshUserProfile()
          if (refreshed) {
            return await this.getDocuments(options) // Retry once
          }
          throw new Error('Authentication failed')
        }

        const errorText = await response.text()
        throw new Error(`Documents request failed: ${response.status} ${errorText}`)
      }

      const result = await response.json() as DocumentsResponse
      
      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to fetch documents')
      }

      console.log(`[RAGQuery] Retrieved ${result.data.documents.length} documents`)
      
      this.emit('documents-fetched', {
        documents: result.data.documents,
        pagination: result.data.pagination
      })

      return {
        documents: result.data.documents,
        pagination: result.data.pagination
      }
    } catch (error: any) {
      console.error(`[RAGQuery] Failed to fetch documents: ${error.message}`)
      this.emit('documents-fetch-failed', { error: error.message })
      throw error
    }
  }

  /**
   * Delete a document from the knowledge base
   */
  public async deleteDocument(documentId: string): Promise<boolean> {
    try {
      console.log(`[RAGQuery] Deleting document: ${documentId}`)

      const tokens = AuthHelper.getTokens()
      if (!tokens || !tokens.accessToken) {
        throw new Error('No valid authentication token available')
      }

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout)

      const response = await fetch(`${this.backendUrl}/api/knowledge/documents/${documentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${tokens.accessToken}`
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        if (response.status === 401) {
          // Try to refresh token and retry
          const refreshed = await AuthHelper.refreshUserProfile()
          if (refreshed) {
            return await this.deleteDocument(documentId) // Retry once
          }
          throw new Error('Authentication failed')
        }

        const errorText = await response.text()
        throw new Error(`Delete request failed: ${response.status} ${errorText}`)
      }

      console.log(`[RAGQuery] Successfully deleted document: ${documentId}`)
      
      this.emit('document-deleted', { documentId })

      return true
    } catch (error: any) {
      console.error(`[RAGQuery] Failed to delete document: ${error.message}`)
      this.emit('document-delete-failed', { documentId, error: error.message })
      throw error
    }
  }

  /**
   * Check RAG service health
   */
  public async checkHealth(): Promise<boolean> {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout)

      const response = await fetch(`${this.backendUrl}/api/knowledge/health`, {
        method: 'GET',
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        return false
      }

      const result = await response.json() as HealthResponse
      return result.success && result.data?.status === 'healthy'
    } catch (error: any) {
      console.error(`[RAGQuery] Health check failed: ${error.message}`)
      return false
    }
  }

  /**
   * Enhance a prompt with relevant knowledge context
   */
  public async enhancePromptWithContext(
    prompt: string, 
    contextQuery?: string,
    maxResults: number = 3
  ): Promise<string> {
    try {
      const searchQuery = contextQuery || prompt
      const results = await this.searchKnowledge(searchQuery, { 
        limit: maxResults, 
        threshold: 0.5 
      })

      if (results.length === 0) {
        return prompt // No relevant context found
      }

      const contextText = results
        .map(result => `[From ${result.filename}]: ${result.content}`)
        .join('\n\n')

      const enhancedPrompt = `Context from knowledge base:
${contextText}

User query: ${prompt}`

      console.log(`[RAGQuery] Enhanced prompt with ${results.length} knowledge chunks`)
      
      return enhancedPrompt
    } catch (error: any) {
      console.error(`[RAGQuery] Failed to enhance prompt: ${error.message}`)
      return prompt // Return original prompt if enhancement fails
    }
  }

  /**
   * Update backend URL configuration
   */
  public updateBackendUrl(url: string): void {
    this.backendUrl = url
    console.log(`[RAGQuery] Updated backend URL to: ${url}`)
  }

  /**
   * Update request timeout
   */
  public updateTimeout(timeoutMs: number): void {
    this.requestTimeout = timeoutMs
    console.log(`[RAGQuery] Updated request timeout to: ${timeoutMs}ms`)
  }

  /**
   * Cleanup method for app shutdown
   */
  public async cleanup(): Promise<void> {
    console.log('[RAGQuery] Cleaning up RAG query service...')
    this.removeAllListeners()
  }
}

export default RAGQueryHelper.getInstance()
export type { Document, SearchResult, SearchResponse, DocumentsResponse, HealthResponse }
