/**
 * ViteConnectionManager.ts
 * 
 * Manages the connection between Electron and Vite development server
 * with proper error handling, retries, and connection monitoring.
 */

import { BrowserWindow } from 'electron'
import http from 'http'

interface ConnectionOptions {
  maxRetries?: number
  retryDelay?: number
  timeout?: number
  healthCheckInterval?: number
}

interface ConnectionResult {
  success: boolean
  error?: string
  attempts: number
  totalTime: number
}

class ViteConnectionManager {
  private static instance: ViteConnectionManager
  private readonly viteUrl = 'http://localhost:5173'
  private readonly vitePort = 5173
  private isConnected = false
  private healthCheckInterval: NodeJS.Timeout | null = null
  private connectionListeners: Array<(connected: boolean) => void> = []

  private constructor() {}

  public static getInstance(): ViteConnectionManager {
    if (!ViteConnectionManager.instance) {
      ViteConnectionManager.instance = new ViteConnectionManager()
    }
    return ViteConnectionManager.instance
  }

  /**
   * Check if Vite server is available
   */
  private async checkViteServer(timeout = 2000): Promise<boolean> {
    return new Promise((resolve) => {
      const req = http.request({
        hostname: 'localhost',
        port: this.vitePort,
        method: 'GET',
        path: '/',
        timeout
      }, (res) => {
        resolve(res.statusCode === 200 || res.statusCode === 304)
      })

      req.on('error', () => resolve(false))
      req.on('timeout', () => {
        req.destroy()
        resolve(false)
      })

      req.end()
    })
  }

  /**
   * Wait for Vite server to become available
   */
  public async waitForViteServer(options: ConnectionOptions = {}): Promise<ConnectionResult> {
    const {
      maxRetries = 30,
      retryDelay = 1000,
      timeout = 2000
    } = options

    const startTime = Date.now()
    let attempts = 0

    console.log('[ViteConnection] Waiting for Vite server to become available...')

    while (attempts < maxRetries) {
      attempts++
      
      try {
        const isAvailable = await this.checkViteServer(timeout)
        
        if (isAvailable) {
          const totalTime = Date.now() - startTime
          console.log(`[ViteConnection] ✅ Vite server is available after ${attempts} attempts (${totalTime}ms)`)
          this.isConnected = true
          this.notifyConnectionListeners(true)
          return { success: true, attempts, totalTime }
        }
      } catch (error) {
        console.log(`[ViteConnection] Attempt ${attempts}/${maxRetries} failed:`, error)
      }

      if (attempts < maxRetries) {
        console.log(`[ViteConnection] Attempt ${attempts}/${maxRetries} failed, retrying in ${retryDelay}ms...`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }

    const totalTime = Date.now() - startTime
    const error = `Failed to connect to Vite server after ${attempts} attempts (${totalTime}ms)`
    console.error(`[ViteConnection] ❌ ${error}`)
    
    this.isConnected = false
    this.notifyConnectionListeners(false)
    return { success: false, error, attempts, totalTime }
  }

  /**
   * Load URL in window with connection handling
   */
  public async loadUrlInWindow(
    window: BrowserWindow, 
    options: ConnectionOptions = {}
  ): Promise<ConnectionResult> {
    // First, wait for Vite server to be available
    const connectionResult = await this.waitForViteServer(options)
    
    if (!connectionResult.success) {
      return connectionResult
    }

    return new Promise((resolve) => {
      let resolved = false
      const startTime = Date.now()

      // Set up error handling
      const handleLoadError = (event: any, errorCode: number, errorDescription: string) => {
        if (resolved) return
        resolved = true
        
        const totalTime = Date.now() - startTime
        const error = `Failed to load Vite URL: ${errorDescription} (code: ${errorCode})`
        console.error(`[ViteConnection] ❌ ${error}`)
        
        this.isConnected = false
        this.notifyConnectionListeners(false)
        resolve({ success: false, error, attempts: 1, totalTime })
      }

      // Set up success handling
      const handleLoadSuccess = () => {
        if (resolved) return
        resolved = true
        
        const totalTime = Date.now() - startTime
        console.log(`[ViteConnection] ✅ Successfully loaded Vite URL in window (${totalTime}ms)`)
        
        this.isConnected = true
        this.notifyConnectionListeners(true)
        resolve({ success: true, attempts: 1, totalTime })
      }

      // Add event listeners
      window.webContents.once('did-fail-load', handleLoadError)
      window.webContents.once('did-finish-load', handleLoadSuccess)

      // Load the URL
      console.log(`[ViteConnection] Loading ${this.viteUrl} in window...`)
      window.loadURL(this.viteUrl).catch((error) => {
        if (resolved) return
        resolved = true
        
        const totalTime = Date.now() - startTime
        console.error(`[ViteConnection] ❌ Failed to load URL:`, error)
        
        this.isConnected = false
        this.notifyConnectionListeners(false)
        resolve({ 
          success: false, 
          error: error.message || 'Unknown error loading URL', 
          attempts: 1, 
          totalTime 
        })
      })

      // Set a timeout for the load operation
      setTimeout(() => {
        if (resolved) return
        resolved = true
        
        const totalTime = Date.now() - startTime
        const error = `Timeout loading Vite URL after ${totalTime}ms`
        console.error(`[ViteConnection] ❌ ${error}`)
        
        this.isConnected = false
        this.notifyConnectionListeners(false)
        resolve({ success: false, error, attempts: 1, totalTime })
      }, options.timeout || 10000)
    })
  }

  /**
   * Start health check monitoring
   */
  public startHealthCheck(interval = 5000): void {
    if (this.healthCheckInterval) {
      this.stopHealthCheck()
    }

    console.log(`[ViteConnection] Starting health check (every ${interval}ms)`)
    
    this.healthCheckInterval = setInterval(async () => {
      const wasConnected = this.isConnected
      const isAvailable = await this.checkViteServer(1000)
      
      if (wasConnected !== isAvailable) {
        console.log(`[ViteConnection] Connection status changed: ${wasConnected} -> ${isAvailable}`)
        this.isConnected = isAvailable
        this.notifyConnectionListeners(isAvailable)
      }
    }, interval)
  }

  /**
   * Stop health check monitoring
   */
  public stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
      console.log('[ViteConnection] Health check stopped')
    }
  }

  /**
   * Add connection status listener
   */
  public onConnectionChange(listener: (connected: boolean) => void): void {
    this.connectionListeners.push(listener)
  }

  /**
   * Remove connection status listener
   */
  public removeConnectionListener(listener: (connected: boolean) => void): void {
    const index = this.connectionListeners.indexOf(listener)
    if (index > -1) {
      this.connectionListeners.splice(index, 1)
    }
  }

  /**
   * Notify all connection listeners
   */
  private notifyConnectionListeners(connected: boolean): void {
    this.connectionListeners.forEach(listener => {
      try {
        listener(connected)
      } catch (error) {
        console.error('[ViteConnection] Error in connection listener:', error)
      }
    })
  }

  /**
   * Get current connection status
   */
  public isViteConnected(): boolean {
    return this.isConnected
  }

  /**
   * Get Vite URL
   */
  public getViteUrl(): string {
    return this.viteUrl
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    this.stopHealthCheck()
    this.connectionListeners = []
    this.isConnected = false
  }
}

export default ViteConnectionManager
