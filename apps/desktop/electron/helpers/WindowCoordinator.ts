/**
 * WindowCoordinator.ts
 * 
 * Main coordinator that manages the window pool and integrates layout + movement managers.
 * Handles window visibility, positioning, and state coordination.
 */

import { BrowserWindow } from 'electron'
import { WindowLayoutManager } from './WindowLayoutManager'
import { SmoothMovementManager } from './SmoothMovementManager'
import path from 'path'

interface WindowVisibility {
  askAI?: boolean
  microphone?: boolean
  settings?: boolean
}

interface WindowCoordinatorOptions {
  isDevelopment?: boolean
}

export class WindowCoordinator {
  private windowPool: Map<string, BrowserWindow> = new Map()
  private layoutManager: WindowLayoutManager
  private movementManager: SmoothMovementManager
  private isDevelopment: boolean
  private settingsHideTimer: NodeJS.Timeout | null = null

  constructor(options: WindowCoordinatorOptions = {}) {
    this.isDevelopment = options.isDevelopment ?? false
    this.layoutManager = new WindowLayoutManager(this.windowPool)
    this.movementManager = new SmoothMovementManager(this.windowPool)
  }

  /**
   * Adds a window to the pool
   */
  public addWindow(name: string, window: BrowserWindow): void {
    this.windowPool.set(name, window)
  }

  /**
   * Gets a window from the pool
   */
  public getWindow(name: string): BrowserWindow | undefined {
    return this.windowPool.get(name)
  }

  /**
   * Removes a window from the pool
   */
  public removeWindow(name: string): void {
    const window = this.windowPool.get(name)
    if (window && !window.isDestroyed()) {
      window.destroy()
    }
    this.windowPool.delete(name)
  }

  /**
   * Creates feature windows (askAI, microphone, settings) as child windows of header
   */
  public createFeatureWindows(headerWindow: BrowserWindow): void {
    const commonOptions = {
      parent: headerWindow,
      show: false,
      frame: false,
      transparent: true,
      skipTaskbar: true,
      resizable: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload.js'),
      },
    }

    // Create Ask AI window - same size as main window expanded
    if (!this.windowPool.has('askAI')) {
      const askAIWindow = new BrowserWindow({
        ...commonOptions,
        width: 650, // Same as APP_WIDTH
        height: 700, // Same as APP_EXPANDED_HEIGHT
      })

      // Load the app with askAI feature parameter
      if (this.isDevelopment) {
        askAIWindow.loadURL('http://localhost:5173?feature=askAI')
      } else {
        askAIWindow.loadFile(path.join(__dirname, '../../index.html'), {
          query: { feature: 'askAI' }
        })
      }

      this.windowPool.set('askAI', askAIWindow)
    }

    // Create Microphone window - same size as main window inline recording
    if (!this.windowPool.has('microphone')) {
      const microphoneWindow = new BrowserWindow({
        ...commonOptions,
        width: 650, // Same as APP_WIDTH
        height: 360, // Same as APP_INLINE_RECORDING_HEIGHT
      })

      if (this.isDevelopment) {
        console.log('[WindowCoordinator] Loading microphone window URL: http://localhost:5173?feature=microphone')
        microphoneWindow.loadURL('http://localhost:5173?feature=microphone')
      } else {
        console.log('[WindowCoordinator] Loading microphone window file with query: feature=microphone')
        microphoneWindow.loadFile(path.join(__dirname, '../../index.html'), {
          query: { feature: 'microphone' }
        })
      }

      // Add event listeners for debugging
      microphoneWindow.webContents.on('did-finish-load', () => {
        console.log('[WindowCoordinator] Microphone window finished loading')
      })

      microphoneWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error('[WindowCoordinator] Microphone window failed to load:', errorCode, errorDescription)
      })

      this.windowPool.set('microphone', microphoneWindow)
    }

    // Create Settings window - same size as main window settings
    if (!this.windowPool.has('settings')) {
      const settingsWindow = new BrowserWindow({
        ...commonOptions,
        width: 650, // Same as APP_WIDTH
        height: 300, // Same as APP_SETTINGS_HEIGHT
      })

      if (this.isDevelopment) {
        settingsWindow.loadURL('http://localhost:5173?feature=settings')
      } else {
        settingsWindow.loadFile(path.join(__dirname, '../../index.html'), {
          query: { feature: 'settings' }
        })
      }

      this.windowPool.set('settings', settingsWindow)
    }
  }

  /**
   * Handles window visibility requests with coordinated positioning
   */
  public async handleWindowVisibilityRequest(
    windowName: string,
    shouldBeVisible: boolean
  ): Promise<void> {
    const window = this.windowPool.get(windowName)

    if (!window || window.isDestroyed()) {
      console.warn(`[WindowCoordinator] Window '${windowName}' not found or destroyed`)
      return
    }

    const isCurrentlyVisible = window.isVisible()

    // For feature windows, toggle visibility if already visible
    if (isCurrentlyVisible && shouldBeVisible && windowName !== 'settings') {
      console.log(`[WindowCoordinator] Toggling visibility for '${windowName}' (currently visible)`)
      shouldBeVisible = false
    }

    if (isCurrentlyVisible === shouldBeVisible) {
      console.log(`[WindowCoordinator] Window '${windowName}' already in desired state`)
      return
    }

    // Special handling for settings window (with delay)
    if (windowName === 'settings') {
      if (shouldBeVisible) {
        // Cancel any pending hide operations
        if (this.settingsHideTimer) {
          clearTimeout(this.settingsHideTimer)
          this.settingsHideTimer = null
        }
        await this.showWindow('settings')
      } else {
        // Hide after a delay
        if (this.settingsHideTimer) {
          clearTimeout(this.settingsHideTimer)
        }
        this.settingsHideTimer = setTimeout(() => {
          this.hideWindow('settings')
          this.settingsHideTimer = null
        }, 200)
      }
      return
    }

    // Handle other windows immediately
    if (shouldBeVisible) {
      await this.showWindow(windowName)
    } else {
      await this.hideWindow(windowName)
    }
  }

  /**
   * Shows a window with coordinated positioning
   */
  private async showWindow(windowName: string): Promise<void> {
    const window = this.windowPool.get(windowName)
    if (!window || window.isDestroyed()) return

    // Calculate current visibility state
    const currentVisibility = this.getCurrentVisibility()
    const newVisibility = { ...currentVisibility, [windowName]: true }

    // Calculate new layout
    const newLayout = this.layoutManager.calculateFeatureWindowLayout(newVisibility)
    const targetBounds = newLayout[windowName]

    if (!targetBounds) return

    // Show window with fade-in animation
    console.log(`[WindowCoordinator] Showing window '${windowName}' at bounds:`, targetBounds)
    window.setOpacity(0)
    window.setBounds(targetBounds)
    window.show()
    console.log(`[WindowCoordinator] Window '${windowName}' show() called, isVisible: ${window.isVisible()}`)

    // Animate fade-in and coordinate with other windows
    this.movementManager.fade(window, { to: 1 })
    this.movementManager.animateLayout(newLayout)
    console.log(`[WindowCoordinator] Window '${windowName}' fade-in started`)
  }

  /**
   * Hides a window with coordinated repositioning of remaining windows
   */
  private async hideWindow(windowName: string): Promise<void> {
    const window = this.windowPool.get(windowName)
    if (!window || window.isDestroyed() || !window.isVisible()) return

    // Calculate new visibility state
    const currentVisibility = this.getCurrentVisibility()
    const newVisibility = { ...currentVisibility, [windowName]: false }

    // Calculate layout for remaining windows
    const newLayout = this.layoutManager.calculateFeatureWindowLayout(newVisibility)

    // Fade out the window
    this.movementManager.fade(window, { 
      to: 0, 
      onComplete: () => window.hide() 
    })

    // Rearrange remaining windows
    this.movementManager.animateLayout(newLayout)
  }

  /**
   * Gets current visibility state of all feature windows
   */
  private getCurrentVisibility(): WindowVisibility {
    return {
      askAI: this.isWindowVisible('askAI'),
      microphone: this.isWindowVisible('microphone'),
      settings: this.isWindowVisible('settings')
    }
  }

  /**
   * Checks if a window is currently visible
   */
  private isWindowVisible(windowName: string): boolean {
    const window = this.windowPool.get(windowName)
    return !!(window && !window.isDestroyed() && window.isVisible())
  }

  /**
   * Updates layout when header window moves
   */
  public updateChildWindowLayouts(animated = true): void {
    if (this.movementManager.isCurrentlyAnimating) return

    const currentVisibility = this.getCurrentVisibility()
    const hasVisibleWindows = Object.values(currentVisibility).some(visible => visible)

    if (!hasVisibleWindows) return

    const newLayout = this.layoutManager.calculateFeatureWindowLayout(currentVisibility)
    this.movementManager.animateLayout(newLayout, animated)
  }

  /**
   * Destroys all feature windows
   */
  public destroyFeatureWindows(): void {
    const featureWindows = ['askAI', 'microphone', 'settings']
    
    // Clear any pending timers
    if (this.settingsHideTimer) {
      clearTimeout(this.settingsHideTimer)
      this.settingsHideTimer = null
    }

    featureWindows.forEach(name => {
      this.removeWindow(name)
    })
  }

  /**
   * Cleans up all resources
   */
  public destroy(): void {
    this.movementManager.destroy()
    this.destroyFeatureWindows()
    
    if (this.settingsHideTimer) {
      clearTimeout(this.settingsHideTimer)
      this.settingsHideTimer = null
    }

    console.log('[WindowCoordinator] Destroyed')
  }
}
