/**
 * Agent Integration Service
 * Provides sophisticated AI agent capabilities to the desktop application
 * Integrates with backend agent endpoints for memory, tools, and planning
 */

import { logger } from '../utils/logger'
import { CallContext } from '../types/CallContext'

export interface AgentState {
  conversationId: string
  userId: string
  context: CallContext
  memory: ConversationMemory
  currentGoal?: string
  planningSteps?: PlanningStep[]
  toolResults?: ToolResult[]
  lastUpdated: Date
  createdAt: Date
}

export interface ConversationMemory {
  shortTerm: Message[]
  workingMemory: WorkingMemoryItem[]
  longTermSummary?: string
  userPreferences?: UserPreferences
}

export interface Message {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  metadata?: any
}

export interface WorkingMemoryItem {
  id: string
  content: string
  timestamp: Date
  type: 'context' | 'goal' | 'tool_result' | 'insight' | 'working'
  metadata?: any
}

export interface UserPreferences {
  communicationStyle?: string
  preferredTools?: string[]
  responseLength?: 'brief' | 'detailed' | 'comprehensive'
  focusAreas?: string[]
}

export interface PlanningStep {
  id: string
  description: string
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  dependencies?: string[]
  estimatedDuration?: number
  metadata?: any
}

export interface ToolResult {
  toolName: string
  parameters: any
  result: any
  success: boolean
  timestamp: Date
  executionTime: number
  metadata?: any
}

export interface AgentGenerationOptions {
  prompt: string
  conversationId: string
  userId: string
  context: CallContext
  useAgent?: boolean
  useRAG?: boolean
  enableToolCalling?: boolean
  enablePlanning?: boolean
  enableReasoning?: boolean
  enableProactiveSuggestions?: boolean
  maxToolCalls?: number
  reasoningPattern?: 'react' | 'cot' | 'tot'
  assistanceType?: string
}

export interface AgentResponse {
  text: string
  contextUsed: boolean
  sources: Array<{
    filename: string
    content: string
    similarity: number
  }>
  toolsUsed: ToolResult[]
  planningSteps?: PlanningStep[]
  proactiveSuggestions?: string[]
  agentState: AgentState
  metadata: {
    responseTime: number
    tokensUsed: number
    confidence: number
    reasoning?: string
  }
}

export interface AvailableTool {
  name: string
  description: string
  category: string
  parameters: any
  permissions: string[]
}

export class AgentIntegrationService {
  private baseUrl: string
  private authToken: string
  private conversationStates: Map<string, AgentState> = new Map()

  constructor(baseUrl: string = 'http://localhost:4000', authToken: string = '') {
    this.baseUrl = baseUrl
    this.authToken = authToken
    logger.info('[AgentIntegration] Service initialized')
  }

  /**
   * Set authentication token for API requests
   */
  setAuthToken(token: string): void {
    this.authToken = token
    logger.info('[AgentIntegration] Auth token updated')
  }

  /**
   * Generate response using full agent capabilities
   */
  async generateWithAgent(options: AgentGenerationOptions): Promise<AgentResponse> {
    try {
      logger.info(`[AgentIntegration] Generating with agent for conversation: ${options.conversationId}`)

      const response = await fetch(`${this.baseUrl}/api/v1/assist/agent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
          'X-Request-ID': `agent-${Date.now()}`
        },
        body: JSON.stringify({
          ...options,
          useAgent: true,
          enableToolCalling: options.enableToolCalling ?? true,
          enablePlanning: options.enablePlanning ?? false,
          enableReasoning: options.enableReasoning ?? false,
          enableProactiveSuggestions: options.enableProactiveSuggestions ?? true
        })
      })

      if (!response.ok) {
        throw new Error(`Agent API request failed: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      // Cache agent state locally
      if (data.agentState) {
        this.conversationStates.set(options.conversationId, data.agentState)
      }

      logger.info(`[AgentIntegration] Agent response generated successfully`)
      return data
    } catch (error: any) {
      logger.error(`[AgentIntegration] Error generating with agent: ${error.message}`)
      throw error
    }
  }

  /**
   * Get agent state for a conversation
   */
  async getAgentState(conversationId: string): Promise<AgentState | null> {
    try {
      // Check local cache first
      const cached = this.conversationStates.get(conversationId)
      if (cached) {
        return cached
      }

      // Fetch from backend
      const response = await fetch(`${this.baseUrl}/api/v1/assist/agent/${conversationId}/state`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      })

      if (response.status === 404) {
        return null
      }

      if (!response.ok) {
        throw new Error(`Failed to get agent state: ${response.status}`)
      }

      const agentState = await response.json()
      
      // Cache locally
      this.conversationStates.set(conversationId, agentState)
      
      return agentState
    } catch (error: any) {
      logger.error(`[AgentIntegration] Error getting agent state: ${error.message}`)
      return null
    }
  }

  /**
   * Update agent goal for a conversation
   */
  async updateAgentGoal(conversationId: string, goal: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/assist/agent/${conversationId}/goal`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({ goal })
      })

      if (!response.ok) {
        throw new Error(`Failed to update agent goal: ${response.status}`)
      }

      // Update local cache
      const cached = this.conversationStates.get(conversationId)
      if (cached) {
        cached.currentGoal = goal
        cached.lastUpdated = new Date()
      }

      logger.info(`[AgentIntegration] Updated goal for conversation: ${conversationId}`)
    } catch (error: any) {
      logger.error(`[AgentIntegration] Error updating agent goal: ${error.message}`)
      throw error
    }
  }

  /**
   * Get available tools for the current user
   */
  async getAvailableTools(): Promise<AvailableTool[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/assist/agent/tools`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to get available tools: ${response.status}`)
      }

      return await response.json()
    } catch (error: any) {
      logger.error(`[AgentIntegration] Error getting available tools: ${error.message}`)
      return []
    }
  }

  /**
   * Clear agent memory for a conversation
   */
  async clearAgentMemory(conversationId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/assist/agent/${conversationId}/memory`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to clear agent memory: ${response.status}`)
      }

      // Clear local cache
      this.conversationStates.delete(conversationId)

      logger.info(`[AgentIntegration] Cleared memory for conversation: ${conversationId}`)
    } catch (error: any) {
      logger.error(`[AgentIntegration] Error clearing agent memory: ${error.message}`)
      throw error
    }
  }

  /**
   * Get conversation memory summary
   */
  getMemorySummary(conversationId: string): string | null {
    const state = this.conversationStates.get(conversationId)
    if (!state) return null

    const recentMessages = state.memory.shortTerm.slice(-5)
    const workingMemory = state.memory.workingMemory.slice(-3)
    
    let summary = 'Recent conversation:\n'
    recentMessages.forEach(msg => {
      summary += `${msg.role}: ${msg.content.substring(0, 100)}...\n`
    })

    if (workingMemory.length > 0) {
      summary += '\nWorking memory:\n'
      workingMemory.forEach(item => {
        summary += `- ${item.type}: ${item.content.substring(0, 80)}...\n`
      })
    }

    if (state.currentGoal) {
      summary += `\nCurrent goal: ${state.currentGoal}`
    }

    return summary
  }

  /**
   * Check if agent capabilities are available
   */
  async isAgentAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/assist/health`)
      return response.ok
    } catch {
      return false
    }
  }

  /**
   * Get cached conversation state
   */
  getCachedState(conversationId: string): AgentState | null {
    return this.conversationStates.get(conversationId) || null
  }

  /**
   * Clear all cached states
   */
  clearCache(): void {
    this.conversationStates.clear()
    logger.info('[AgentIntegration] Cache cleared')
  }
}
