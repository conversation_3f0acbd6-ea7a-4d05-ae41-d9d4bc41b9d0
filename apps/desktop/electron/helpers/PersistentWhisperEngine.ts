/**
 * PersistentWhisperEngine.ts
 *
 * UPGRADED: Now implements TRUE PROCESS POOLING for concurrent whisper-cli processing.
 * Uses multiple worker processes to achieve parallel transcription and reduced queue wait times.
 *
 * Current behavior:
 * - Maintains pool of concurrent whisper-cli processes (default: 3 workers)
 * - Each worker still incurs ~878ms model loading overhead per request
 * - Provides significant performance improvement through parallelization
 * - Intelligent request routing to available workers
 * - Comprehensive worker health monitoring and management
 *
 * Performance improvements:
 * - Concurrent processing: Multiple requests processed simultaneously
 * - Reduced queue wait times: Requests don't wait for single process
 * - Better resource utilization: Uses multiple CPU cores effectively
 * - Higher throughput: Significantly improved requests/second
 *
 * Key features:
 * - True process pooling with multiple workers
 * - Intelligent request routing and load balancing
 * - Worker health monitoring and automatic replacement
 * - Comprehensive performance metrics and monitoring
 * - Configurable pool size for performance tuning
 * - Thread-safe concurrent operation
 */

import { EventEmitter } from 'events'
import { spawn, ChildProcess } from 'child_process'
import { promises as fs } from 'fs'
import * as path from 'path'
import * as os from 'os'
import { v4 as uuidv4 } from 'uuid'
import { ProcessPool, ProcessPoolOptions, PoolStats } from './WhisperProcessPool'

export interface PersistentWhisperOptions {
  modelSize?: 'tiny.en' | 'base.en' | 'small.en'
  language?: string
  maxConcurrentRequests?: number // Deprecated: now controlled by poolSize
  processTimeout?: number // Timeout for pool monitoring
  restartThreshold?: number
  requestTimeout?: number // Timeout for individual transcription requests
  poolSize?: number // Number of worker processes in the pool (default: 3)
}

export interface TranscriptionRequest {
  id: string
  audioFilePath: string
  resolve: (result: TranscriptionResult | null) => void
  reject: (error: Error) => void
  timestamp: number
}

export interface TranscriptionResult {
  success: boolean
  text: string
  segments?: any[]
  duration?: number
  processingTime?: number
}

export interface EngineStatus {
  isRunning: boolean
  isReady: boolean
  processId?: number
  modelLoaded: boolean
  requestsProcessed: number
  averageLatency: number
  lastError?: string
  // Process pool specific status
  poolStats?: PoolStats
  workerCount?: number
  availableWorkers?: number
  poolUtilization?: number
}

/**
 * Process Pool Whisper Engine (Singleton)
 *
 * This class manages a pool of whisper-cli worker processes for concurrent transcription.
 * Provides significant performance improvements through parallel processing.
 * Implements singleton pattern to prevent multiple instances and resource conflicts.
 */
export class PersistentWhisperEngine extends EventEmitter {
  private static instance: PersistentWhisperEngine | null = null
  private static isInitializing = false

  private options: Required<PersistentWhisperOptions & { requestTimeout: number; poolSize: number }>
  private processPool: ProcessPool | null = null
  private isInitialized = false
  private isReady = false
  private modelCache: any = null

  // Performance tracking (aggregated from pool)
  private lastError: string | undefined = undefined

  // Process management
  private processStartTime = 0
  private lastActivity = 0
  private activityMonitorInterval: NodeJS.Timeout | null = null

  private constructor(options: PersistentWhisperOptions = {}) {
    super()

    this.options = {
      modelSize: options.modelSize || 'base.en', // Upgraded default from tiny.en for better accuracy
      language: options.language || 'en',
      maxConcurrentRequests: options.maxConcurrentRequests || 10, // Deprecated but kept for compatibility
      processTimeout: options.processTimeout || 30000, // 30 seconds for pool monitoring
      restartThreshold: options.restartThreshold || 5, // Restart after 5 failures
      requestTimeout: options.requestTimeout || 2000, // 2 seconds for individual requests (878ms model load + transcription)
      poolSize: options.poolSize || 3 // Default pool size of 3 workers
    }

    console.log('[PersistentWhisperEngine] Singleton initialized with process pool, options:', this.options)
  }

  /**
   * Get the singleton instance of PersistentWhisperEngine
   */
  public static getInstance(options?: PersistentWhisperOptions): PersistentWhisperEngine {
    if (!PersistentWhisperEngine.instance) {
      console.log('[PersistentWhisperEngine] Creating singleton instance...')
      PersistentWhisperEngine.instance = new PersistentWhisperEngine(options)
    } else if (options) {
      console.log('[PersistentWhisperEngine] Singleton instance already exists, ignoring new options')
    }
    return PersistentWhisperEngine.instance
  }

  /**
   * Initialize the singleton instance if not already initialized
   */
  public static async initializeSingleton(options?: PersistentWhisperOptions): Promise<PersistentWhisperEngine> {
    if (PersistentWhisperEngine.isInitializing) {
      console.log('[PersistentWhisperEngine] Initialization already in progress, waiting...')
      // Wait for initialization to complete
      while (PersistentWhisperEngine.isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return PersistentWhisperEngine.getInstance()
    }

    const instance = PersistentWhisperEngine.getInstance(options)

    if (!instance.isInitialized) {
      PersistentWhisperEngine.isInitializing = true
      try {
        console.log('[PersistentWhisperEngine] Initializing singleton instance...')
        await instance.initialize()
        console.log('[PersistentWhisperEngine] Singleton initialization completed')
      } finally {
        PersistentWhisperEngine.isInitializing = false
      }
    }

    return instance
  }

  /**
   * Reset the singleton instance (for testing or cleanup)
   */
  public static async resetSingleton(): Promise<void> {
    if (PersistentWhisperEngine.instance) {
      console.log('[PersistentWhisperEngine] Resetting singleton instance...')
      await PersistentWhisperEngine.instance.shutdown()
      PersistentWhisperEngine.instance = null
      PersistentWhisperEngine.isInitializing = false
    }
  }

  /**
   * Initialize the persistent whisper engine
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      console.log('[PersistentWhisperEngine] Already initialized')
      return true
    }

    try {
      console.log('[PersistentWhisperEngine] Starting initialization...')

      // Initialize model cache
      await this.initializeModelCache()

      if (!this.modelCache) {
        throw new Error('Failed to initialize model cache')
      }

      // Start the process pool
      await this.startProcessPool()

      // Start activity monitoring
      this.startActivityMonitoring()

      this.isInitialized = true
      console.log('[PersistentWhisperEngine] Initialization completed successfully')
      this.emit('initialized')

      return true
    } catch (error) {
      console.error('[PersistentWhisperEngine] Initialization failed:', error)
      this.emit('error', error)
      return false
    }
  }

  /**
   * Check if the engine is ready for processing
   */
  public isEngineReady(): boolean {
    return this.isInitialized && this.isReady && this.processPool !== null
  }

  /**
   * Initialize model cache with paths and validation
   */
  private async initializeModelCache(): Promise<void> {
    try {
      console.log('[PersistentWhisperEngine] Starting model cache initialization...')
      console.log('[PersistentWhisperEngine] Current working directory:', process.cwd())
      console.log('[PersistentWhisperEngine] __dirname:', __dirname)

      // Try homebrew whisper-cpp first, then fallback to nodejs-whisper
      const homebrewWhisperCliPath = '/opt/homebrew/Cellar/whisper-cpp/1.7.6/bin/whisper-cli'
      const homebrewModelPath = `/opt/homebrew/share/whisper-cpp/ggml-${this.options.modelSize}.bin`

      let whisperCppDir: string | null = null
      let whisperCliPath: string | null = null
      let modelPath: string | null = null

      // First try homebrew installation
      if (await this.fileExists(homebrewWhisperCliPath) && await this.fileExists(homebrewModelPath)) {
        whisperCppDir = '/opt/homebrew/share/whisper-cpp'
        whisperCliPath = homebrewWhisperCliPath
        modelPath = homebrewModelPath
        console.log(`[PersistentWhisperEngine] Found homebrew whisper installation`)
      } else {
        // Fallback to nodejs-whisper installation
        const possiblePaths = [
          // Standard nodejs-whisper installation
          path.resolve(process.cwd(), '../..', 'node_modules', 'nodejs-whisper', 'cpp', 'whisper.cpp'),
          // Local installation
          path.resolve(process.cwd(), 'node_modules', 'nodejs-whisper', 'cpp', 'whisper.cpp'),
          // Desktop app specific
          path.resolve(__dirname, '../../..', 'node_modules', 'nodejs-whisper', 'cpp', 'whisper.cpp'),
          // Alternative path
          path.resolve(__dirname, '../../../..', 'node_modules', 'nodejs-whisper', 'cpp', 'whisper.cpp'),
          // Direct from apps/desktop
          path.resolve(__dirname, '../../../', 'node_modules', 'nodejs-whisper', 'cpp', 'whisper.cpp')
        ]

        for (const testPath of possiblePaths) {
          const testCliPath = path.join(testPath, 'build', 'bin', 'whisper-cli')
          const testModelPath = path.join(testPath, 'models', `ggml-${this.options.modelSize}.bin`)

          console.log(`[PersistentWhisperEngine] Testing nodejs-whisper path: ${testPath}`)

          if (await this.fileExists(testCliPath) && await this.fileExists(testModelPath)) {
            whisperCppDir = testPath
            whisperCliPath = testCliPath
            modelPath = testModelPath
            console.log(`[PersistentWhisperEngine] Found valid nodejs-whisper installation at: ${testPath}`)
            break
          } else {
            console.log(`[PersistentWhisperEngine] nodejs-whisper path not valid: ${testPath}`)
          }
        }
      }

      if (!whisperCppDir || !whisperCliPath || !modelPath) {
        const errorMsg = `Could not find whisper-cli or model. Tried homebrew and nodejs-whisper installations.`
        console.warn('[PersistentWhisperEngine]', errorMsg)
        console.warn('[PersistentWhisperEngine] Voice transcription will be disabled. To enable it, install via homebrew: brew install whisper-cpp')

        // Set a flag to indicate whisper is not available
        this.modelCache = {
          isLoaded: false,
          modelPath: null,
          whisperCliPath: null,
          whisperCppDir: null,
          lastUsed: Date.now(),
          whisperUnavailable: true
        }
        return // Don't throw error, just continue without whisper
      }

      this.modelCache = {
        isLoaded: false,
        modelPath,
        whisperCliPath,
        whisperCppDir,
        lastUsed: Date.now()
      }

      console.log('[PersistentWhisperEngine] Model cache initialized successfully:', {
        modelPath: this.modelCache.modelPath,
        whisperCliPath: this.modelCache.whisperCliPath,
        whisperCppDir: this.modelCache.whisperCppDir
      })
    } catch (error) {
      console.error('[PersistentWhisperEngine] Failed to initialize model cache:', error)
      throw error
    }
  }

  /**
   * Initialize the process pool with multiple workers
   */
  private async startProcessPool(): Promise<void> {
    try {
      console.log(`[PersistentWhisperEngine] Initializing process pool with ${this.options.poolSize} workers...`)

      this.processPool = new ProcessPool({
        poolSize: this.options.poolSize,
        whisperCliPath: this.modelCache.whisperCliPath,
        modelPath: this.modelCache.modelPath,
        whisperCppDir: this.modelCache.whisperCppDir,
        language: this.options.language,
        requestTimeout: this.options.requestTimeout
      })

      // Listen for pool events
      this.processPool.on('initialized', () => {
        console.log('[PersistentWhisperEngine] Process pool initialized successfully')
      })

      this.processPool.on('warmedUp', (warmUpInfo) => {
        console.log(`[PersistentWhisperEngine] Pool warmed up: ${warmUpInfo.successCount}/${warmUpInfo.totalWorkers} workers in ${warmUpInfo.totalTime}ms`)

        // Mark as ready only after warm-up
        this.isReady = true
        this.processStartTime = Date.now()
        this.lastActivity = Date.now()

        console.log(`[PersistentWhisperEngine] Engine ready with ${warmUpInfo.successCount} warm workers`)
        this.emit('ready')
      })

      this.processPool.on('warmUpFailed', () => {
        console.error('[PersistentWhisperEngine] Pool warm-up failed - no workers available')
        this.lastError = 'Pool warm-up failed'
        this.emit('error', new Error('Pool warm-up failed'))
      })

      await this.processPool.initialize()

      // Note: isReady will be set to true only after warm-up completes
      console.log(`[PersistentWhisperEngine] Process pool initializing with ${this.options.poolSize} workers...`)

    } catch (error) {
      console.error('[PersistentWhisperEngine] Failed to initialize process pool:', error)
      throw error
    }
  }

  /**
   * Transcribe audio file using the process pool
   */
  async transcribe(audioFilePath: string): Promise<TranscriptionResult | null> {
    if (!this.isInitialized || !this.isReady || !this.processPool) {
      throw new Error('PersistentWhisperEngine not initialized or ready')
    }

    try {
      console.log(`[PersistentWhisperEngine] Submitting transcription request for: ${audioFilePath}`)
      this.lastActivity = Date.now()

      const result = await this.processPool.processRequest(audioFilePath)

      if (result) {
        console.log(`[PersistentWhisperEngine] Transcription completed successfully`)
        return result
      } else {
        console.warn(`[PersistentWhisperEngine] Transcription failed - no result`)
        return null
      }
    } catch (error) {
      console.error(`[PersistentWhisperEngine] Transcription failed:`, error)
      this.lastError = `Transcription failed: ${error instanceof Error ? error.message : String(error)}`
      throw error
    }
  }

  // Note: processQueue method removed - now handled by ProcessPool

  // Note: executeWhisperCli, parseWhisperOutput, and updatePerformanceMetrics methods
  // removed - now handled by ProcessWorker in ProcessPool

  /**
   * Get current engine status including process pool statistics
   */
  getStatus(): EngineStatus {
    const poolStats = this.processPool?.getStatus()

    return {
      isRunning: this.processPool !== null && this.isReady,
      isReady: this.isReady,
      processId: undefined, // Multiple processes in pool
      modelLoaded: false, // Models are loaded fresh for each request in workers
      requestsProcessed: poolStats?.totalRequestsProcessed || 0,
      averageLatency: poolStats?.averageLatency || 0,
      lastError: this.lastError,
      // Process pool specific status
      poolStats,
      workerCount: poolStats?.totalWorkers || 0,
      availableWorkers: poolStats?.availableWorkers || 0,
      poolUtilization: poolStats?.poolUtilization || 0
    }
  }

  /**
   * Start activity monitoring for the process pool
   */
  private startActivityMonitoring(): void {
    this.activityMonitorInterval = setInterval(() => {
      const now = Date.now()

      // Check for excessive inactivity
      if (this.lastActivity > 0 && (now - this.lastActivity) > this.options.processTimeout) {
        console.info('[PersistentWhisperEngine] No recent activity - process pool may be idle')
      }

      // Log pool status periodically
      if (this.processPool) {
        const poolStats = this.processPool.getStatus()
        console.log(`[PersistentWhisperEngine] Pool status: ${poolStats.warmWorkers}/${poolStats.totalWorkers} warm, ${poolStats.availableWorkers} available, ${poolStats.poolUtilization.toFixed(1)}% utilization`)
      }
    }, 30000) // Check every 30 seconds
  }

  /**
   * Restart the process pool
   */
  async restart(): Promise<boolean> {
    console.log('[PersistentWhisperEngine] Restarting process pool...')

    try {
      await this.shutdown()
      this.lastError = undefined

      return await this.initialize()
    } catch (error) {
      console.error('[PersistentWhisperEngine] Restart failed:', error)
      this.lastError = `Restart failed: ${error instanceof Error ? error.message : String(error)}`
      return false
    }
  }

  /**
   * Shutdown the process pool
   */
  async shutdown(): Promise<void> {
    console.log('[PersistentWhisperEngine] Shutting down process pool...')

    this.isReady = false
    this.isInitialized = false

    // Clear activity monitoring
    if (this.activityMonitorInterval) {
      clearInterval(this.activityMonitorInterval)
      this.activityMonitorInterval = null
    }

    // Shutdown process pool
    if (this.processPool) {
      await this.processPool.shutdown()
      this.processPool = null
    }

    this.emit('shutdown')
    console.log('[PersistentWhisperEngine] Process pool shutdown completed')
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get process pool queue status
   */
  getQueueStatus() {
    if (!this.processPool) {
      return { queueLength: 0, queuedRequests: [] }
    }
    return this.processPool.getQueueStatus()
  }

  /**
   * Get detailed worker statuses
   */
  getWorkerStatuses() {
    if (!this.processPool) {
      return []
    }
    return this.processPool.getWorkerStatuses()
  }

  /**
   * Clear the process pool queue (if any)
   */
  clearQueue(): void {
    // Note: ProcessPool handles its own queue management
    // This method is kept for API compatibility
    console.log('[PersistentWhisperEngine] Queue clearing handled by ProcessPool')
  }
}
