/**
 * WindowHelper.ts
 *
 * Manages the overlay window for the Closezly desktop application.
 * Handles window creation, visibility, positioning, sizing, and transparency.
 */

import { BrowserWindow, screen } from 'electron'
import path from 'path'
import AppState from './AppState'
import { WindowCoordinator } from './WindowCoordinator'

// Define constants for window dimensions
export const COMPACT_WINDOW_HEIGHT = 40; // px, for header only
export const EXPANDED_WINDOW_HEIGHT = 700; // px, for header + body
export const WINDOW_WIDTH = 650; // px, minimum width to ensure all elements are visible
// APP_SETTINGS_HEIGHT will be EXPANDED_WINDOW_HEIGHT for this feature
export const APP_SETTINGS_HEIGHT = EXPANDED_WINDOW_HEIGHT;

interface WindowPosition {
  x: number
  y: number
}

interface WindowSize {
  width: number
  height: number
}

class WindowHelper {
  private static instance: WindowHelper
  private mainWindow: BrowserWindow | null = null
  private isDevelopment = process.env.NODE_ENV !== 'production'
  private isManuallyPositioned: boolean = false
  private isHoverExpanded: boolean = false;
  private baseHeight: number = COMPACT_WINDOW_HEIGHT; // Initialize with compact height
  private contentProtectionEnabled: boolean = true // Default ON as requested
  private randomWindowTitles: string[] = [
    'System Monitor',
    'Audio Settings',
    'Network Monitor',
    'Performance Monitor',
    'Display Settings',
    'Security Center'
  ]
  // Multi-window system (always enabled by default)
  private windowCoordinator: WindowCoordinator | null = null
  private multiWindowEnabled: boolean = true // Always use multi-window mode
  // Commented out unused property - will be used in future implementation
  // private previousHeightBeforeHover: number | null = null;

  private constructor() {}

  public static getInstance(): WindowHelper {
    if (!WindowHelper.instance) {
      WindowHelper.instance = new WindowHelper()
    }
    return WindowHelper.instance
  }

  public createMainWindow(): BrowserWindow {
    // Get the primary display dimensions
    const primaryDisplay = screen.getPrimaryDisplay()
    const { width } = primaryDisplay.workAreaSize
    // const { height } = primaryDisplay.workAreaSize // Unused

    // Get the bounds of the primary display (includes menu bar area)
    // const displayBounds = primaryDisplay.bounds // Unused

    // Calculate top center position
    const initialWindowHeight = COMPACT_WINDOW_HEIGHT; // Use compact height initially
    const centerX = Math.floor((width - WINDOW_WIDTH) / 2)

    // Use workArea.y to position the window at the top visible edge (below menu bar)
    // See: https://github.com/electron/electron/blob/main/docs/breaking-changes.md#_snippet_123
    const topY = primaryDisplay.workArea.y

    // Debug logging for display geometry
    console.log('[WindowHelper] primaryDisplay.workArea:', primaryDisplay.workArea)
    console.log('[WindowHelper] primaryDisplay.bounds:', primaryDisplay.bounds)
    console.log('[WindowHelper] Calculated topY:', topY)

    // Create the browser window
    this.mainWindow = new BrowserWindow({
      width: WINDOW_WIDTH,
      height: initialWindowHeight, // Use initial compact height
      x: centerX, // Center horizontally
      y: topY, // Position at the top visible edge (below menu bar)
      frame: false, // No window frame
      transparent: true, // Transparent background
      alwaysOnTop: true, // Always on top of other windows
      skipTaskbar: true, // Don't show in taskbar
      hiddenInMissionControl: true, // Add this for macOS stealth
      resizable: false,
      hasShadow: false, // Add this to reduce visibility
      webPreferences: {
        preload: path.join(__dirname, '../preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        backgroundThrottling: false, // Add this for consistent performance
        webSecurity: true,
        sandbox: true
      },
      backgroundColor: '#00000000', // Add transparent background
    })

    // CRITICAL: Apply content protection immediately after creation
    this.mainWindow.setContentProtection(true)
    this.mainWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true })

    // Additional stealth measures from Cheating Daddy
    try {
      // Force content protection multiple times for reliability
      this.mainWindow.once('ready-to-show', () => {
        this.mainWindow?.setContentProtection(true)
        console.log('[WindowHelper] Content protection re-applied on ready-to-show')
      })

      this.mainWindow.webContents.once('did-finish-load', () => {
        this.mainWindow?.setContentProtection(true)
        console.log('[WindowHelper] Content protection re-applied on did-finish-load')
      })
    } catch (error) {
      console.warn('[WindowHelper] Could not set additional content protection:', error)
    }

    // Platform-specific enhancements from Cheating Daddy
    if (process.platform === 'win32') {
      this.mainWindow.setAlwaysOnTop(true, 'screen-saver', 1)
    }

    // Apply additional stealth measures
    this.applyStealthMeasures()

    // Set the window to be non-focusable in production
    // This allows clicks to pass through to the window underneath
    if (!this.isDevelopment) {
      this.mainWindow.setIgnoreMouseEvents(true, { forward: true })
    }

    // Load the app
    if (this.isDevelopment) {
      // In development, load from the dev server
      this.mainWindow.loadURL('http://localhost:5173')

      // Open DevTools in development after the window has finished loading
      this.mainWindow.webContents.once('did-finish-load', () => {
        if (this.mainWindow) {
          this.mainWindow.webContents.openDevTools({ mode: 'detach' })
        }
      })
    } else {
      // In production, load from the built files
      this.mainWindow.loadFile(path.join(__dirname, '../../index.html'))
    }

    // Update AppState with the main window reference
    AppState.getInstance().setMainWindow(this.mainWindow)

    // Set initial visibility based on AppState
    this.setVisibility(AppState.getInstance().isOverlayVisible())

    // Initialize multi-window mode automatically
    console.log('[WindowHelper] About to initialize multi-window mode')
    this.enableMultiWindowMode()
    console.log('[WindowHelper] Multi-window mode initialization complete')

    // Handle window close
    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })

    // AGGRESSIVE HIDE ON BLUR (DIAGNOSTIC)
    this.mainWindow.on('blur', () => {
      if (this.mainWindow && !AppState.getInstance().isOverlayVisible() && !this.mainWindow.webContents.isDevToolsFocused()) {
        console.log('[WindowHelper DEBUG] Window blurred while AppState.isOverlayVisible is false. Forcing hide.');
        this.mainWindow.hide();
        this.mainWindow.setFocusable(false);
      }
    });

    return this.mainWindow
  }

  // New method: Apply stealth measures (from Cheating Daddy stealthFeatures.js)
  private applyStealthMeasures(): void {
    if (!this.mainWindow) return

    try {
      // Hide from Mission Control on macOS
      if (process.platform === 'darwin') {
        this.mainWindow.setHiddenInMissionControl(true)

        // Set random app name for additional stealth
        const { app } = require('electron')
        const randomName = this.randomWindowTitles[Math.floor(Math.random() * this.randomWindowTitles.length)]
        app.setName(randomName)

        // Hide window buttons for additional stealth
        this.mainWindow.setWindowButtonVisibility(false)
      }

      // Set random window title for additional stealth
      const randomTitle = this.randomWindowTitles[Math.floor(Math.random() * this.randomWindowTitles.length)]
      this.mainWindow.setTitle(randomTitle)

      // Randomize user agent
      const userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      ]
      const randomUA = userAgents[Math.floor(Math.random() * userAgents.length)]
      this.mainWindow.webContents.setUserAgent(randomUA)

      // Additional stealth measures for better invisibility
      if (process.platform === 'darwin') {
        // Try to make window as invisible as possible to screen capture
        this.mainWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true })
        this.mainWindow.setFullScreenable(false)

        // Set window level to maximum
        try {
          // @ts-ignore - Using undocumented API for maximum stealth
          this.mainWindow.setWindowLevel && this.mainWindow.setWindowLevel(3)
        } catch (e) {
          console.warn('[WindowHelper] Could not set window level:', e)
        }
      }

      // Force content protection again after all setup
      setTimeout(() => {
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.setContentProtection(true)
          console.log('[WindowHelper] Final content protection enforcement applied')
        }
      }, 1000)

      console.log('[WindowHelper] Enhanced stealth measures applied')
    } catch (error) {
      console.warn('[WindowHelper] Could not apply all stealth measures:', error)
    }
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  // Content Protection Management - Based on Glass Pattern
  public setContentProtection(enabled: boolean): boolean {
    if (!this.mainWindow) return false

    try {
      // Check platform support
      if (process.platform === 'darwin' || process.platform === 'win32') {
        this.mainWindow.setContentProtection(enabled)
        this.contentProtectionEnabled = enabled

        // Update AppState
        AppState.getInstance().setUndetectableMode(enabled)

        console.log(`[WindowHelper] Content protection ${enabled ? 'enabled' : 'disabled'}`)
        return true
      } else {
        console.warn('[WindowHelper] Content protection not supported on this platform')
        return false
      }
    } catch (error) {
      console.error('[WindowHelper] Failed to set content protection:', error)
      return false
    }
  }

  public toggleContentProtection(): boolean {
    const newState = !this.contentProtectionEnabled
    const success = this.setContentProtection(newState)
    return success ? newState : this.contentProtectionEnabled
  }

  public isContentProtectionEnabled(): boolean {
    return this.contentProtectionEnabled
  }

  public setVisibility(visible: boolean): void {
    if (!this.mainWindow) return

    if (visible) {
      this.mainWindow.setFocusable(true); // Make focusable before showing
      this.mainWindow.show()
      // In production, we need to handle mouse events differently
      if (!this.isDevelopment) {
        // When visible, we want to capture mouse events for the overlay UI
        this.mainWindow.setIgnoreMouseEvents(false)
      }
      // It's often good to explicitly focus after showing, especially if it was non-focusable
      // However, be cautious if this causes issues with other focus-dependent UI elements.
      // this.mainWindow.focus();
    } else {
      this.mainWindow.hide()
      this.mainWindow.setFocusable(false); // Make non-focusable after hiding
      // In production, when hidden, we want to ignore mouse events
      if (!this.isDevelopment) {
        this.mainWindow.setIgnoreMouseEvents(true, { forward: true })
      }
    }
  }

  public toggleVisibility(): boolean {
    const newVisibility = !this.isVisible()
    this.setVisibility(newVisibility)
    return newVisibility
  }

  public isVisible(): boolean {
    return this.mainWindow ? this.mainWindow.isVisible() : false
  }

  public setPosition(position: WindowPosition): void {
    if (!this.mainWindow) return
    this.mainWindow.setPosition(position.x, position.y)
    this.isManuallyPositioned = true
  }

  public getPosition(): WindowPosition {
    if (!this.mainWindow) return { x: 0, y: 0 }
    const [x, y] = this.mainWindow.getPosition()
    return { x, y }
  }

  public moveWindow(deltaX: number, deltaY: number): void {
    if (!this.mainWindow) return
    const currentPosition = this.getPosition()
    this.setPosition({
      x: currentPosition.x + deltaX,
      y: currentPosition.y + deltaY
    })
  }

  public setSize(size: WindowSize): void {
    if (!this.mainWindow) return
    this.mainWindow.setSize(size.width, size.height)
  }

  public getSize(): WindowSize {
    if (!this.mainWindow) return { width: 0, height: 0 }
    const [width, height] = this.mainWindow.getSize()
    return { width, height }
  }

  public setAlwaysOnTop(alwaysOnTop: boolean): void {
    if (!this.mainWindow) return
    this.mainWindow.setAlwaysOnTop(alwaysOnTop)
  }

  public setResizable(resizable: boolean): void {
    if (!this.mainWindow) return
    this.mainWindow.setResizable(resizable)
  }

  public setTransparency(transparent: boolean): void {
    if (!this.mainWindow) return
    // The BrowserWindow option `transparent: true` handles actual window transparency.
    // This method *could* control overall window content opacity if desired,
    // but for now, let's not force an opacity change here as it might conflict
    // with CSS opacity on elements like the header.
    // If you need to change overall window opacity, uncomment and adjust:
    // this.mainWindow.setOpacity(transparent ? 0.8 : 1.0)
    console.log(`[WindowHelper] setTransparency called with: ${transparent}. Opacity not changed by this method directly.`);
  }

  /**
   * Positions the window at the top of the screen
   */
  public positionAtTop(): void {
    if (!this.mainWindow) return

    // Get the primary display dimensions
    const primaryDisplay = screen.getPrimaryDisplay()
    const { width } = primaryDisplay.workAreaSize
    const topY = primaryDisplay.workArea.y // Use workArea.y for top edge

    // Calculate center X position
    const [currentWidth] = this.mainWindow.getSize()
    const centerX = Math.floor((width - currentWidth) / 2)

    // Set position at the top visible edge (below menu bar)
    this.mainWindow.setPosition(centerX, topY)

    // Reset the manual positioning flag since this is an explicit repositioning
    this.isManuallyPositioned = false
  }

  public ensureVisibilityAcrossWorkspaces(): void {
    // If the overlay is not supposed to be visible according to AppState, do nothing.
    if (!AppState.getInstance().isOverlayVisible()) {
      // Optionally, if it's not supposed to be visible, ensure it's actually hidden.
      // This might be redundant if setVisibility(false) was called correctly, but can be a safeguard.
      // if (this.mainWindow && this.mainWindow.isVisible()) {
      //   this.mainWindow.hide();
      //   this.mainWindow.setFocusable(false);
      // }
      return;
    }

    if (!this.mainWindow) return

    // Make sure it stays on top with the highest level
    // 'pop-up-menu' has a higher z-index than 'floating' for better visibility
    this.mainWindow.setAlwaysOnTop(true, 'pop-up-menu')

    // Set the window to be visible on all workspaces (macOS only)
    if (process.platform === 'darwin') {
      // For macOS, we need to set both visibleOnAllWorkspaces and level
      this.mainWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true })

      // For macOS, we need to use the correct approach for setting window levels
      // The 'pop-up-menu' level is already set above with setAlwaysOnTop, which is the correct way
      // to set window levels in Electron
    }

    // Force the window to show
    this.mainWindow.showInactive()

    // Only reposition the window if it hasn't been manually positioned
    if (!this.isManuallyPositioned) {
      this.positionAtTop()
    }
  }

  /**
   * Periodically reasserts the window's always-on-top status
   * This helps ensure the window stays visible across all applications
   */
  public startAlwaysOnTopInterval(): void {
    // Clear any existing interval
    this.stopAlwaysOnTopInterval()

    // Set up an interval to reassert always-on-top status every 1 second (more frequent checks)
    const intervalId = setInterval(() => {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.ensureVisibilityAcrossWorkspaces()
      } else {
        this.stopAlwaysOnTopInterval()
      }
    }, 1000)

    // Store the interval ID for later cleanup
    // @ts-ignore - Adding a new property to the class
    this.alwaysOnTopIntervalId = intervalId
  }

  /**
   * Stops the always-on-top interval
   */
  public stopAlwaysOnTopInterval(): void {
    // @ts-ignore - Accessing the property we added
    if (this.alwaysOnTopIntervalId) {
      // @ts-ignore - Accessing the property we added
      clearInterval(this.alwaysOnTopIntervalId)
      // @ts-ignore - Accessing the property we added
      this.alwaysOnTopIntervalId = null
    }
  }

  /**
   * Resets the manual positioning flag
   * Call this when you want to allow automatic repositioning again
   */
  public resetManualPositioning(): void {
    this.isManuallyPositioned = false
  }

  /**
   * Checks if the window has been manually positioned
   * @returns boolean indicating if the window was manually positioned
   */
  public isWindowManuallyPositioned(): boolean {
    return this.isManuallyPositioned
  }

  public setHoverExpand(expand: boolean): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return;

    if (expand) {
      if (!this.isHoverExpanded) { // Only store if not already hover-expanded
        // Store the actual current height before hover, could be compact or user-expanded
        // const currentHeight = this.mainWindow.getBounds().height;
        // Just using baseHeight instead of storing previous height
      }
      this.resizeWindow(WINDOW_WIDTH, APP_SETTINGS_HEIGHT); // Expand to settings height
      this.isHoverExpanded = true;
    } else {
      if (this.isHoverExpanded) {
        // When hover ends, restore to the current baseHeight (which might have been changed by a click)
        this.resizeWindow(WINDOW_WIDTH, this.baseHeight);
        this.isHoverExpanded = false;
        // Clear stored height (removed)
      }
    }
  }

  /**
   * Toggles the window between compact (header only) and expanded (header + body) states.
   */
  public toggleCompactExpand(): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return

    if (this.baseHeight === COMPACT_WINDOW_HEIGHT) {
      this.baseHeight = EXPANDED_WINDOW_HEIGHT;
    } else {
      this.baseHeight = COMPACT_WINDOW_HEIGHT;
    }

    // If not currently hover-expanded, apply the new baseHeight immediately.
    // If hover-expanded, the new baseHeight will take effect when hover ends.
    if (!this.isHoverExpanded) {
      this.resizeWindow(WINDOW_WIDTH, this.baseHeight);
    }

    // Log the state change
    console.log(`[WindowHelper] Toggled compact/expand. New base height: ${this.baseHeight}px. Is hover expanded: ${this.isHoverExpanded}`);
  }

  /**
   * Resizes the window to the specified dimensions.
   * @param width The new width of the window (currently unused, keeping WINDOW_WIDTH).
   * @param height The new height of the window.
   */
  public resizeWindow(_width: number, height: number): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return

    const currentBounds = this.mainWindow.getBounds()
    // We only change height, width is fixed for now unless specified otherwise
    // Keep current x, y position
    this.mainWindow.setBounds({
        x: currentBounds.x,
        y: currentBounds.y,
        width: WINDOW_WIDTH, // Use constant WINDOW_WIDTH
        height: Math.round(height) // Ensure height is an integer
    })
    console.log(`[WindowHelper] Resized window to: ${WINDOW_WIDTH}x${Math.round(height)}`);
  }

  // Multi-window system methods
  public enableMultiWindowMode(): void {
    if (this.windowCoordinator || !this.mainWindow) {
      console.log('[WindowHelper] Multi-window mode already initialized or no main window')
      return
    }

    console.log('[WindowHelper] Enabling multi-window mode')
    this.multiWindowEnabled = true

    // Initialize window coordinator
    this.windowCoordinator = new WindowCoordinator({
      isDevelopment: this.isDevelopment
    })

    // Add main window to coordinator as 'header'
    this.windowCoordinator.addWindow('header', this.mainWindow)

    // Create feature windows
    this.windowCoordinator.createFeatureWindows(this.mainWindow)

    // Set up header movement listener for coordinated movement
    this.mainWindow.on('moved', () => {
      if (this.windowCoordinator) {
        this.windowCoordinator.updateChildWindowLayouts(false)
      }
    })
  }

  public disableMultiWindowMode(): void {
    if (!this.multiWindowEnabled || !this.windowCoordinator) return

    console.log('[WindowHelper] Disabling multi-window mode')
    this.multiWindowEnabled = false

    // Destroy feature windows and coordinator
    this.windowCoordinator.destroy()
    this.windowCoordinator = null
  }

  public isMultiWindowEnabled(): boolean {
    return this.multiWindowEnabled
  }

  // Window visibility methods for multi-window system
  public showFeatureWindow(windowName: 'askAI' | 'microphone' | 'settings'): void {
    console.log(`[WindowHelper] showFeatureWindow called for: ${windowName}`)
    console.log(`[WindowHelper] multiWindowEnabled: ${this.multiWindowEnabled}`)
    console.log(`[WindowHelper] windowCoordinator exists: ${!!this.windowCoordinator}`)

    if (!this.multiWindowEnabled || !this.windowCoordinator) {
      console.warn('[WindowHelper] Multi-window mode not enabled or coordinator missing')
      return
    }

    console.log(`[WindowHelper] Showing feature window: ${windowName}`)
    this.windowCoordinator.handleWindowVisibilityRequest(windowName, true)
  }

  public hideFeatureWindow(windowName: 'askAI' | 'microphone' | 'settings'): void {
    if (!this.multiWindowEnabled || !this.windowCoordinator) {
      console.warn('[WindowHelper] Multi-window mode not enabled')
      return
    }

    this.windowCoordinator.handleWindowVisibilityRequest(windowName, false)
  }

  public getFeatureWindow(windowName: string): BrowserWindow | undefined {
    if (!this.multiWindowEnabled || !this.windowCoordinator) {
      return undefined
    }

    return this.windowCoordinator.getWindow(windowName)
  }
}

export default WindowHelper.getInstance()
