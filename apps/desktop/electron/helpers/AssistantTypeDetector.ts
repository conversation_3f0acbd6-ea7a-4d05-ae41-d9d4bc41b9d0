/**
 * Enhanced Assistant Type Detector
 * 
 * Provides intelligent, configurable assistant type detection that goes beyond
 * simple keyword matching to include context analysis and user preferences.
 */

import { CallContext } from './AIInteractionService'

export type AssistanceType = 
  | 'objection' 
  | 'product_info' 
  | 'competitive_positioning' 
  | 'price_objection' 
  | 'closing' 
  | 'discovery' 
  | 'general_assistance'

export interface DetectionRule {
  type: AssistanceType
  keywords: string[]
  contextPatterns: RegExp[]
  priority: number
  confidence: number
  requiredContext?: string[]
}

export interface DetectionConfig {
  rules: DetectionRule[]
  defaultType: AssistanceType
  confidenceThreshold: number
  enableContextAnalysis: boolean
  enableLearning: boolean
}

export interface DetectionResult {
  type: AssistanceType
  confidence: number
  reasoning: string
  alternativeTypes: Array<{
    type: AssistanceType
    confidence: number
  }>
}

export class AssistantTypeDetector {
  private config: DetectionConfig
  private userPreferences: Map<string, number> = new Map()

  constructor(config?: Partial<DetectionConfig>) {
    this.config = {
      rules: this.getDefaultRules(),
      defaultType: 'general_assistance',
      confidenceThreshold: 0.4, // Lowered from 0.6 to be more responsive
      enableContextAnalysis: true,
      enableLearning: false,
      ...config
    }
  }

  /**
   * Detect the most appropriate assistance type based on context
   */
  public detectAssistanceType(context: CallContext): DetectionResult {
    const transcript = context.currentTranscriptSegment?.toLowerCase() || ''
    const query = context.userQuery?.toLowerCase() || ''
    const combinedText = `${transcript} ${query}`.trim()

    if (!combinedText) {
      return {
        type: this.config.defaultType,
        confidence: 0.5,
        reasoning: 'No text content available for analysis',
        alternativeTypes: []
      }
    }

    // Score all rules against the context
    const scores = this.config.rules.map(rule => ({
      rule,
      score: this.scoreRule(rule, combinedText, context)
    }))

    // Sort by score descending
    scores.sort((a, b) => b.score - a.score)

    const bestMatch = scores[0]
    const alternatives = scores
      .slice(1, 4)
      .filter(s => s.score > 0.3)
      .map(s => ({
        type: s.rule.type,
        confidence: s.score
      }))

    // Apply user preferences if learning is enabled
    if (this.config.enableLearning) {
      const preferenceBoost = this.getUserPreference(bestMatch.rule.type)
      bestMatch.score = Math.min(1.0, bestMatch.score + preferenceBoost)
    }

    return {
      type: bestMatch.score >= this.config.confidenceThreshold 
        ? bestMatch.rule.type 
        : this.config.defaultType,
      confidence: bestMatch.score,
      reasoning: this.generateReasoning(bestMatch.rule, combinedText, context),
      alternativeTypes: alternatives
    }
  }

  /**
   * Score a detection rule against the given text and context
   */
  private scoreRule(rule: DetectionRule, text: string, context: CallContext): number {
    let score = 0

    // Keyword matching (base score)
    const keywordMatches = rule.keywords.filter(keyword => 
      text.includes(keyword.toLowerCase())
    ).length
    score += (keywordMatches / rule.keywords.length) * 0.4

    // Pattern matching (enhanced score)
    const patternMatches = rule.contextPatterns.filter(pattern => 
      pattern.test(text)
    ).length
    score += (patternMatches / Math.max(1, rule.contextPatterns.length)) * 0.3

    // Context analysis (if enabled)
    if (this.config.enableContextAnalysis) {
      score += this.analyzeContext(rule, context) * 0.2
    }

    // Rule priority and base confidence
    score += (rule.priority / 10) * 0.05
    score += rule.confidence * 0.05

    return Math.min(1.0, score)
  }

  /**
   * Analyze conversation context for additional scoring
   */
  private analyzeContext(rule: DetectionRule, context: CallContext): number {
    let contextScore = 0

    // Deal stage analysis
    if (context.dealStage) {
      switch (rule.type) {
        case 'discovery':
          contextScore += context.dealStage === 'discovery' ? 0.8 : 0.2
          break
        case 'closing':
          contextScore += ['negotiation', 'closing'].includes(context.dealStage) ? 0.8 : 0.2
          break
        case 'competitive_positioning':
          contextScore += ['evaluation', 'negotiation'].includes(context.dealStage) ? 0.6 : 0.3
          break
        default:
          contextScore += 0.4 // Neutral for other types
      }
    }

    // Required context check
    if (rule.requiredContext) {
      const hasRequiredContext = rule.requiredContext.some(req => 
        context.currentTranscriptSegment?.toLowerCase().includes(req.toLowerCase()) ||
        context.userQuery?.toLowerCase().includes(req.toLowerCase())
      )
      contextScore += hasRequiredContext ? 0.3 : -0.2
    }

    return Math.max(0, Math.min(1, contextScore))
  }

  /**
   * Generate human-readable reasoning for the detection result
   */
  private generateReasoning(rule: DetectionRule, text: string, context: CallContext): string {
    const matchedKeywords = rule.keywords.filter(keyword => 
      text.includes(keyword.toLowerCase())
    )
    
    let reasoning = `Detected "${rule.type}" based on`
    
    if (matchedKeywords.length > 0) {
      reasoning += ` keywords: ${matchedKeywords.join(', ')}`
    }
    
    if (context.dealStage) {
      reasoning += `, deal stage: ${context.dealStage}`
    }
    
    if (rule.contextPatterns.some(pattern => pattern.test(text))) {
      reasoning += `, context patterns matched`
    }

    return reasoning
  }

  /**
   * Get user preference boost for a specific assistance type
   */
  private getUserPreference(type: AssistanceType): number {
    return (this.userPreferences.get(type) || 0) * 0.1
  }

  /**
   * Update user preferences based on feedback
   */
  public updateUserPreference(type: AssistanceType, positive: boolean): void {
    if (!this.config.enableLearning) return

    const current = this.userPreferences.get(type) || 0
    const adjustment = positive ? 1 : -1
    this.userPreferences.set(type, Math.max(-5, Math.min(5, current + adjustment)))
  }

  /**
   * Update detection configuration
   */
  public updateConfig(newConfig: Partial<DetectionConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Get current configuration
   */
  public getConfig(): DetectionConfig {
    return { ...this.config }
  }

  /**
   * Default detection rules with enhanced patterns
   */
  private getDefaultRules(): DetectionRule[] {
    return [
      {
        type: 'price_objection',
        keywords: ['price', 'cost', 'expensive', 'budget', 'afford', 'cheaper', 'discount'],
        contextPatterns: [
          /too\s+(expensive|costly|much)/i,
          /can't\s+afford/i,
          /budget\s+(concerns?|issues?)/i,
          /price\s+is\s+(high|too)/i
        ],
        priority: 9,
        confidence: 0.8,
        requiredContext: ['price', 'cost', 'budget']
      },
      {
        type: 'competitive_positioning',
        keywords: ['competitor', 'alternative', 'vs', 'versus', 'compare', 'competition'],
        contextPatterns: [
          /compared?\s+to/i,
          /what\s+about\s+\w+/i,
          /vs\.?\s+\w+/i,
          /alternative\s+to/i
        ],
        priority: 8,
        confidence: 0.8,
        requiredContext: ['competitor', 'alternative', 'compare']
      },
      {
        type: 'product_info',
        keywords: ['feature', 'how does', 'what is', 'explain', 'tell me about', 'product'],
        contextPatterns: [
          /how\s+does\s+\w+\s+work/i,
          /what\s+(is|are)\s+the\s+features?/i,
          /tell\s+me\s+about/i,
          /explain\s+how/i
        ],
        priority: 7,
        confidence: 0.7
      },
      {
        type: 'closing',
        keywords: ['close', 'next step', 'move forward', 'decision', 'sign', 'contract'],
        contextPatterns: [
          /ready\s+to\s+(move|proceed)/i,
          /next\s+steps?/i,
          /when\s+can\s+we\s+start/i,
          /let's\s+move\s+forward/i
        ],
        priority: 8,
        confidence: 0.8
      },
      {
        type: 'discovery',
        keywords: ['question', 'tell me', 'what', 'how', 'why', 'when', 'where'],
        contextPatterns: [
          /tell\s+me\s+about\s+your/i,
          /what\s+(is|are)\s+your/i,
          /how\s+do\s+you\s+currently/i,
          /what\s+challenges/i
        ],
        priority: 6,
        confidence: 0.6
      },
      {
        type: 'objection',
        keywords: ['concern', 'worry', 'problem', 'issue', 'but', 'however', 'not sure'],
        contextPatterns: [
          /i'm\s+(not\s+sure|concerned|worried)/i,
          /but\s+what\s+about/i,
          /however[,\s]/i,
          /that's\s+a\s+problem/i
        ],
        priority: 7,
        confidence: 0.7
      },
      {
        type: 'general_assistance',
        keywords: ['help', 'assist', 'support', 'advice', 'guidance'],
        contextPatterns: [
          /can\s+you\s+help/i,
          /i\s+need\s+(help|assistance)/i,
          /what\s+should\s+i/i
        ],
        priority: 5,
        confidence: 0.5
      }
    ]
  }
}

export default AssistantTypeDetector
