/**
 * Conversation Memory Manager
 * Handles persistent conversation memory across desktop app sessions
 * Integrates with agent state and provides local caching
 */

import { logger } from '../utils/logger'
import { AgentState, ConversationMemory, Message, WorkingMemoryItem } from './AgentIntegrationService'

export interface ConversationSession {
  conversationId: string
  userId: string
  startTime: Date
  lastActivity: Date
  messageCount: number
  summary?: string
  tags: string[]
  metadata: any
}

export interface MemorySearchResult {
  content: string
  relevance: number
  timestamp: Date
  type: string
  conversationId: string
}

export interface MemoryStats {
  totalConversations: number
  totalMessages: number
  averageSessionLength: number
  mostActiveHours: number[]
  topTopics: Array<{ topic: string; count: number }>
}

export class ConversationMemoryManager {
  private static instance: ConversationMemoryManager
  private sessions: Map<string, ConversationSession> = new Map()
  private memoryCache: Map<string, ConversationMemory> = new Map()
  private storageKey = 'closezly_conversation_memory'
  private maxCachedSessions = 50
  private maxMemoryAge = 30 * 24 * 60 * 60 * 1000 // 30 days

  private constructor() {
    this.loadFromStorage()
    this.startCleanupTimer()
    logger.info('[ConversationMemory] Manager initialized')
  }

  static getInstance(): ConversationMemoryManager {
    if (!ConversationMemoryManager.instance) {
      ConversationMemoryManager.instance = new ConversationMemoryManager()
    }
    return ConversationMemoryManager.instance
  }

  /**
   * Start a new conversation session
   */
  startSession(conversationId: string, userId: string, metadata: any = {}): ConversationSession {
    const session: ConversationSession = {
      conversationId,
      userId,
      startTime: new Date(),
      lastActivity: new Date(),
      messageCount: 0,
      tags: [],
      metadata
    }

    this.sessions.set(conversationId, session)
    this.saveToStorage()

    logger.info(`[ConversationMemory] Started session: ${conversationId}`)
    return session
  }

  /**
   * Update session activity
   */
  updateSessionActivity(conversationId: string): void {
    const session = this.sessions.get(conversationId)
    if (session) {
      session.lastActivity = new Date()
      session.messageCount++
      this.saveToStorage()
    }
  }

  /**
   * Add memory to conversation
   */
  addMemory(conversationId: string, memory: ConversationMemory): void {
    this.memoryCache.set(conversationId, memory)
    this.updateSessionActivity(conversationId)
    
    // Limit cache size
    if (this.memoryCache.size > this.maxCachedSessions) {
      const oldestKey = Array.from(this.memoryCache.keys())[0]
      this.memoryCache.delete(oldestKey)
    }

    logger.info(`[ConversationMemory] Added memory for conversation: ${conversationId}`)
  }

  /**
   * Get conversation memory
   */
  getMemory(conversationId: string): ConversationMemory | null {
    return this.memoryCache.get(conversationId) || null
  }

  /**
   * Search across conversation memories
   */
  searchMemories(query: string, limit: number = 10): MemorySearchResult[] {
    const results: MemorySearchResult[] = []
    const queryLower = query.toLowerCase()

    for (const [conversationId, memory] of this.memoryCache.entries()) {
      // Search short-term memory
      for (const message of memory.shortTerm) {
        if (message.content.toLowerCase().includes(queryLower)) {
          results.push({
            content: message.content,
            relevance: this.calculateRelevance(message.content, query),
            timestamp: message.timestamp,
            type: 'message',
            conversationId
          })
        }
      }

      // Search working memory
      for (const item of memory.workingMemory) {
        if (item.content.toLowerCase().includes(queryLower)) {
          results.push({
            content: item.content,
            relevance: this.calculateRelevance(item.content, query),
            timestamp: item.timestamp,
            type: item.type,
            conversationId
          })
        }
      }

      // Search long-term summary
      if (memory.longTermSummary && memory.longTermSummary.toLowerCase().includes(queryLower)) {
        results.push({
          content: memory.longTermSummary,
          relevance: this.calculateRelevance(memory.longTermSummary, query),
          timestamp: new Date(), // Use current time for summaries
          type: 'summary',
          conversationId
        })
      }
    }

    // Sort by relevance and limit results
    return results
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, limit)
  }

  /**
   * Get conversation session
   */
  getSession(conversationId: string): ConversationSession | null {
    return this.sessions.get(conversationId) || null
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(maxAge: number = 24 * 60 * 60 * 1000): ConversationSession[] {
    const cutoff = new Date(Date.now() - maxAge)
    return Array.from(this.sessions.values())
      .filter(session => session.lastActivity > cutoff)
      .sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime())
  }

  /**
   * Add tags to conversation
   */
  addTags(conversationId: string, tags: string[]): void {
    const session = this.sessions.get(conversationId)
    if (session) {
      session.tags = [...new Set([...session.tags, ...tags])]
      this.saveToStorage()
    }
  }

  /**
   * Update conversation summary
   */
  updateSummary(conversationId: string, summary: string): void {
    const session = this.sessions.get(conversationId)
    if (session) {
      session.summary = summary
      this.saveToStorage()
    }

    const memory = this.memoryCache.get(conversationId)
    if (memory) {
      memory.longTermSummary = summary
    }
  }

  /**
   * Get memory statistics
   */
  getMemoryStats(): MemoryStats {
    const sessions = Array.from(this.sessions.values())
    const totalMessages = sessions.reduce((sum, session) => sum + session.messageCount, 0)
    
    // Calculate average session length
    const sessionLengths = sessions.map(session => 
      session.lastActivity.getTime() - session.startTime.getTime()
    )
    const averageSessionLength = sessionLengths.length > 0 
      ? sessionLengths.reduce((sum, length) => sum + length, 0) / sessionLengths.length
      : 0

    // Calculate most active hours
    const hourCounts = new Array(24).fill(0)
    sessions.forEach(session => {
      const hour = session.startTime.getHours()
      hourCounts[hour]++
    })
    const mostActiveHours = hourCounts
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour)

    // Calculate top topics from tags
    const tagCounts = new Map<string, number>()
    sessions.forEach(session => {
      session.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
      })
    })
    const topTopics = Array.from(tagCounts.entries())
      .map(([topic, count]) => ({ topic, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    return {
      totalConversations: sessions.length,
      totalMessages,
      averageSessionLength,
      mostActiveHours,
      topTopics
    }
  }

  /**
   * Clear old memories
   */
  clearOldMemories(): void {
    const cutoff = new Date(Date.now() - this.maxMemoryAge)
    
    for (const [conversationId, session] of this.sessions.entries()) {
      if (session.lastActivity < cutoff) {
        this.sessions.delete(conversationId)
        this.memoryCache.delete(conversationId)
      }
    }

    this.saveToStorage()
    logger.info('[ConversationMemory] Cleared old memories')
  }

  /**
   * Export conversation data
   */
  exportConversationData(conversationId: string): any {
    const session = this.sessions.get(conversationId)
    const memory = this.memoryCache.get(conversationId)

    return {
      session,
      memory,
      exportedAt: new Date()
    }
  }

  /**
   * Import conversation data
   */
  importConversationData(data: any): void {
    if (data.session) {
      this.sessions.set(data.session.conversationId, data.session)
    }
    if (data.memory) {
      this.memoryCache.set(data.session.conversationId, data.memory)
    }
    this.saveToStorage()
  }

  /**
   * Calculate relevance score for search
   */
  private calculateRelevance(content: string, query: string): number {
    const contentLower = content.toLowerCase()
    const queryLower = query.toLowerCase()
    
    // Exact match gets highest score
    if (contentLower.includes(queryLower)) {
      return 1.0
    }

    // Word-based matching
    const queryWords = queryLower.split(/\s+/)
    const contentWords = contentLower.split(/\s+/)
    const matchingWords = queryWords.filter(word => 
      contentWords.some(contentWord => contentWord.includes(word))
    )

    return matchingWords.length / queryWords.length
  }

  /**
   * Load data from localStorage
   */
  private loadFromStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem(this.storageKey)
        if (stored) {
          const data = JSON.parse(stored)
          
          // Restore sessions
          if (data.sessions) {
            for (const [id, session] of Object.entries(data.sessions as any)) {
              const sessionData = session as any
              this.sessions.set(id, {
                ...sessionData,
                startTime: new Date(sessionData.startTime),
                lastActivity: new Date(sessionData.lastActivity)
              })
            }
          }

          // Restore memory cache (limited)
          if (data.memoryCache) {
            for (const [id, memory] of Object.entries(data.memoryCache as any)) {
              this.memoryCache.set(id, memory as ConversationMemory)
            }
          }
        }
      }
    } catch (error: any) {
      logger.error(`[ConversationMemory] Failed to load from storage: ${error.message}`)
    }
  }

  /**
   * Save data to localStorage
   */
  private saveToStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const data = {
          sessions: Object.fromEntries(this.sessions),
          memoryCache: Object.fromEntries(this.memoryCache),
          lastSaved: new Date()
        }
        localStorage.setItem(this.storageKey, JSON.stringify(data))
      }
    } catch (error: any) {
      logger.error(`[ConversationMemory] Failed to save to storage: ${error.message}`)
    }
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    // Clean up old memories every hour
    setInterval(() => {
      this.clearOldMemories()
    }, 60 * 60 * 1000)
  }
}
