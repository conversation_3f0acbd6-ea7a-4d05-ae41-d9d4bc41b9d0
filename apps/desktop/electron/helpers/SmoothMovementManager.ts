/**
 * SmoothMovementManager.ts
 * 
 * Handles smooth animations and coordinated movement between multiple windows.
 * Based on Glass app's approach with easing functions and proper cleanup.
 */

import { BrowserWindow } from 'electron'

interface Rectangle {
  x: number
  y: number
  width: number
  height: number
}

interface AnimationOptions {
  duration?: number
  onComplete?: () => void
}

interface FadeOptions {
  from?: number
  to: number
  duration?: number
  onComplete?: () => void
}

export class SmoothMovementManager {
  private windowPool: Map<string, BrowserWindow>
  private animationTimers: Map<BrowserWindow, NodeJS.Timeout> = new Map()
  private isAnimating = false
  private readonly defaultDuration = 300

  constructor(windowPool: Map<string, BrowserWindow>) {
    this.windowPool = windowPool
  }

  /**
   * Checks if a window is valid and cleans up timers if not
   */
  private isWindowValid(window: BrowserWindow): boolean {
    if (!window || window.isDestroyed()) {
      // Clean up any existing timer for this window
      if (this.animationTimers.has(window)) {
        clearTimeout(this.animationTimers.get(window)!)
        this.animationTimers.delete(window)
      }
      return false
    }
    return true
  }

  /**
   * Animates a window's opacity with smooth transitions
   */
  public fade(window: BrowserWindow, options: FadeOptions): void {
    if (!this.isWindowValid(window)) {
      if (options.onComplete) options.onComplete()
      return
    }

    const startOpacity = options.from ?? window.getOpacity()
    const targetOpacity = options.to
    const duration = options.duration ?? 250
    const startTime = Date.now()

    const animate = () => {
      if (!this.isWindowValid(window)) {
        if (options.onComplete) options.onComplete()
        return
      }

      const progress = Math.min(1, (Date.now() - startTime) / duration)
      const eased = 1 - Math.pow(1 - progress, 3) // ease-out-cubic
      const currentOpacity = startOpacity + (targetOpacity - startOpacity) * eased

      window.setOpacity(currentOpacity)

      if (progress < 1) {
        setTimeout(animate, 16) // ~60fps
      } else {
        window.setOpacity(targetOpacity)
        if (options.onComplete) options.onComplete()
      }
    }

    animate()
  }

  /**
   * Animates a window to new bounds (position and size)
   */
  public animateWindowBounds(
    window: BrowserWindow, 
    targetBounds: Rectangle, 
    options: AnimationOptions = {}
  ): void {
    // Clear any existing animation for this window
    if (this.animationTimers.has(window)) {
      clearTimeout(this.animationTimers.get(window)!)
      this.animationTimers.delete(window)
    }

    if (!this.isWindowValid(window)) {
      if (options.onComplete) options.onComplete()
      return
    }

    this.isAnimating = true

    const startBounds = window.getBounds()
    const duration = options.duration ?? this.defaultDuration
    const startTime = Date.now()

    const animate = () => {
      if (!this.isWindowValid(window)) {
        this.animationTimers.delete(window)
        if (this.animationTimers.size === 0) {
          this.isAnimating = false
        }
        if (options.onComplete) options.onComplete()
        return
      }

      const progress = Math.min(1, (Date.now() - startTime) / duration)
      const eased = 1 - Math.pow(1 - progress, 3) // ease-out-cubic

      const currentBounds: Rectangle = {
        x: Math.round(startBounds.x + (targetBounds.x - startBounds.x) * eased),
        y: Math.round(startBounds.y + (targetBounds.y - startBounds.y) * eased),
        width: Math.round(startBounds.width + (targetBounds.width - startBounds.width) * eased),
        height: Math.round(startBounds.height + (targetBounds.height - startBounds.height) * eased),
      }

      window.setBounds(currentBounds)

      if (progress < 1) {
        const timerId = setTimeout(animate, 16)
        this.animationTimers.set(window, timerId)
      } else {
        // Ensure final position is exact
        window.setBounds(targetBounds)
        this.animationTimers.delete(window)
        
        // Check if all animations are complete
        if (this.animationTimers.size === 0) {
          this.isAnimating = false
        }
        
        if (options.onComplete) options.onComplete()
      }
    }

    animate()
  }

  /**
   * Animates a window to a new position (keeping current size)
   */
  public animateWindowPosition(
    window: BrowserWindow, 
    targetPosition: { x: number; y: number }, 
    options: AnimationOptions = {}
  ): void {
    if (!this.isWindowValid(window)) {
      if (options.onComplete) options.onComplete()
      return
    }

    const currentBounds = window.getBounds()
    const targetBounds: Rectangle = {
      ...currentBounds,
      ...targetPosition
    }

    this.animateWindowBounds(window, targetBounds, options)
  }

  /**
   * Animates multiple windows to their target positions simultaneously
   */
  public animateLayout(
    layout: Record<string, Rectangle>, 
    animated = true
  ): void {
    if (!layout || Object.keys(layout).length === 0) return

    for (const [windowName, targetBounds] of Object.entries(layout)) {
      const window = this.windowPool.get(windowName)
      
      if (window && !window.isDestroyed() && targetBounds) {
        if (animated) {
          this.animateWindowBounds(window, targetBounds)
        } else {
          window.setBounds(targetBounds)
        }
      }
    }
  }

  /**
   * Immediately stops all animations and cleans up timers
   */
  public stopAllAnimations(): void {
    for (const [window, timerId] of this.animationTimers) {
      clearTimeout(timerId)
    }
    this.animationTimers.clear()
    this.isAnimating = false
  }

  /**
   * Returns whether any animations are currently running
   */
  public get isCurrentlyAnimating(): boolean {
    return this.isAnimating
  }

  /**
   * Cleans up all resources
   */
  public destroy(): void {
    this.stopAllAnimations()
    console.log('[SmoothMovementManager] Destroyed')
  }
}
