/**
 * RAG Configuration Manager
 *
 * Provides dynamic configuration for RAG (Retrieval-Augmented Generation) behavior
 * in the desktop application, allowing users to customize knowledge base usage.
 */

import Store from 'electron-store'

export interface RAGConfig {
  enabled: boolean
  similarityThreshold: number
  contextLimit: number
  maxResults: number
  enableQueryRewriting: boolean
  enableContextOptimization: boolean
  knowledgeSourceFilters: string[]
  responseMode: 'enhanced' | 'balanced' | 'minimal'
  debugMode: boolean
}

export interface RAGUsageStats {
  totalQueries: number
  ragEnabledQueries: number
  averageConfidence: number
  topKnowledgeSources: Array<{
    source: string
    usageCount: number
  }>
  lastUpdated: Date
}

export class RAGConfigManager {
  private static instance: RAGConfigManager
  private config: RAGConfig
  private stats: RAGUsageStats
  private configChangeListeners: Array<(config: RAGConfig) => void> = []
  private store: Store

  private constructor() {
    // Initialize the secure store
    this.store = new Store({
      name: 'closezly-rag-config',
      encryptionKey: process.env.ELECTRON_STORE_ENCRYPTION_KEY || 'closezly-rag-key'
    })

    this.config = this.getDefaultConfig()
    this.stats = this.getDefaultStats()
    this.loadConfigFromStorage()
  }

  public static getInstance(): RAGConfigManager {
    if (!RAGConfigManager.instance) {
      RAGConfigManager.instance = new RAGConfigManager()
    }
    return RAGConfigManager.instance
  }

  /**
   * Get current RAG configuration
   */
  public getConfig(): RAGConfig {
    return { ...this.config }
  }

  /**
   * Update RAG configuration
   */
  public updateConfig(updates: Partial<RAGConfig>): void {
    const oldConfig = { ...this.config }
    this.config = { ...this.config, ...updates }
    
    // Validate configuration
    this.validateConfig()
    
    // Save to storage
    this.saveConfigToStorage()
    
    // Notify listeners
    this.notifyConfigChange()
    
    console.log('[RAGConfig] Configuration updated:', {
      changes: this.getConfigDiff(oldConfig, this.config),
      newConfig: this.config
    })
  }

  /**
   * Reset configuration to defaults
   */
  public resetToDefaults(): void {
    this.config = this.getDefaultConfig()
    this.saveConfigToStorage()
    this.notifyConfigChange()
    console.log('[RAGConfig] Configuration reset to defaults')
  }

  /**
   * Get RAG usage statistics
   */
  public getStats(): RAGUsageStats {
    return { ...this.stats }
  }

  /**
   * Update usage statistics
   */
  public updateStats(ragUsed: boolean, confidence?: number, sources?: string[]): void {
    this.stats.totalQueries++
    
    if (ragUsed) {
      this.stats.ragEnabledQueries++
      
      if (confidence !== undefined) {
        // Update rolling average confidence
        const totalRagQueries = this.stats.ragEnabledQueries
        this.stats.averageConfidence = 
          ((this.stats.averageConfidence * (totalRagQueries - 1)) + confidence) / totalRagQueries
      }
      
      if (sources) {
        // Update top knowledge sources
        sources.forEach(source => {
          const existing = this.stats.topKnowledgeSources.find(s => s.source === source)
          if (existing) {
            existing.usageCount++
          } else {
            this.stats.topKnowledgeSources.push({ source, usageCount: 1 })
          }
        })
        
        // Keep only top 10 sources
        this.stats.topKnowledgeSources.sort((a, b) => b.usageCount - a.usageCount)
        this.stats.topKnowledgeSources = this.stats.topKnowledgeSources.slice(0, 10)
      }
    }
    
    this.stats.lastUpdated = new Date()
    this.saveStatsToStorage()
  }

  /**
   * Get configuration optimized for API request
   */
  public getAPIConfig(): {
    useRAG: boolean
    similarityThreshold: number
    contextLimit: number
    maxResults: number
  } {
    return {
      useRAG: this.config.enabled,
      similarityThreshold: this.config.similarityThreshold,
      contextLimit: this.config.contextLimit,
      maxResults: this.config.maxResults
    }
  }

  /**
   * Get configuration for specific response mode
   */
  public getConfigForMode(mode: RAGConfig['responseMode']): Partial<RAGConfig> {
    switch (mode) {
      case 'enhanced':
        return {
          similarityThreshold: 0.6,
          contextLimit: 5,
          maxResults: 8,
          enableQueryRewriting: true,
          enableContextOptimization: true
        }
      case 'balanced':
        return {
          similarityThreshold: 0.7,
          contextLimit: 3,
          maxResults: 5,
          enableQueryRewriting: true,
          enableContextOptimization: false
        }
      case 'minimal':
        return {
          similarityThreshold: 0.8,
          contextLimit: 2,
          maxResults: 3,
          enableQueryRewriting: false,
          enableContextOptimization: false
        }
      default:
        return {}
    }
  }

  /**
   * Add configuration change listener
   */
  public addConfigChangeListener(listener: (config: RAGConfig) => void): void {
    this.configChangeListeners.push(listener)
  }

  /**
   * Remove configuration change listener
   */
  public removeConfigChangeListener(listener: (config: RAGConfig) => void): void {
    const index = this.configChangeListeners.indexOf(listener)
    if (index > -1) {
      this.configChangeListeners.splice(index, 1)
    }
  }

  /**
   * Export configuration for backup
   */
  public exportConfig(): string {
    return JSON.stringify({
      config: this.config,
      stats: this.stats,
      exportDate: new Date().toISOString()
    }, null, 2)
  }

  /**
   * Import configuration from backup
   */
  public importConfig(configJson: string): boolean {
    try {
      const imported = JSON.parse(configJson)
      
      if (imported.config) {
        this.config = { ...this.getDefaultConfig(), ...imported.config }
        this.validateConfig()
        this.saveConfigToStorage()
        this.notifyConfigChange()
      }
      
      if (imported.stats) {
        this.stats = { ...this.getDefaultStats(), ...imported.stats }
        this.saveStatsToStorage()
      }
      
      console.log('[RAGConfig] Configuration imported successfully')
      return true
    } catch (error) {
      console.error('[RAGConfig] Failed to import configuration:', error)
      return false
    }
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): RAGConfig {
    return {
      enabled: true,
      similarityThreshold: 0.7,
      contextLimit: 3,
      maxResults: 5,
      enableQueryRewriting: false,
      enableContextOptimization: false,
      knowledgeSourceFilters: [],
      responseMode: 'balanced',
      debugMode: false
    }
  }

  /**
   * Get default statistics
   */
  private getDefaultStats(): RAGUsageStats {
    return {
      totalQueries: 0,
      ragEnabledQueries: 0,
      averageConfidence: 0,
      topKnowledgeSources: [],
      lastUpdated: new Date()
    }
  }

  /**
   * Validate configuration values
   */
  private validateConfig(): void {
    // Clamp similarity threshold between 0 and 1
    this.config.similarityThreshold = Math.max(0, Math.min(1, this.config.similarityThreshold))
    
    // Ensure positive values for limits
    this.config.contextLimit = Math.max(1, Math.min(10, this.config.contextLimit))
    this.config.maxResults = Math.max(1, Math.min(20, this.config.maxResults))
    
    // Validate response mode
    if (!['enhanced', 'balanced', 'minimal'].includes(this.config.responseMode)) {
      this.config.responseMode = 'balanced'
    }
  }

  /**
   * Load configuration from storage
   */
  private loadConfigFromStorage(): void {
    try {
      const stored = this.store.get('config')
      if (stored) {
        this.config = { ...this.config, ...stored }
        this.validateConfig()
      }

      const storedStats = this.store.get('stats')
      if (storedStats) {
        this.stats = { ...this.stats, ...storedStats }
        this.stats.lastUpdated = new Date(this.stats.lastUpdated)
      }
    } catch (error) {
      console.error('[RAGConfig] Failed to load configuration from storage:', error)
    }
  }

  /**
   * Save configuration to storage
   */
  private saveConfigToStorage(): void {
    try {
      this.store.set('config', this.config)
    } catch (error) {
      console.error('[RAGConfig] Failed to save configuration to storage:', error)
    }
  }

  /**
   * Save statistics to storage
   */
  private saveStatsToStorage(): void {
    try {
      this.store.set('stats', this.stats)
    } catch (error) {
      console.error('[RAGConfig] Failed to save statistics to storage:', error)
    }
  }

  /**
   * Notify configuration change listeners
   */
  private notifyConfigChange(): void {
    this.configChangeListeners.forEach(listener => {
      try {
        listener(this.config)
      } catch (error) {
        console.error('[RAGConfig] Error in config change listener:', error)
      }
    })
  }

  /**
   * Get configuration differences
   */
  private getConfigDiff(oldConfig: RAGConfig, newConfig: RAGConfig): Record<string, any> {
    const diff: Record<string, any> = {}
    
    Object.keys(newConfig).forEach(key => {
      const typedKey = key as keyof RAGConfig
      if (oldConfig[typedKey] !== newConfig[typedKey]) {
        diff[key] = {
          old: oldConfig[typedKey],
          new: newConfig[typedKey]
        }
      }
    })
    
    return diff
  }
}

export default RAGConfigManager
