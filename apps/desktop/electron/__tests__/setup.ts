// Test setup file for desktop app
import '@testing-library/jest-dom'

// Mock environment variables
process.env.NODE_ENV = 'test'

// Mock console methods to reduce test output noise
global.console = {
  ...console,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
}

// Global test timeout
jest.setTimeout(30000)

// Mock fetch for API tests
global.fetch = jest.fn()

// Mock Electron Store
jest.mock('electron-store', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    clear: jest.fn(),
    has: jest.fn(),
    store: {},
  }))
})

// Mock node-fetch
jest.mock('node-fetch', () => jest.fn())

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  send: jest.fn(),
  close: jest.fn(),
  readyState: 1,
})) as any

// Mock audio context for audio tests
global.AudioContext = jest.fn().mockImplementation(() => ({
  createMediaStreamSource: jest.fn(),
  createScriptProcessor: jest.fn(),
  createAnalyser: jest.fn(),
  close: jest.fn(),
  suspend: jest.fn(),
  resume: jest.fn(),
})) as any

// Mock media devices
Object.defineProperty(global.navigator, 'mediaDevices', {
  value: {
    getUserMedia: jest.fn(),
    enumerateDevices: jest.fn(),
  },
  writable: true,
})

// Mock screen capture API
Object.defineProperty(global.navigator, 'mediaDevices', {
  value: {
    ...global.navigator.mediaDevices,
    getDisplayMedia: jest.fn(),
  },
  writable: true,
})

// Mock file system APIs
Object.defineProperty(global, 'File', {
  value: jest.fn().mockImplementation((parts, filename, properties) => ({
    name: filename,
    size: parts.reduce((acc: number, part: any) => acc + (part.length || 0), 0),
    type: properties?.type || '',
    lastModified: Date.now(),
  })),
})

Object.defineProperty(global, 'FileReader', {
  value: jest.fn().mockImplementation(() => ({
    readAsDataURL: jest.fn(),
    readAsText: jest.fn(),
    readAsArrayBuffer: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    result: null,
    error: null,
  })),
})

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'test-uuid-123'),
    getRandomValues: jest.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    }),
  },
})

// Mock timers
jest.useFakeTimers()

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks()
  jest.clearAllTimers()
})
