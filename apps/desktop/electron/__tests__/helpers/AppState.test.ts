import AppState from '../../helpers/AppState'
import { BrowserWindow } from 'electron'

// Mock Electron
jest.mock('electron')

describe('AppState', () => {
  let mockWindow: any

  beforeEach(() => {
    // Reset AppState singleton
    ;(AppState as any).instance = undefined
    
    mockWindow = {
      webContents: {
        send: jest.fn(),
      },
      isDestroyed: jest.fn(() => false),
    }
    
    jest.clearAllMocks()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = AppState.getInstance()
      const instance2 = AppState.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })

  describe('Authentication State', () => {
    it('should initialize with unauthenticated state', () => {
      const appState = AppState.getInstance()
      
      expect(appState.isAuthenticated()).toBe(false)
      expect(appState.getUser()).toBeNull()
    })

    it('should set authenticated state with user', () => {
      const appState = AppState.getInstance()
      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        fullName: 'Test User',
        username: 'testuser',
        profilePictureUrl: 'https://example.com/avatar.jpg',
        subscriptionStatus: 'free' as const
      }

      appState.setAuthenticated(true, user)

      expect(appState.isAuthenticated()).toBe(true)
      expect(appState.getUser()).toEqual(user)
    })

    it('should clear user data when setting unauthenticated', () => {
      const appState = AppState.getInstance()
      const user = {
        id: 'user-123',
        email: '<EMAIL>',
        fullName: 'Test User',
        username: 'testuser',
        profilePictureUrl: 'https://example.com/avatar.jpg',
        subscriptionStatus: 'free' as const
      }

      appState.setAuthenticated(true, user)
      appState.setAuthenticated(false)

      expect(appState.isAuthenticated()).toBe(false)
      expect(appState.getUser()).toBeNull()
    })

    it('should emit auth-changed event when authentication state changes', () => {
      const appState = AppState.getInstance()
      const mockListener = jest.fn()
      
      appState.on('auth-changed', mockListener)
      appState.setAuthenticated(true)

      expect(mockListener).toHaveBeenCalledWith(true)
    })
  })

  describe('Overlay State', () => {
    it('should initialize with overlay hidden', () => {
      const appState = AppState.getInstance()
      
      expect(appState.isOverlayVisible()).toBe(false)
    })

    it('should toggle overlay visibility', () => {
      const appState = AppState.getInstance()
      
      appState.toggleOverlay()
      expect(appState.isOverlayVisible()).toBe(true)
      
      appState.toggleOverlay()
      expect(appState.isOverlayVisible()).toBe(false)
    })

    it('should set overlay visibility', () => {
      const appState = AppState.getInstance()
      
      appState.setOverlayVisible(true)
      expect(appState.isOverlayVisible()).toBe(true)
      
      appState.setOverlayVisible(false)
      expect(appState.isOverlayVisible()).toBe(false)
    })

    it('should emit overlay-changed event when visibility changes', () => {
      const appState = AppState.getInstance()
      const mockListener = jest.fn()
      
      appState.on('overlay-changed', mockListener)
      appState.setOverlayVisible(true)

      expect(mockListener).toHaveBeenCalledWith(true)
    })
  })

  describe('Processing State', () => {
    it('should initialize with not processing', () => {
      const appState = AppState.getInstance()
      
      expect(appState.isProcessing()).toBe(false)
    })

    it('should set processing state', () => {
      const appState = AppState.getInstance()
      
      appState.setProcessing(true)
      expect(appState.isProcessing()).toBe(true)
      
      appState.setProcessing(false)
      expect(appState.isProcessing()).toBe(false)
    })

    it('should emit processing-changed event when processing state changes', () => {
      const appState = AppState.getInstance()
      const mockListener = jest.fn()
      
      appState.on('processing-changed', mockListener)
      appState.setProcessing(true)

      expect(mockListener).toHaveBeenCalledWith(true)
    })
  })

  describe('Call Context', () => {
    it('should initialize with no active call', () => {
      const appState = AppState.getInstance()
      
      expect(appState.getActiveCall().isActive).toBe(false)
      expect(appState.getActiveCall().transcriptSegments).toEqual([])
    })

    it('should start a call', () => {
      const appState = AppState.getInstance()
      
      appState.startCall()
      
      const activeCall = appState.getActiveCall()
      expect(activeCall.isActive).toBe(true)
      expect(activeCall.startTime).toBeInstanceOf(Date)
    })

    it('should end a call', () => {
      const appState = AppState.getInstance()
      
      appState.startCall()
      appState.endCall()
      
      expect(appState.getActiveCall().isActive).toBe(false)
    })

    it('should add transcript segments', () => {
      const appState = AppState.getInstance()
      
      appState.startCall()
      appState.addTranscriptSegment('user', 'Hello, how are you?')
      appState.addTranscriptSegment('customer', 'I am doing well, thank you.')
      
      const segments = appState.getActiveCall().transcriptSegments
      expect(segments).toHaveLength(2)
      expect(segments[0].speaker).toBe('user')
      expect(segments[0].text).toBe('Hello, how are you?')
      expect(segments[1].speaker).toBe('customer')
      expect(segments[1].text).toBe('I am doing well, thank you.')
    })

    it('should emit call-changed event when call state changes', () => {
      const appState = AppState.getInstance()
      const mockListener = jest.fn()
      
      appState.on('call-changed', mockListener)
      appState.startCall()

      expect(mockListener).toHaveBeenCalled()
    })
  })

  describe('Suggestions', () => {
    it('should initialize with no suggestions', () => {
      const appState = AppState.getInstance()
      
      expect(appState.getSuggestions()).toEqual([])
    })

    it('should set suggestions', () => {
      const appState = AppState.getInstance()
      const suggestions = [
        {
          id: 'suggestion-1',
          text: 'Try focusing on the value proposition',
          type: 'objection-handling' as const,
          source: 'AI Assistant'
        },
        {
          id: 'suggestion-2',
          text: 'Ask about their budget constraints',
          type: 'question-suggestion' as const,
          source: 'AI Assistant'
        }
      ]
      
      appState.setSuggestions(suggestions)
      
      expect(appState.getSuggestions()).toEqual(suggestions)
    })

    it('should clear suggestions', () => {
      const appState = AppState.getInstance()
      const suggestions = [
        {
          id: 'suggestion-1',
          text: 'Test suggestion',
          type: 'information' as const,
          source: 'AI Assistant'
        }
      ]
      
      appState.setSuggestions(suggestions)
      appState.clearSuggestions()
      
      expect(appState.getSuggestions()).toEqual([])
    })

    it('should emit suggestions-changed event when suggestions change', () => {
      const appState = AppState.getInstance()
      const mockListener = jest.fn()
      const suggestions = [
        {
          id: 'suggestion-1',
          text: 'Test suggestion',
          type: 'information' as const,
          source: 'AI Assistant'
        }
      ]
      
      appState.on('suggestions-changed', mockListener)
      appState.setSuggestions(suggestions)

      expect(mockListener).toHaveBeenCalledWith(suggestions)
    })
  })

  describe('Main Window Management', () => {
    it('should set and get main window', () => {
      const appState = AppState.getInstance()
      
      appState.setMainWindow(mockWindow as BrowserWindow)
      
      expect(appState.getMainWindow()).toBe(mockWindow)
    })

    it('should return null when no main window is set', () => {
      const appState = AppState.getInstance()
      
      expect(appState.getMainWindow()).toBeNull()
    })

    it('should send IPC messages to main window when available', () => {
      const appState = AppState.getInstance()
      
      appState.setMainWindow(mockWindow as BrowserWindow)
      appState.setProcessing(true)

      expect(mockWindow.webContents.send).toHaveBeenCalledWith(
        'closezly:state-changed',
        expect.objectContaining({
          isProcessing: true
        })
      )
    })

    it('should not send IPC messages when window is destroyed', () => {
      const appState = AppState.getInstance()
      mockWindow.isDestroyed.mockReturnValue(true)
      
      appState.setMainWindow(mockWindow as BrowserWindow)
      appState.setProcessing(true)

      expect(mockWindow.webContents.send).not.toHaveBeenCalled()
    })
  })

  describe('Current Query', () => {
    it('should initialize with empty query', () => {
      const appState = AppState.getInstance()
      
      expect(appState.getCurrentQuery()).toBe('')
    })

    it('should set and get current query', () => {
      const appState = AppState.getInstance()
      
      appState.setCurrentQuery('How do I handle price objections?')
      
      expect(appState.getCurrentQuery()).toBe('How do I handle price objections?')
    })

    it('should emit query-changed event when query changes', () => {
      const appState = AppState.getInstance()
      const mockListener = jest.fn()
      
      appState.on('query-changed', mockListener)
      appState.setCurrentQuery('Test query')

      expect(mockListener).toHaveBeenCalledWith('Test query')
    })
  })

  describe('CRM Context', () => {
    it('should initialize with empty CRM context', () => {
      const appState = AppState.getInstance()
      
      expect(appState.getCRMContext()).toEqual({})
    })

    it('should set and get CRM context', () => {
      const appState = AppState.getInstance()
      const crmContext = {
        prospectName: 'John Doe',
        companyName: 'Acme Corp',
        dealStage: 'negotiation',
        dealValue: 50000
      }
      
      appState.setCRMContext(crmContext)
      
      expect(appState.getCRMContext()).toEqual(crmContext)
    })

    it('should emit crm-changed event when CRM context changes', () => {
      const appState = AppState.getInstance()
      const mockListener = jest.fn()
      const crmContext = {
        prospectName: 'Jane Smith',
        companyName: 'Tech Corp'
      }
      
      appState.on('crm-changed', mockListener)
      appState.setCRMContext(crmContext)

      expect(mockListener).toHaveBeenCalledWith(crmContext)
    })
  })
})
