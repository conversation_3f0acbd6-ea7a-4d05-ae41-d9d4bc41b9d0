import ScreenshotHelper from '../../helpers/ScreenshotHelper'
import AppState from '../../helpers/AppState'
import { desktopCapturer, systemPreferences } from 'electron'

// Mock dependencies
jest.mock('electron')
jest.mock('../../helpers/AppState')

const mockDesktopCapturer = desktopCapturer as jest.Mocked<typeof desktopCapturer>
const mockSystemPreferences = systemPreferences as jest.Mocked<typeof systemPreferences>

// Create a mock AppState instance
const mockAppStateInstance = {
  setProcessing: jest.fn(),
}

// Mock the AppState.getInstance static method
const mockAppState = AppState as jest.Mocked<typeof AppState>
mockAppState.getInstance = jest.fn().mockReturnValue(mockAppStateInstance)

describe('ScreenshotHelper', () => {
  let screenshotHelper: ScreenshotHelper

  beforeEach(() => {
    // Reset singleton
    ;(ScreenshotHelper as any).instance = undefined

    // Reset AppState mock
    mockAppState.getInstance.mockReturnValue(mockAppStateInstance)

    screenshotHelper = ScreenshotHelper.getInstance()
    jest.clearAllMocks()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = ScreenshotHelper.getInstance()
      const instance2 = ScreenshotHelper.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })

  describe('Permission Checking', () => {
    it('should check screen recording permission on macOS', async () => {
      // Mock macOS
      Object.defineProperty(process, 'platform', {
        value: 'darwin',
        configurable: true
      })

      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.checkScreenRecordingPermission()

      expect(result.granted).toBe(true)
      expect(result.status).toBe('granted')
      expect(mockSystemPreferences.getMediaAccessStatus).toHaveBeenCalledWith('screen')
    })

    it('should handle denied permission on macOS', async () => {
      Object.defineProperty(process, 'platform', {
        value: 'darwin',
        configurable: true
      })

      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('denied')

      const result = await screenshotHelper.checkScreenRecordingPermission()

      expect(result.granted).toBe(false)
      expect(result.status).toBe('denied')
      expect(result.userGuidance).toContain('System Preferences')
    })

    it('should assume granted permission on non-macOS platforms', async () => {
      Object.defineProperty(process, 'platform', {
        value: 'win32',
        configurable: true
      })

      const result = await screenshotHelper.checkScreenRecordingPermission()

      expect(result.granted).toBe(true)
      expect(result.status).toBe('granted')
    })
  })

  describe('Full Screen Capture', () => {
    it('should capture full screen successfully', async () => {
      const mockSources = [
        {
          id: 'screen:0',
          name: 'Entire Screen',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,mockImageData'),
            getSize: jest.fn(() => ({ width: 1920, height: 1080 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureFullScreen()

      expect(result).toBe('data:image/png;base64,mockImageData')
      expect(mockDesktopCapturer.getSources).toHaveBeenCalledWith({
        types: ['screen'],
        thumbnailSize: { width: 1920, height: 1080 },
        fetchWindowIcons: false
      })
      expect(mockAppStateInstance.setProcessing).toHaveBeenCalledWith(true)
      expect(mockAppStateInstance.setProcessing).toHaveBeenCalledWith(false)
    })

    it('should handle permission denied for full screen capture', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('denied')

      await expect(screenshotHelper.captureFullScreen()).rejects.toThrow(
        'Screen recording permission denied'
      )
      
      expect(mockAppStateInstance.setProcessing).toHaveBeenCalledWith(false)
    })

    it('should handle no screen sources available', async () => {
      mockDesktopCapturer.getSources.mockResolvedValue([])
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      await expect(screenshotHelper.captureFullScreen()).rejects.toThrow(
        'No screen sources available'
      )
    })
  })

  describe('Active Window Capture', () => {
    it('should capture active window successfully', async () => {
      const mockSources = [
        {
          id: 'window:123',
          name: 'Test Application',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,mockWindowData'),
            getSize: jest.fn(() => ({ width: 800, height: 600 }))
          }
        },
        {
          id: 'window:456',
          name: 'Closezly', // Should be filtered out
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,mockClosezlyData'),
            getSize: jest.fn(() => ({ width: 400, height: 300 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureActiveWindow()

      expect(result).toBe('data:image/png;base64,mockWindowData')
      expect(mockDesktopCapturer.getSources).toHaveBeenCalledWith({
        types: ['window'],
        thumbnailSize: { width: 1920, height: 1080 },
        fetchWindowIcons: false
      })
    })

    it('should filter out Closezly windows', async () => {
      const mockSources = [
        {
          id: 'window:123',
          name: 'Closezly - Main Window',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,mockClosezlyData'),
            getSize: jest.fn(() => ({ width: 400, height: 300 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureActiveWindow()

      // Should fall back to full screen when no suitable windows
      expect(result).toBe('data:image/png;base64,mockClosezlyData')
    })

    it('should handle empty thumbnail data', async () => {
      const mockSources = [
        {
          id: 'window:123',
          name: 'Test Application',
          thumbnail: null
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureActiveWindow()

      // Should fall back to full screen
      expect(mockDesktopCapturer.getSources).toHaveBeenCalledTimes(2) // Once for window, once for screen
    })
  })

  describe('Smart Capture', () => {
    it('should prefer window capture by default', async () => {
      const mockWindowSources = [
        {
          id: 'window:123',
          name: 'Test Application',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,mockWindowData'),
            getSize: jest.fn(() => ({ width: 800, height: 600 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockWindowSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureSmartScreenshot()

      expect(result).toBe('data:image/png;base64,mockWindowData')
    })

    it('should fall back to full screen when window capture fails', async () => {
      const mockScreenSources = [
        {
          id: 'screen:0',
          name: 'Entire Screen',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,mockScreenData'),
            getSize: jest.fn(() => ({ width: 1920, height: 1080 }))
          }
        }
      ]

      // First call (window) returns empty, second call (screen) returns sources
      mockDesktopCapturer.getSources
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce(mockScreenSources as any)
      
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureSmartScreenshot()

      expect(result).toBe('data:image/png;base64,mockScreenData')
    })

    it('should prefer full screen when specified', async () => {
      const mockScreenSources = [
        {
          id: 'screen:0',
          name: 'Entire Screen',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,mockScreenData'),
            getSize: jest.fn(() => ({ width: 1920, height: 1080 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockScreenSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureSmartScreenshot(false) // preferWindow = false

      expect(result).toBe('data:image/png;base64,mockScreenData')
    })

    it('should return null when both capture methods fail', async () => {
      mockDesktopCapturer.getSources.mockResolvedValue([])
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureSmartScreenshot()

      expect(result).toBeNull()
    })
  })

  describe('Error Handling', () => {
    it('should handle desktop capturer errors', async () => {
      mockDesktopCapturer.getSources.mockRejectedValue(new Error('Desktop capturer failed'))
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      await expect(screenshotHelper.captureFullScreen()).rejects.toThrow('Desktop capturer failed')
      expect(mockAppStateInstance.setProcessing).toHaveBeenCalledWith(false)
    })

    it('should handle thumbnail conversion errors', async () => {
      const mockSources = [
        {
          id: 'screen:0',
          name: 'Entire Screen',
          thumbnail: {
            toDataURL: jest.fn(() => {
              throw new Error('Thumbnail conversion failed')
            }),
            getSize: jest.fn(() => ({ width: 1920, height: 1080 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      await expect(screenshotHelper.captureFullScreen()).rejects.toThrow('Thumbnail conversion failed')
    })
  })

  describe('Image Processing', () => {
    it('should validate base64 image data format', async () => {
      const mockSources = [
        {
          id: 'screen:0',
          name: 'Entire Screen',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/png;base64,validBase64Data'),
            getSize: jest.fn(() => ({ width: 1920, height: 1080 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureFullScreen()

      expect(result).toMatch(/^data:image\/png;base64,/)
      expect(result).toContain('validBase64Data')
    })

    it('should handle different image formats', async () => {
      const mockSources = [
        {
          id: 'screen:0',
          name: 'Entire Screen',
          thumbnail: {
            toDataURL: jest.fn(() => 'data:image/jpeg;base64,jpegImageData'),
            getSize: jest.fn(() => ({ width: 1920, height: 1080 }))
          }
        }
      ]

      mockDesktopCapturer.getSources.mockResolvedValue(mockSources as any)
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const result = await screenshotHelper.captureFullScreen()

      expect(result).toMatch(/^data:image\/jpeg;base64,/)
    })
  })
})
