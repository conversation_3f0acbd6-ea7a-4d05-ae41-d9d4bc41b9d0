import AudioCaptureService from '../../helpers/AudioCaptureService'
import AppState from '../../helpers/AppState'
import { systemPreferences, BrowserWindow } from 'electron'

// Mock dependencies
jest.mock('electron')
jest.mock('../../helpers/AppState')

const mockSystemPreferences = systemPreferences as jest.Mocked<typeof systemPreferences>

describe('AudioCaptureService', () => {
  let audioCaptureService: AudioCaptureService
  let mockAppStateInstance: any
  let mockWindow: any

  beforeEach(() => {
    // Reset singleton
    ;(AudioCaptureService as any).instance = undefined

    mockWindow = {
      webContents: {
        executeJavaScript: jest.fn(),
      },
      isDestroyed: jest.fn(() => false),
    }

    mockAppStateInstance = {
      getMainWindow: jest.fn(() => mockWindow),
    }

    // Mock the AppState.getInstance static method
    const mockAppState = AppState as jest.Mocked<typeof AppState>
    mockAppState.getInstance = jest.fn().mockReturnValue(mockAppStateInstance)

    audioCaptureService = AudioCaptureService.getInstance()
    jest.clearAllMocks()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = AudioCaptureService.getInstance()
      const instance2 = AudioCaptureService.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })

  describe('Permission Checking', () => {
    it('should check microphone permissions on macOS', async () => {
      Object.defineProperty(process, 'platform', {
        value: 'darwin',
        configurable: true
      })

      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')

      const hasPermissions = await audioCaptureService.checkPermissions()

      expect(hasPermissions).toBe(true)
      expect(mockSystemPreferences.getMediaAccessStatus).toHaveBeenCalledWith('microphone')
    })

    it('should handle denied microphone permissions', async () => {
      Object.defineProperty(process, 'platform', {
        value: 'darwin',
        configurable: true
      })

      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('denied')

      const hasPermissions = await audioCaptureService.checkPermissions()

      expect(hasPermissions).toBe(false)
    })

    it('should assume granted permissions on non-macOS platforms', async () => {
      Object.defineProperty(process, 'platform', {
        value: 'win32',
        configurable: true
      })

      const hasPermissions = await audioCaptureService.checkPermissions()

      expect(hasPermissions).toBe(true)
    })
  })

  describe('Audio Capture Lifecycle', () => {
    it('should start audio capture successfully', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')
      mockWindow.webContents.executeJavaScript.mockResolvedValue(undefined)

      const result = await audioCaptureService.startCapture()

      expect(result).toBe(true)
      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('start-audio-capture')
      )
    })

    it('should not start capture if already capturing', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')
      
      // Start capture first time
      await audioCaptureService.startCapture()
      
      // Try to start again
      const result = await audioCaptureService.startCapture()

      expect(result).toBe(true)
      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledTimes(1)
    })

    it('should fail to start capture without permissions', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('denied')

      await expect(audioCaptureService.startCapture()).rejects.toThrow(
        'Microphone permissions denied'
      )
    })

    it('should stop audio capture', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')
      mockWindow.webContents.executeJavaScript.mockResolvedValue(undefined)

      // Start capture first
      await audioCaptureService.startCapture()
      
      // Then stop it
      await audioCaptureService.stopCapture()

      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('stop-audio-capture')
      )
    })

    it('should handle stop capture when not capturing', async () => {
      await audioCaptureService.stopCapture()

      // Should not throw error
      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('stop-audio-capture')
      )
    })
  })

  describe('Audio Chunk Handling', () => {
    it('should handle audio chunks from microphone', () => {
      const mockCallback = jest.fn()
      const audioChunk = {
        data: Buffer.from('audio-data'),
        timestamp: Date.now(),
        source: 'microphone' as const
      }

      audioCaptureService.startCapture({ onAudioChunk: mockCallback })
      audioCaptureService.handleAudioChunk(audioChunk)

      expect(mockCallback).toHaveBeenCalledWith(audioChunk.data)
    })

    it('should not call callback for system audio chunks', () => {
      const mockCallback = jest.fn()
      const audioChunk = {
        data: Buffer.from('audio-data'),
        timestamp: Date.now(),
        source: 'system' as const
      }

      audioCaptureService.startCapture({ onAudioChunk: mockCallback })
      audioCaptureService.handleAudioChunk(audioChunk)

      expect(mockCallback).not.toHaveBeenCalled()
    })

    it('should emit audio-chunk event', () => {
      const mockListener = jest.fn()
      const audioChunk = {
        data: Buffer.from('audio-data'),
        timestamp: Date.now(),
        source: 'microphone' as const
      }

      audioCaptureService.on('audio-chunk', mockListener)
      audioCaptureService.handleAudioChunk(audioChunk)

      expect(mockListener).toHaveBeenCalledWith(audioChunk)
    })

    it('should maintain audio buffer with time-based cleanup', () => {
      const oldChunk = {
        data: Buffer.from('old-audio'),
        timestamp: Date.now() - 35000, // 35 seconds ago
        source: 'microphone' as const
      }

      const newChunk = {
        data: Buffer.from('new-audio'),
        timestamp: Date.now(),
        source: 'microphone' as const
      }

      audioCaptureService.handleAudioChunk(oldChunk)
      audioCaptureService.handleAudioChunk(newChunk)

      const recentAudio = audioCaptureService.getRecentAudio(30000) // Last 30 seconds
      
      expect(recentAudio).toHaveLength(1)
      expect(recentAudio[0]).toEqual(newChunk)
    })
  })

  describe('Audio Buffer Management', () => {
    it('should return recent audio within time window', () => {
      const chunk1 = {
        data: Buffer.from('audio-1'),
        timestamp: Date.now() - 10000, // 10 seconds ago
        source: 'microphone' as const
      }

      const chunk2 = {
        data: Buffer.from('audio-2'),
        timestamp: Date.now() - 5000, // 5 seconds ago
        source: 'microphone' as const
      }

      audioCaptureService.handleAudioChunk(chunk1)
      audioCaptureService.handleAudioChunk(chunk2)

      const recentAudio = audioCaptureService.getRecentAudio(15000) // Last 15 seconds
      
      expect(recentAudio).toHaveLength(2)
      expect(recentAudio).toContain(chunk1)
      expect(recentAudio).toContain(chunk2)
    })

    it('should return empty array when no recent audio', () => {
      const oldChunk = {
        data: Buffer.from('old-audio'),
        timestamp: Date.now() - 35000, // 35 seconds ago
        source: 'microphone' as const
      }

      audioCaptureService.handleAudioChunk(oldChunk)

      const recentAudio = audioCaptureService.getRecentAudio(30000) // Last 30 seconds
      
      expect(recentAudio).toHaveLength(0)
    })

    it('should clear audio buffer', () => {
      const audioChunk = {
        data: Buffer.from('audio-data'),
        timestamp: Date.now(),
        source: 'microphone' as const
      }

      audioCaptureService.handleAudioChunk(audioChunk)
      audioCaptureService.clearBuffer()

      const recentAudio = audioCaptureService.getRecentAudio(60000)
      expect(recentAudio).toHaveLength(0)
    })
  })

  describe('Capture Status', () => {
    it('should return correct capture status when not capturing', () => {
      const status = audioCaptureService.getCaptureStatus()

      expect(status).toEqual({
        isCapturing: false,
        hasMicrophone: true,
        hasSystemAudio: false
      })
    })

    it('should return correct capture status when capturing', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')
      mockWindow.webContents.executeJavaScript.mockResolvedValue(undefined)

      await audioCaptureService.startCapture()

      const status = audioCaptureService.getCaptureStatus()

      expect(status).toEqual({
        isCapturing: true,
        hasMicrophone: true,
        hasSystemAudio: false
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle window not available error', async () => {
      mockAppStateInstance.getMainWindow.mockReturnValue(null)

      await expect(audioCaptureService.startCapture()).rejects.toThrow(
        'Main window not available for audio capture'
      )
    })

    it('should handle destroyed window error', async () => {
      mockWindow.isDestroyed.mockReturnValue(true)

      await expect(audioCaptureService.startCapture()).rejects.toThrow(
        'Main window not available for audio capture'
      )
    })

    it('should handle JavaScript execution errors', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')
      mockWindow.webContents.executeJavaScript.mockRejectedValue(new Error('JS execution failed'))

      await expect(audioCaptureService.startCapture()).rejects.toThrow('JS execution failed')
    })
  })

  describe('Configuration Options', () => {
    it('should use default audio capture options', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')
      mockWindow.webContents.executeJavaScript.mockResolvedValue(undefined)

      await audioCaptureService.startCapture()

      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('"sampleRate":16000')
      )
      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('"channels":1')
      )
    })

    it('should use custom audio capture options', async () => {
      mockSystemPreferences.getMediaAccessStatus.mockReturnValue('granted')
      mockWindow.webContents.executeJavaScript.mockResolvedValue(undefined)

      const customOptions = {
        sampleRate: 44100,
        channels: 2,
        chunkDuration: 500,
        enableVAD: false
      }

      await audioCaptureService.startCapture(customOptions)

      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('"sampleRate":44100')
      )
      expect(mockWindow.webContents.executeJavaScript).toHaveBeenCalledWith(
        expect.stringContaining('"channels":2')
      )
    })
  })
})
