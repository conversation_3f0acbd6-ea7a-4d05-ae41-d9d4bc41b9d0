import AuthHelper from '../../helpers/AuthHelper'
import AppState from '../../helpers/AppState'
import { shell } from 'electron'

// Mock dependencies
jest.mock('electron')
jest.mock('electron-store')
jest.mock('node-fetch')
jest.mock('../../helpers/AppState')

const mockFetch = require('node-fetch') as jest.MockedFunction<typeof fetch>
const mockShell = shell as jest.Mocked<typeof shell>

// Create a mock AppState instance
const mockAppStateInstance = {
  setAuthenticated: jest.fn(),
}

// Mock the AppState.getInstance static method
const mockAppState = AppState as jest.Mocked<typeof AppState>
mockAppState.getInstance = jest.fn().mockReturnValue(mockAppStateInstance)

describe('AuthHelper', () => {
  let authHelper: AuthHelper
  let mockStore: any

  beforeEach(() => {
    // Reset singleton
    ;(AuthHelper as any).instance = undefined

    // Mock electron-store
    mockStore = {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      clear: jest.fn(),
    }

    const MockStore = require('electron-store')
    MockStore.mockImplementation(() => mockStore)

    // Reset AppState mock
    mockAppState.getInstance.mockReturnValue(mockAppStateInstance)

    authHelper = AuthHelper.getInstance()
    jest.clearAllMocks()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = AuthHelper.getInstance()
      const instance2 = AuthHelper.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })

  describe('Token Management', () => {
    it('should store tokens securely', async () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'
      const expiresIn = 3600

      await authHelper.setTokens(accessToken, refreshToken, expiresIn)

      expect(mockStore.set).toHaveBeenCalledWith('tokens', {
        accessToken,
        refreshToken,
        expiresAt: expect.any(Number)
      })
    })

    it('should retrieve stored tokens', () => {
      const storedTokens = {
        accessToken: 'stored-access-token',
        refreshToken: 'stored-refresh-token',
        expiresAt: Date.now() + 3600000
      }
      
      mockStore.get.mockReturnValue(storedTokens)

      const tokens = authHelper.getStoredTokens()

      expect(tokens).toEqual(storedTokens)
      expect(mockStore.get).toHaveBeenCalledWith('tokens')
    })

    it('should return null when no tokens are stored', () => {
      mockStore.get.mockReturnValue(null)

      const tokens = authHelper.getStoredTokens()

      expect(tokens).toBeNull()
    })

    it('should clear stored tokens', () => {
      authHelper.clearTokens()

      expect(mockStore.delete).toHaveBeenCalledWith('tokens')
    })

    it('should check if tokens are expired', () => {
      const expiredTokens = {
        accessToken: 'expired-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() - 1000 // Expired 1 second ago
      }
      
      const validTokens = {
        accessToken: 'valid-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000 // Expires in 1 hour
      }

      expect(authHelper.isTokenExpired(expiredTokens)).toBe(true)
      expect(authHelper.isTokenExpired(validTokens)).toBe(false)
    })

    it('should consider tokens expired if expiresAt is within 5 minutes', () => {
      const soonToExpireTokens = {
        accessToken: 'soon-to-expire-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 4 * 60 * 1000 // Expires in 4 minutes
      }

      expect(authHelper.isTokenExpired(soonToExpireTokens)).toBe(true)
    })
  })

  describe('Authentication Status', () => {
    it('should return false when no tokens are stored', () => {
      mockStore.get.mockReturnValue(null)

      expect(authHelper.isAuthenticated()).toBe(false)
    })

    it('should return false when stored tokens are expired', () => {
      const expiredTokens = {
        accessToken: 'expired-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() - 1000
      }
      
      mockStore.get.mockReturnValue(expiredTokens)

      expect(authHelper.isAuthenticated()).toBe(false)
    })

    it('should return true when valid tokens are stored', () => {
      const validTokens = {
        accessToken: 'valid-token',
        refreshToken: 'refresh-token',
        expiresAt: Date.now() + 3600000
      }
      
      mockStore.get.mockReturnValue(validTokens)

      expect(authHelper.isAuthenticated()).toBe(true)
    })
  })

  describe('User Profile Fetching', () => {
    it('should fetch user profile successfully', async () => {
      const mockUserResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            full_name: 'Test User',
            username: 'testuser'
          }
        })
      }

      mockFetch.mockResolvedValue(mockUserResponse as any)

      await authHelper.fetchUserProfile('valid-access-token')

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/auth/me'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer valid-access-token'
          })
        })
      )

      const appStateInstance = mockAppState.getInstance()
      expect(appStateInstance.setAuthenticated).toHaveBeenCalledWith(true, {
        id: 'user-123',
        email: '<EMAIL>',
        fullName: 'Test User',
        username: 'testuser',
        profilePictureUrl: undefined,
        subscriptionStatus: 'free'
      })
    })

    it('should handle user profile fetch errors', async () => {
      const mockErrorResponse = {
        ok: false,
        status: 401,
        json: jest.fn().mockResolvedValue({
          error: 'Unauthorized'
        })
      }

      mockFetch.mockResolvedValue(mockErrorResponse as any)

      await authHelper.fetchUserProfile('invalid-token')

      const appStateInstance = mockAppState.getInstance()
      expect(appStateInstance.setAuthenticated).toHaveBeenCalledWith(false)
    })

    it('should handle network errors during profile fetch', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await authHelper.fetchUserProfile('valid-token')

      const appStateInstance = mockAppState.getInstance()
      expect(appStateInstance.setAuthenticated).toHaveBeenCalledWith(false)
    })
  })

  describe('Token Refresh', () => {
    it('should refresh tokens successfully', async () => {
      const mockRefreshResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          access_token: 'new-access-token',
          refresh_token: 'new-refresh-token',
          expires_in: 3600
        })
      }

      mockFetch.mockResolvedValue(mockRefreshResponse as any)

      const result = await authHelper.refreshTokens('old-refresh-token')

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/auth/refresh-token'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            refresh_token: 'old-refresh-token'
          })
        })
      )

      expect(mockStore.set).toHaveBeenCalledWith('tokens', {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresAt: expect.any(Number)
      })
    })

    it('should handle token refresh errors', async () => {
      const mockErrorResponse = {
        ok: false,
        status: 401,
        json: jest.fn().mockResolvedValue({
          error: 'Invalid refresh token'
        })
      }

      mockFetch.mockResolvedValue(mockErrorResponse as any)

      const result = await authHelper.refreshTokens('invalid-refresh-token')

      expect(result).toBe(false)
      expect(mockStore.delete).toHaveBeenCalledWith('tokens')
    })
  })

  describe('Login Flow', () => {
    it('should open login page in external browser', () => {
      authHelper.openLoginPage()

      expect(mockShell.openExternal).toHaveBeenCalledWith(
        expect.stringContaining('/login?source=desktop')
      )
    })
  })

  describe('Auth Callback Handling', () => {
    it('should handle successful auth callback', async () => {
      const callbackUrl = 'closezly://auth?access_token=new-token&refresh_token=new-refresh&expires_in=3600'

      const result = await authHelper.handleAuthCallback(callbackUrl)

      expect(result).toBe(true)
      expect(mockStore.set).toHaveBeenCalledWith('tokens', {
        accessToken: 'new-token',
        refreshToken: 'new-refresh',
        expiresAt: expect.any(Number)
      })
    })

    it('should reject invalid callback URLs', async () => {
      const invalidUrl = 'https://example.com/callback'

      const result = await authHelper.handleAuthCallback(invalidUrl)

      expect(result).toBe(false)
    })

    it('should reject callback URLs missing tokens', async () => {
      const incompleteUrl = 'closezly://auth?access_token=token-only'

      const result = await authHelper.handleAuthCallback(incompleteUrl)

      expect(result).toBe(false)
    })
  })

  describe('Logout', () => {
    it('should clear all authentication data on logout', () => {
      authHelper.logout()

      expect(mockStore.delete).toHaveBeenCalledWith('tokens')
      
      const appStateInstance = mockAppState.getInstance()
      expect(appStateInstance.setAuthenticated).toHaveBeenCalledWith(false)
    })
  })

  describe('Initialization', () => {
    it('should initialize authentication state from stored tokens', async () => {
      const validTokens = {
        accessToken: 'stored-token',
        refreshToken: 'stored-refresh',
        expiresAt: Date.now() + 3600000
      }
      
      mockStore.get.mockReturnValue(validTokens)

      // Mock successful profile fetch
      const mockUserResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          }
        })
      }
      mockFetch.mockResolvedValue(mockUserResponse as any)

      // Create new instance to trigger initialization
      ;(AuthHelper as any).instance = undefined
      const newAuthHelper = AuthHelper.getInstance()

      // Wait for async initialization
      await new Promise(resolve => setTimeout(resolve, 0))

      const appStateInstance = mockAppState.getInstance()
      expect(appStateInstance.setAuthenticated).toHaveBeenCalledWith(true, expect.any(Object))
    })
  })
})
