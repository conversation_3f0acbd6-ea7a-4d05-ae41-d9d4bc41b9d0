// Mock Electron modules for testing

export const app = {
  whenReady: jest.fn(() => Promise.resolve()),
  on: jest.fn(),
  quit: jest.fn(),
  getPath: jest.fn((name: string) => `/mock/path/${name}`),
  getVersion: jest.fn(() => '1.0.0'),
  getName: jest.fn(() => 'Closezly'),
  isReady: jest.fn(() => true),
  dock: {
    hide: jest.fn(),
    show: jest.fn(),
    setBadge: jest.fn(),
  },
}

export const BrowserWindow = jest.fn().mockImplementation(() => ({
  loadFile: jest.fn(),
  loadURL: jest.fn(),
  on: jest.fn(),
  once: jest.fn(),
  show: jest.fn(),
  hide: jest.fn(),
  close: jest.fn(),
  destroy: jest.fn(),
  focus: jest.fn(),
  blur: jest.fn(),
  minimize: jest.fn(),
  maximize: jest.fn(),
  unmaximize: jest.fn(),
  setFullScreen: jest.fn(),
  setAlwaysOnTop: jest.fn(),
  setVisibleOnAllWorkspaces: jest.fn(),
  setPosition: jest.fn(),
  setSize: jest.fn(),
  setBounds: jest.fn(),
  getBounds: jest.fn(() => ({ x: 0, y: 0, width: 800, height: 600 })),
  setOpacity: jest.fn(),
  setIgnoreMouseEvents: jest.fn(),
  setSkipTaskbar: jest.fn(),
  webContents: {
    send: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    executeJavaScript: jest.fn(),
    openDevTools: jest.fn(),
    closeDevTools: jest.fn(),
    isDevToolsOpened: jest.fn(() => false),
    session: {
      clearCache: jest.fn(),
      clearStorageData: jest.fn(),
    },
  },
  isDestroyed: jest.fn(() => false),
  isVisible: jest.fn(() => true),
  isFocused: jest.fn(() => true),
  isMinimized: jest.fn(() => false),
  isMaximized: jest.fn(() => false),
  isFullScreen: jest.fn(() => false),
}))

;(BrowserWindow as any).getAllWindows = jest.fn(() => [])
;(BrowserWindow as any).getFocusedWindow = jest.fn(() => null)

export const ipcMain = {
  handle: jest.fn(),
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
}

export const ipcRenderer = {
  invoke: jest.fn(),
  send: jest.fn(),
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
}

export const shell = {
  openExternal: jest.fn(),
  openPath: jest.fn(),
  showItemInFolder: jest.fn(),
  moveItemToTrash: jest.fn(),
}

export const screen = {
  getPrimaryDisplay: jest.fn(() => ({
    bounds: { x: 0, y: 0, width: 1920, height: 1080 },
    workArea: { x: 0, y: 0, width: 1920, height: 1040 },
    scaleFactor: 1,
  })),
  getAllDisplays: jest.fn(() => [
    {
      bounds: { x: 0, y: 0, width: 1920, height: 1080 },
      workArea: { x: 0, y: 0, width: 1920, height: 1040 },
      scaleFactor: 1,
    },
  ]),
  getDisplayNearestPoint: jest.fn(),
  getDisplayMatching: jest.fn(),
  getCursorScreenPoint: jest.fn(() => ({ x: 100, y: 100 })),
}

export const globalShortcut = {
  register: jest.fn(() => true),
  unregister: jest.fn(),
  unregisterAll: jest.fn(),
  isRegistered: jest.fn(() => false),
}

export const systemPreferences = {
  getMediaAccessStatus: jest.fn(() => 'granted'),
  askForMediaAccess: jest.fn(() => Promise.resolve(true)),
  getSystemColor: jest.fn(),
  isInvertedColorScheme: jest.fn(() => false),
  getEffectiveAppearance: jest.fn(() => 'light'),
}

export const desktopCapturer = {
  getSources: jest.fn(() => Promise.resolve([
    {
      id: 'screen:0',
      name: 'Entire Screen',
      thumbnail: {
        toDataURL: jest.fn(() => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='),
        getSize: jest.fn(() => ({ width: 1920, height: 1080 })),
      },
    },
    {
      id: 'window:123',
      name: 'Test Window',
      thumbnail: {
        toDataURL: jest.fn(() => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='),
        getSize: jest.fn(() => ({ width: 800, height: 600 })),
      },
    },
  ])),
}

export const session = {
  defaultSession: {
    clearCache: jest.fn(),
    clearStorageData: jest.fn(),
    setPermissionRequestHandler: jest.fn(),
    setPermissionCheckHandler: jest.fn(),
  },
}

export const nativeImage = {
  createFromPath: jest.fn(),
  createFromBuffer: jest.fn(),
  createFromDataURL: jest.fn(),
  createEmpty: jest.fn(),
}

export const dialog = {
  showOpenDialog: jest.fn(),
  showSaveDialog: jest.fn(),
  showMessageBox: jest.fn(),
  showErrorBox: jest.fn(),
}

export const Menu = {
  buildFromTemplate: jest.fn(),
  setApplicationMenu: jest.fn(),
  getApplicationMenu: jest.fn(),
}

export const MenuItem = jest.fn()

export const Tray = jest.fn().mockImplementation(() => ({
  setToolTip: jest.fn(),
  setImage: jest.fn(),
  setContextMenu: jest.fn(),
  destroy: jest.fn(),
}))

export const Notification = jest.fn().mockImplementation(() => ({
  show: jest.fn(),
  close: jest.fn(),
  on: jest.fn(),
}))

// Default export for ES6 imports
export default {
  app,
  BrowserWindow,
  ipcMain,
  ipcRenderer,
  shell,
  screen,
  globalShortcut,
  systemPreferences,
  desktopCapturer,
  session,
  nativeImage,
  dialog,
  Menu,
  MenuItem,
  Tray,
  Notification,
}
