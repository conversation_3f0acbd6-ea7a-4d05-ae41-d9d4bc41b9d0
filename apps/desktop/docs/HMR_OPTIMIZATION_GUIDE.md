# HMR Optimization Guide

This guide provides best practices for optimizing Hot Module Replacement (HMR) in the Closezly desktop application.

## Overview

Hot Module Replacement (HMR) allows you to update modules in a running application without a full reload. Proper HMR configuration improves development experience by:

- Preserving component state during updates
- Reducing development server restart frequency
- Providing faster feedback during development
- Maintaining application context during changes

## HMR Best Practices

### 1. Component Structure

**✅ Good:**
```tsx
import React, { memo, useCallback, useMemo } from 'react'
import { cn } from '@/lib/utils'

interface ButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary'
  className?: string
}

const Button = memo<ButtonProps>(({ 
  children, 
  onClick, 
  variant = 'primary', 
  className 
}) => {
  const handleClick = useCallback(() => {
    onClick?.()
  }, [onClick])

  const buttonClasses = useMemo(() => cn(
    'px-4 py-2 rounded-md font-medium transition-colors',
    variant === 'primary' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-900',
    className
  ), [variant, className])

  return (
    <button 
      className={buttonClasses}
      onClick={handleClick}
    >
      {children}
    </button>
  )
})

Button.displayName = 'Button'

export default Button
```

**❌ Avoid:**
```tsx
// Inline styles cause HMR issues
const Button = ({ children, onClick }) => (
  <button 
    style={{ padding: '8px 16px', backgroundColor: 'blue' }}
    onClick={() => onClick?.()}
  >
    {children}
  </button>
)

// Named export without memo
export { Button }
```

### 2. Import Optimization

**✅ Good:**
```tsx
// Specific imports
import { useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'

// Lazy loading for heavy components
const HeavyChart = lazy(() => import('./HeavyChart'))
```

**❌ Avoid:**
```tsx
// Barrel imports can cause cascading updates
import * as React from 'react'
import * as Icons from 'lucide-react'
```

### 3. CSS and Styling

**✅ Good:**
```tsx
// CSS modules or external stylesheets
import styles from './Button.module.css'

// Tailwind classes with cn utility
const className = cn('btn btn-primary', props.className)
```

**❌ Avoid:**
```tsx
// Inline styles
const style = { color: 'red', fontSize: '16px' }

// Dynamic style objects
const dynamicStyle = { 
  backgroundColor: isActive ? 'blue' : 'gray' 
}
```

### 4. State Management

**✅ Good:**
```tsx
// Stable state structure
const [state, setState] = useState({
  isLoading: false,
  data: null,
  error: null
})

// Memoized selectors
const filteredData = useMemo(() => 
  data?.filter(item => item.active), [data]
)
```

**❌ Avoid:**
```tsx
// Frequent state shape changes
const [isLoading, setIsLoading] = useState(false)
const [data, setData] = useState(null)
const [error, setError] = useState(null)
// ... many individual state variables
```

## HMR Configuration

### Vite Configuration

The project is configured with optimized HMR settings in `vite.config.ts`:

```typescript
export default defineConfig({
  plugins: [
    react({
      fastRefresh: true,
      include: "**/*.{jsx,tsx}",
      jsxRuntime: 'automatic'
    })
  ],
  server: {
    hmr: {
      overlay: true,
      port: 5174,
      timeout: 30000,
      include: ['src/**/*.{ts,tsx,js,jsx}'],
      exclude: ['src/**/*.test.*', 'src/**/__tests__/**']
    }
  }
})
```

### Component-Level HMR

For complex components, you can add HMR boundaries:

```typescript
// ComponentName.hmr.ts
if (import.meta.hot) {
  import.meta.hot.accept()
  
  import.meta.hot.accept('./ComponentName', (newModule) => {
    console.log('HMR: ComponentName updated')
  })
}

export { default } from './ComponentName'
```

## Troubleshooting HMR Issues

### Common Problems

1. **Full page reloads instead of HMR updates**
   - Check for syntax errors in components
   - Ensure components use default exports
   - Verify React Fast Refresh is enabled

2. **State loss during updates**
   - Use React.memo() for expensive components
   - Implement proper key props for lists
   - Avoid anonymous functions in JSX

3. **Cascading updates**
   - Check for circular dependencies
   - Minimize shared utility dependencies
   - Use lazy loading for heavy imports

### Debugging Tools

Use the provided scripts to analyze and optimize HMR:

```bash
# Analyze component dependencies
npm run analyze:deps

# Check HMR optimization opportunities
npm run optimize:hmr

# Create HMR boundaries for complex components
npm run hmr:boundaries

# Test development server health
npm run dev:health
```

## Performance Monitoring

Monitor HMR performance using browser DevTools:

1. Open DevTools → Network tab
2. Filter by "WS" (WebSocket) to see HMR updates
3. Check Console for HMR logs
4. Monitor Performance tab during updates

## Best Practices Summary

1. ✅ Use default exports for components
2. ✅ Wrap components with React.memo() when appropriate
3. ✅ Use useCallback() and useMemo() for optimization
4. ✅ Minimize component dependencies
5. ✅ Use CSS modules or external stylesheets
6. ✅ Implement proper error boundaries
7. ✅ Use lazy loading for heavy components
8. ❌ Avoid inline styles and dynamic style objects
9. ❌ Avoid anonymous functions in JSX
10. ❌ Avoid frequent component structure changes

Following these guidelines will ensure optimal HMR performance and a smooth development experience.
