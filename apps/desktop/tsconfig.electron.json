{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "CommonJS", "outDir": "dist/electron", "rootDir": "electron", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "composite": true, "declaration": true, "declarationMap": true}, "include": ["electron/**/*"], "exclude": ["electron/__tests__/**/*"]}