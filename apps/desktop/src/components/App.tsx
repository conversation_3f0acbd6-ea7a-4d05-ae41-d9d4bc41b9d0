import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion'; // Import motion
import Header from './Header';
// Commented out unused imports - will be used in future implementation
// import Question from './Question';
// import Analysis from './Analysis';
// import AudioWaveform from './AudioWaveform';
// import Suggestions from './Suggestions';
// import AudioInputControl from './AudioInputControl';
import RecordingFeedbackPopup from './RecordingFeedbackPopup';
import PermissionDialog from './PermissionDialog';
import PermissionModalDialog from './PermissionModalDialog';
import KnowledgeUsageIndicator from './KnowledgeUsageIndicator';
// Removed VoiceRecordingModalWindow - now using inline interface
import PermissionStatus from './PermissionStatus';
import { MainWindowOnly, ModalWindowOnly } from './ModalWindow';
import FeatureWindowRouter from './FeatureWindowRouter';
import '../utils/rendererAudioCapture'; // Initialize renderer audio capture

// ElectronAPI interface is now defined in electron.d.ts

// Define constants for window dimensions (should match main process)
const APP_COMPACT_HEIGHT = 40; // Match COMPACT_WINDOW_HEIGHT in WindowHelper.ts
const APP_EXPANDED_HEIGHT = 700; // Match EXPANDED_WINDOW_HEIGHT in WindowHelper.ts
const APP_SETTINGS_HEIGHT = 300; // Height when settings popover is open
const APP_INLINE_RECORDING_HEIGHT = 360; // Height for inline recording dropdown (header + dropdown + margin)
const APP_WIDTH = 650; // Match WINDOW_WIDTH in WindowHelper.ts

// Define interfaces for our component props and state
interface AppState {
  isAuthenticated: boolean;
  user: any | null;
  overlayVisible: boolean;
  isProcessing: boolean;
  activeCall: {
    isActive: boolean;
    startTime?: Date;
    transcriptSegments: Array<{
      speaker: 'user' | 'customer';
      text: string;
      timestamp: string;
    }>;
  };
  currentSuggestions: Array<{
    id: string;
    text: string;
    type: string;
    source?: string;
  }>;
  crmContext: any;
  currentQuery: string;
}

const App: React.FC = () => {
  // Check if this is a feature window
  const urlParams = new URLSearchParams(window.location.search)
  const featureType = urlParams.get('feature')

  // If this is a feature window, render the feature router instead
  if (featureType) {
    return <FeatureWindowRouter />
  }

  const [appState, setAppState] = useState<AppState>({
    isAuthenticated: false,
    user: null,
    overlayVisible: false,
    isProcessing: false,
    activeCall: {
      isActive: false,
      transcriptSegments: []
    },
    currentSuggestions: [],
    crmContext: {},
    currentQuery: ''
  });

  const [isBodyAreaVisible, setIsBodyAreaVisible] = useState(false);
  const [isRecordingFeedbackVisible, setIsRecordingFeedbackVisible] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  // Commented out unused state - will be used in future implementation
  // const [previousWindowHeight, setPreviousWindowHeight] = useState(APP_COMPACT_HEIGHT);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisStatusText, setAnalysisStatusText] = useState('');
  const [queryInput, setQueryInput] = useState('');
  const [showQueryInput, setShowQueryInput] = useState(false);
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [isFirstLaunch, setIsFirstLaunch] = useState(false);
  const [hasCheckedPermissions, setHasCheckedPermissions] = useState(false);
  const [hasPermissionIssues, setHasPermissionIssues] = useState(false);
  // Removed: voice recording state variables - now handled in microphone window
  const [useScreenContext, setUseScreenContext] = useState(false); // Default OFF as requested

  // RAG metadata state for knowledge usage indicators
  const [ragMetadata, setRagMetadata] = useState<{
    contextUsed?: boolean;
    sources?: Array<{
      id: string;
      content: string;
      filename: string;
      similarity: number;
    }>;
  } | null>(null);

  // Determine if the body section should be visible (based on content)
  // This existing logic can determine *what* is in the body,
  // while isBodyAreaVisible determines *if* the body area is expanded.
  // const showBodyContent = !!appState.currentQuery || appState.isProcessing || appState.currentSuggestions.length > 0 || appState.activeCall.isActive;

  // Initialize app state from the main process
  useEffect(() => {
    const initAppState = async () => {
      try {
        const state = await window.electronAPI.getAppState();
        setAppState(state);

        // Check if this is the first launch and permissions need to be set up
        await checkInitialPermissions();
      } catch (error) {
        console.error('Error initializing app state:', error);
      }
    };

    initAppState();

    // Subscribe to state updates from the main process
    const unsubscribe = window.electronAPI.onStateUpdated((state) => {
      setAppState(state);
    });

    // Subscribe to AI query triggers
    const unsubscribeTrigger = window.electronAPI.onTriggerAIQuery(() => {
      handleAskAIClick(); // Use the proper Ask AI function that handles multi-window
    });

    // Set up focus event listener to refresh user profile when app regains focus
    const handleFocus = async () => {
      if (appState.isAuthenticated && window.electronAPI && 'refreshUserProfile' in window.electronAPI) {
        try {
          await (window.electronAPI as any).refreshUserProfile();
        } catch (error) {
          console.error('Error refreshing user profile:', error);
        }
      }
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      unsubscribe();
      unsubscribeTrigger();
      window.removeEventListener('focus', handleFocus);
    };
  }, [appState.isAuthenticated]);



  // Effect to handle visibility changes triggered by main process hotkeys
  useEffect(() => {
    const handleVisibilityChangedByHotkey = (newVisibility: boolean) => {
      console.log(`[App.tsx] IPC 'closezly:visibility-changed-by-hotkey' received. New visibility: ${newVisibility}`);
      // Update React state based on main process. App.tsx no longer calls toggleVisibility itself for hotkeys.
      // The main process (ShortcutsHelper) is now the source of truth for visibility toggle via hotkey.
      // App.tsx's handleToggleVisibility is for UI button clicks.

      // If the app is now hidden by hotkey, ensure the body area is collapsed.
      if (!newVisibility) {
        setIsBodyAreaVisible(false);
      }
      // If it's shown by hotkey, App.tsx doesn't need to decide to expand the body.
      // Expansion is driven by user actions like 'Ask AI'.
    };

    const unsubscribe = window.electronAPI.onVisibilityChangedByHotkey(handleVisibilityChangedByHotkey);

    return () => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    };
  }, []); // Empty dependency array ensures this runs once on mount and cleans up on unmount

  // Note: Recording timer removed since we're now using voice-to-text queries instead of call recording

  // Check initial permissions and determine if onboarding is needed
  const checkInitialPermissions = async () => {
    if (hasCheckedPermissions) return;

    try {
      const result = await window.electronAPI.checkAllPermissions();
      if (result.success) {
        const permissionIssues = Object.values(result.results).some((p: any) => p.status !== 'granted');

        // Check if this is likely a first launch (no permissions granted)
        const allPermissionsNotDetermined = Object.values(result.results).every(
          (p: any) => p.status === 'not-determined'
        );

        if (allPermissionsNotDetermined) {
          setIsFirstLaunch(true);
        }

        // Set permission issues state
        setHasPermissionIssues(permissionIssues);
        setHasCheckedPermissions(true);
      }
    } catch (error) {
      console.error('Failed to check initial permissions:', error);
      setHasCheckedPermissions(true);
      setHasPermissionIssues(true); // Assume there are issues if we can't check
    }
  };

  // Effect to resize window when body visibility changes
  useEffect(() => {
    console.log(`[App.tsx] Window resize trigger - isBodyAreaVisible: ${isBodyAreaVisible}, isRecordingFeedbackVisible: ${isRecordingFeedbackVisible}, isSettingsOpen: ${isSettingsOpen}`);

    if (isBodyAreaVisible || isRecordingFeedbackVisible) {
      // Ensure window expands for feedback UI too
      window.electronAPI.resizeWindow(APP_WIDTH, APP_EXPANDED_HEIGHT);
    } else if (isSettingsOpen) {
      // If settings is open but body is not visible, use settings height
      window.electronAPI.resizeWindow(APP_WIDTH, APP_SETTINGS_HEIGHT);
    } else {
      window.electronAPI.resizeWindow(APP_WIDTH, APP_COMPACT_HEIGHT);
    }
  }, [isBodyAreaVisible, isRecordingFeedbackVisible, isSettingsOpen]);

  // Handle taking a screenshot and processing it
  const handleTakeScreenshot = async () => {
    try {
      const result = await window.electronAPI.takeScreenshotAndProcess();
      if (result.success) {
        console.log('Screenshot taken and processed successfully');
        if (result.suggestions && result.suggestions.length > 0) {
          console.log('AI suggestions received:', result.suggestions);
        }
      } else {
        console.error('Failed to take screenshot:', result.error);
      }
    } catch (error) {
      console.error('Error taking screenshot:', error);
    }
  };

  // Handle Ask AI click - shows the body area and triggers AI processing
  const handleAskAIClick = async () => {
    console.log('[App.tsx] handleAskAIClick called');

    // Always use multi-window mode (default behavior)
    console.log('[App.tsx] Using multi-window mode, showing Ask AI window');
    await window.electronAPI.showFeatureWindow('askAI');
    return;
  };

  // Handle Start Over - hides the body area and resets relevant state
  const handleStartOver = () => {
    console.log('[App.tsx] handleStartOver called');
    setIsBodyAreaVisible(false);
    setAppState(prev => ({
      ...prev,
      isProcessing: false,
      currentQuery: '',
      currentSuggestions: [],
      // activeCall: { ...prev.activeCall, isActive: false } // Optionally stop call on start over
    }));
    // window.electronAPI.endCall(); // Also stop call in main process if needed
    setIsAnalyzing(false);
    setAnalysisStatusText('');
    setRagMetadata(null); // Clear RAG metadata
    setIsRecordingFeedbackVisible(false);
    setShowQueryInput(false);
    setQueryInput('');
  };

  // Handle manual query submission
  const handleQuerySubmit = async () => {
    if (!queryInput.trim()) return;

    setIsAnalyzing(true);
    setAnalysisStatusText("Processing your question...");
    setShowQueryInput(false);

    try {
      const response = await window.electronAPI.processManualQuery(queryInput.trim(), useScreenContext);

      if (response.success) {
        setIsAnalyzing(false);

        // Capture RAG metadata
        setRagMetadata({
          contextUsed: response.contextUsed,
          sources: response.sources
        });

        if (response.suggestions && response.suggestions.length > 0) {
          const suggestionText = response.suggestions
            .map((s: any, index: number) => `${index + 1}. ${s.text}`)
            .join('\n\n');
          setAnalysisStatusText(`Response to "${queryInput}":\n\n${suggestionText}`);
        } else if (response.response) {
          setAnalysisStatusText(`Response to "${queryInput}":\n\n${response.response}`);
        } else {
          setAnalysisStatusText(`I've processed your question: "${queryInput}"\n\nNo specific suggestions available at this time.`);
        }
      } else {
        setIsAnalyzing(false);
        setRagMetadata(null); // Clear metadata on error
        setAnalysisStatusText(`Error processing query: ${response.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('[App.tsx] Error processing manual query:', error);
      setIsAnalyzing(false);
      setAnalysisStatusText('Error: Failed to process your question');
    }

    setQueryInput('');
  };

  // Handle showing query input
  const handleShowQueryInput = () => {
    setIsBodyAreaVisible(true);
    setShowQueryInput(true);
    setAnalysisStatusText('');
    setIsAnalyzing(false);
  };

  const handleToggleRecording = async () => {
    // Always use multi-window mode (default behavior)
    console.log('[App.tsx] handleToggleRecording called - showing microphone window');

    try {
      const result = await window.electronAPI.showFeatureWindow('microphone');
      console.log('[App.tsx] showFeatureWindow result:', result);
    } catch (error) {
      console.error('[App.tsx] Error showing microphone window:', error);
    }

    return;
  };

  // Removed: handleStartVoiceRecording - now handled in microphone window

  // Removed: handleStopVoiceRecording and handleCancelVoiceRecording - now handled in microphone window

  // Removed: handleAutoSendVoiceTranscriptChange - now handled in microphone window

  // Removed: handleSendVoiceTranscript - now handled in microphone window

  // Handle visibility toggle from Header button
  const handleToggleVisibility = () => {
    const newVisibility = window.electronAPI.toggleVisibility();
    // Main process now emits 'closezly:visibility-changed-by-hotkey'
    // which App.tsx already listens to. So, direct state update here might be redundant
    // if the event handler covers it. However, for immediate UI feedback from button:
    if (!newVisibility) {
      setIsBodyAreaVisible(false); // Collapse body if app is hidden
    }
    // setAppState(prev => ({ ...prev, overlayVisible: newVisibility })); // Reflect main process state if needed
  };

  const handleLogin = () => {
    if (window.electronAPI && 'openLoginPage' in window.electronAPI) {
      (window.electronAPI as any).openLoginPage();
    } else {
      console.log('Login action triggered, but openLoginPage method not available');
    }
  };


  const handleUpgrade = () => console.log('Upgrade action triggered');
  const handleAccount = () => console.log('Account action triggered');
  const handleQuit = () => console.log('Quit action triggered'); // This should ideally use IPC to quit app

  // Handle settings popover open/close
  const handleSettingsOpenChange = (open: boolean) => {
    console.log(`[App.tsx] Settings popover ${open ? 'opened' : 'closed'}`);
    setIsSettingsOpen(open);
  };

  // Permission dialog handlers
  const handlePermissionDialogClose = async () => {
    setShowPermissionDialog(false);
    // Close the modal window
    try {
      await window.electronAPI.closeModal('permission-dialog');
    } catch (error) {
      console.error('Failed to close permission modal:', error);
    }
  };

  const handlePermissionGranted = async () => {
    console.log('[App.tsx] Permission granted, proceeding with AI processing');
    setShowPermissionDialog(false);
    // Close the modal window
    try {
      await window.electronAPI.closeModal('permission-dialog');
    } catch (error) {
      console.error('Failed to close permission modal:', error);
    }
    // Retry the AI processing now that permissions are granted
    handleAskAIClick();
  };

  const handleOpenPermissionDialog = async () => {
    setShowPermissionDialog(true);
    // Open the modal window
    try {
      const modalOptions = {
        width: 700,
        height: 600,
        minWidth: 600,
        minHeight: 500,
        resizable: false,
        title: isFirstLaunch ? 'Welcome to Closezly AI' : 'Permissions Required',
        modal: true,
        center: true
      };
      await window.electronAPI.createModal('permission-dialog', modalOptions);
    } catch (error) {
      console.error('Failed to open permission modal:', error);
    }
  };

  // Determine what to show in the AI Response section based on state
  // Note: aiResponseContent is currently not used in the JSX, but kept for future implementation
  // TODO: Replace the motion.p with aiResponseContent when components are ready
  // let aiResponseContent = null;
  let statusMessage = '';

  if (appState.activeCall.isActive) {
    statusMessage = 'Listening...';
    // aiResponseContent = (
    //   <>
    //     <AudioInputControl
    //       isRecording={appState.activeCall.isActive}
    //       recordingTime={recordingTime}
    //       onToggleRecording={handleToggleRecording}
    //       className="mb-2 mx-auto" // Centering and margin
    //     />
    //     <AudioWaveform isActive={appState.activeCall.isActive} />
    //   </>
    // );
  } else if (appState.isProcessing) {
    statusMessage = 'Thinking...';
    // if Analysis handles its loading state well.
    // For now, the statusMessage will be displayed above.
    // aiResponseContent = <Analysis text={appState.currentQuery} isProcessing={appState.isProcessing} />;
  } else if (appState.currentQuery) {
    // If there's a query but not processing, show Question and Analysis (not loading)
    // aiResponseContent = (
    //   <>
    //     <Question text={appState.currentQuery} />
    //     <Analysis text={appState.currentQuery} isProcessing={false} />
    //   </>
    // );
  } else if (appState.currentSuggestions.length > 0) {
    // aiResponseContent = <Suggestions suggestions={appState.currentSuggestions} />;
  }

  // Prop to pass to Header to hide its mic/timer
  const hideHeaderMic = isBodyAreaVisible && appState.activeCall.isActive;

  return (
    <>
      {/* Main Window Content */}
      <MainWindowOnly>
        <div className={`flex flex-col h-screen w-full bg-transparent text-white antialiased rounded-xl shadow-2xl`}>
          <Header
                    isRecording={false} // Always false since recording happens in separate window
                    recordingTime={0}
                    onToggleRecording={handleToggleRecording}
                    onToggleVisibility={handleToggleVisibility}
                    showInlineRecording={false} // Always false since we use separate window
                    onStartVoiceRecording={() => {}} // No-op since recording happens in separate window
                    onStopVoiceRecording={() => {}} // No-op since recording happens in separate window
                    onCancelVoiceRecording={() => {}} // No-op since recording happens in separate window
                    onSendVoiceTranscript={() => {}} // No-op since recording happens in separate window
                    autoSendVoiceTranscript={false} // Not used in main window
                    onAutoSendVoiceTranscriptChange={() => {}} // No-op since recording happens in separate window
                    isVisible={appState.overlayVisible}
                    isAuthenticated={appState.isAuthenticated}
                    userSubscriptionStatus={appState.user?.subscriptionStatus || null}
                    userInitials={appState.user?.initials}
                    userEmail={appState.user?.email}
                    username={appState.user?.username}
                    profilePictureUrl={appState.user?.profilePictureUrl}
                    onLoginClick={handleLogin}
                    onUpgradeClick={handleUpgrade}
                    onAccountClick={handleAccount}
                    onAskAIClick={handleAskAIClick}
                    onStartOver={handleStartOver}
                    onQuitApp={handleQuit}
                    hideMicAndTime={hideHeaderMic}
                    onSettingsOpenChange={handleSettingsOpenChange}
                  />

          {/* Body Area - AI Response */}
          {isBodyAreaVisible && (
            <motion.div
              className="mx-4 mb-4 p-4 bg-black/40 backdrop-blur-lg border border-white/15 rounded-xl shadow-xl"
              initial={{ opacity: 0, y: 10, scale: 0.98 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.98 }}
              transition={{
                duration: 0.3,
                type: "spring",
                stiffness: 300,
                damping: 25
              }}
            >
              {showQueryInput ? (
                <div className="space-y-3">
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm text-white/80">Ask me anything about your conversation:</label>
                    <textarea
                      value={queryInput}
                      onChange={(e) => setQueryInput(e.target.value)}
                      placeholder="e.g., How should I handle their price objection?"
                      className="w-full h-16 p-3 text-sm bg-black/25 border border-white/15 text-white rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-blue-400/60 focus:border-blue-400/60 placeholder-white/50 backdrop-blur-md"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleQuerySubmit();
                        }
                      }}
                    />
                  </div>
                  {/* Screen Context Toggle */}
                  <div className="flex items-center space-x-2">
                    <label className="flex items-center space-x-2 text-sm text-white/80 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={useScreenContext}
                        onChange={(e) => setUseScreenContext(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-black/25 border-white/20 rounded focus:ring-blue-500 focus:ring-2"
                      />
                      <span>Include screen context in this follow-up</span>
                    </label>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleQuerySubmit}
                      disabled={!queryInput.trim()}
                      className="flex-1 h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Send
                    </button>
                    <button
                      onClick={() => setShowQueryInput(false)}
                      className="h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : isAnalyzing ? (
                <div className="flex flex-col items-center justify-center py-8 space-y-3">
                  <div className="w-6 h-6 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
                  <p className="text-white/80 text-center text-sm">
                    {analysisStatusText}
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* Knowledge Usage Indicator */}
                  {analysisStatusText && analysisStatusText !== "Click 'Ask AI' to analyze your screen, or ask a specific question below." && (
                    <KnowledgeUsageIndicator
                      contextUsed={ragMetadata?.contextUsed}
                      sources={ragMetadata?.sources}
                      className="mb-2"
                    />
                  )}

                  <div className="prose prose-invert max-w-none text-sm">
                    <p className="text-white/90 whitespace-pre-wrap leading-relaxed">
                      {analysisStatusText || "Click 'Ask AI' to analyze your screen, or ask a specific question below."}
                    </p>
                  </div>

                  {/* Action Buttons */}
                  {analysisStatusText ? (
                    <div className="mt-3 pt-3 border-t border-white/10">
                      <div className="flex space-x-2">
                        <button
                          onClick={handleShowQueryInput}
                          className="flex-1 h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                        >
                          Ask Follow-up
                        </button>
                        <button
                          onClick={handleAskAIClick}
                          className="flex-1 h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                        >
                          Re-analyze Screen
                        </button>
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={handleShowQueryInput}
                      className="w-full h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                    >
                      Ask a Question
                    </button>
                  )}
                </div>
              )}
            </motion.div>
          )}

          {/* Inline Voice Recording is now handled in Header component */}

          {/* Recording Feedback Popup Container & Component */}
          {isRecordingFeedbackVisible && (
            // Position this container absolutely, below header (top-10), towards the right (right-4).
            // Use flex to align its content (the popup itself).
            <div className="absolute top-10 right-4 pt-2 flex flex-col items-end z-40 pointer-events-none">
              {/* pointer-events-none on container, popup itself will have pointer-events-auto */}
              <RecordingFeedbackPopup
                statusText={analysisStatusText}
              />
            </div>
          )}

          {/* Permission Status - Show in settings or when there are permission issues */}
          {(isSettingsOpen || (hasCheckedPermissions && hasPermissionIssues && !showPermissionDialog)) && (
            <div className="absolute top-12 right-4 z-30">
              <div className="bg-black bg-opacity-75 backdrop-blur-lg rounded-lg p-3 shadow-xl">
                <PermissionStatus
                  onOpenPermissionDialog={handleOpenPermissionDialog}
                  className="w-48"
                  showLabels={true}
                  compact={false}
                />
              </div>
            </div>
          )}
        </div>
      </MainWindowOnly>

      {/* Permission Dialog - Only render in modal window */}
      <ModalWindowOnly modalId="permission-dialog">
        <PermissionModalDialog
          isOpen={true} // Always open when in modal window
          onClose={handlePermissionDialogClose}
          onPermissionGranted={handlePermissionGranted}
          requiredPermissions={['screen', 'microphone']}
          showOnboarding={isFirstLaunch}
        />
      </ModalWindowOnly>

      {/* Voice recording is now handled inline in the Header component */}
    </>
  );
};

export default App;
