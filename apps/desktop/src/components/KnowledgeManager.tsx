import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Document {
  id: string;
  title: string;
  filename: string;
  mimetype: string;
  size: number;
  uploadedAt: string;
  processedAt?: string;
  processingStatus: 'queued' | 'processing' | 'completed' | 'failed';
  chunkCount?: number;
  errorMessage?: string;
}

interface SearchResult {
  id: string;
  content: string;
  similarity: number;
  documentId: string;
  filename: string;
  chunkIndex: number;
  metadata: {
    mimetype: string;
    uploadedAt: string;
    processedAt?: string;
  };
}

interface KnowledgeManagerProps {
  isVisible: boolean;
  onClose: () => void;
}

const KnowledgeManager: React.FC<KnowledgeManagerProps> = ({ isVisible, onClose }) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [activeTab, setActiveTab] = useState<'documents' | 'search'>('documents');
  const [error, setError] = useState<string | null>(null);

  // Load documents when component becomes visible
  useEffect(() => {
    if (isVisible) {
      loadDocuments();
    }
  }, [isVisible]);

  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await window.electronAPI.getDocuments({ limit: 50 });
      
      if (response.success && response.data?.documents) {
        setDocuments(response.data.documents);
      } else {
        setError(response.error || 'Failed to load documents');
      }
    } catch (err) {
      setError('Failed to load documents');
      console.error('Error loading documents:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setIsSearching(true);
      setError(null);
      
      const response = await window.electronAPI.searchKnowledge(searchQuery, {
        limit: 10,
        threshold: 0.3
      });
      
      if (response.success && response.data?.results) {
        setSearchResults(response.data.results);
        setActiveTab('search');
      } else {
        setError(response.error || 'Search failed');
      }
    } catch (err) {
      setError('Search failed');
      console.error('Error searching knowledge:', err);
    } finally {
      setIsSearching(false);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) return;

    try {
      const response = await window.electronAPI.deleteDocument(documentId);
      
      if (response.success) {
        setDocuments(docs => docs.filter(doc => doc.id !== documentId));
      } else {
        setError(response.error || 'Failed to delete document');
      }
    } catch (err) {
      setError('Failed to delete document');
      console.error('Error deleting document:', err);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'processing': return 'text-blue-600';
      case 'queued': return 'text-yellow-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        exit={{ scale: 0.9 }}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-3/4 flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Knowledge Manager</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Search Bar */}
        <div className="p-6 border-b">
          <div className="flex gap-3">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="Search your knowledge base..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={handleSearch}
              disabled={isSearching || !searchQuery.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSearching ? 'Searching...' : 'Search'}
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('documents')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'documents'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Documents ({documents.length})
          </button>
          <button
            onClick={() => setActiveTab('search')}
            className={`px-6 py-3 font-medium transition-colors ${
              activeTab === 'search'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Search Results ({searchResults.length})
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mx-6 mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {activeTab === 'documents' && (
            <div>
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : documents.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <p>No documents found.</p>
                  <p className="text-sm mt-2">Upload documents through the web portal to get started.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {documents.map((doc) => (
                    <div key={doc.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{doc.title}</h3>
                          <p className="text-sm text-gray-600">{doc.filename}</p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span>{formatFileSize(doc.size)}</span>
                            <span>Uploaded: {formatDate(doc.uploadedAt)}</span>
                            <span className={getStatusColor(doc.processingStatus)}>
                              {doc.processingStatus}
                            </span>
                            {doc.chunkCount && (
                              <span>{doc.chunkCount} chunks</span>
                            )}
                          </div>
                        </div>
                        <button
                          onClick={() => handleDeleteDocument(doc.id)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                          title="Delete document"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'search' && (
            <div>
              {searchResults.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <p>No search results.</p>
                  <p className="text-sm mt-2">Try searching for content in your documents.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {searchResults.map((result) => (
                    <div key={result.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-medium text-gray-900">{result.filename}</h3>
                        <span className="text-sm text-blue-600 font-medium">
                          {Math.round(result.similarity * 100)}% match
                        </span>
                      </div>
                      <p className="text-gray-700 text-sm leading-relaxed">{result.content}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span>Chunk {result.chunkIndex + 1}</span>
                        <span>{result.metadata.mimetype}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Manage your knowledge base documents and search through their content.
            </p>
            <button
              onClick={() => window.electronAPI.openLoginPage()}
              className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
            >
              Open Web Portal →
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default KnowledgeManager;
