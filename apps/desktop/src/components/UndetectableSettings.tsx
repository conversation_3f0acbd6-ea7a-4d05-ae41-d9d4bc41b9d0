import React, { useState, useEffect } from 'react'
import { Badge } from "@/components/ui/badge"

export const UndetectableSettings: React.FC = () => {
  const [isProtected, setIsProtected] = useState(true)

  useEffect(() => {
    window.electronAPI.getUndetectableStatus().then(status => {
      setIsProtected(status.contentProtection)
    })



    // Listen for changes from hotkey
    const unsubscribe = window.electronAPI.onUndetectableModeChanged((enabled) => {
      setIsProtected(enabled)
    })

    return unsubscribe
  }, [])

  const handleToggle = () => {
    const newState = !isProtected
    const success = window.electronAPI.setUndetectableMode(newState)
    if (success) {
      setIsProtected(newState)
    }
  }



  return (
    <div className="max-h-64 overflow-y-auto">
      {/* Status Indicator - Only shown in settings */}
      <div className="flex items-center justify-between mb-4 p-2 bg-muted/20 rounded-sm">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{isProtected ? '🔒' : '👁️'}</span>
          <span className="text-sm text-foreground">
            {isProtected ? 'Protected' : 'Visible'}
          </span>
        </div>
        <Badge variant={isProtected ? "default" : "secondary"} className="text-xs">
          {isProtected ? 'ON' : 'OFF'}
        </Badge>
      </div>

      {/* Settings Toggle */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <label className="text-sm font-medium text-foreground cursor-pointer" onClick={handleToggle}>
              Screen Recording Protection
            </label>
            <p className="text-xs text-muted-foreground mt-1">
              When enabled, the app won't appear in screen recordings or screenshots
            </p>
          </div>
          <input
            type="checkbox"
            checked={isProtected}
            onChange={handleToggle}
            className="ml-3 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
          />
        </div>



        {/* Hotkey Reference */}
        <div className="pt-2 border-t border-muted/20">
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>Toggle Undetectable Mode</span>
            <Badge variant="secondary" className="px-1.5 py-0.5">Alt+I</Badge>
          </div>
        </div>

        {/* Limitations Notice */}
        <div className="pt-2 text-xs text-muted-foreground">
          <p className="leading-relaxed">
            <strong>Note:</strong> Works with most screen recording software (OBS, etc.).
            Some web-based tools may bypass protection.
          </p>
        </div>
      </div>
    </div>
  )
}
