/**
 * MarkdownRenderer.tsx
 * 
 * Markdown rendering component for AI responses with syntax highlighting
 * and proper typography integration.
 */

import React from 'react'
import { motion } from 'framer-motion'
import { Typography, Code } from './typography'
import { cn } from '@/lib/utils'

export interface MarkdownRendererProps {
  content: string
  className?: string
  animated?: boolean
}

// Simple markdown parser for basic formatting
interface MarkdownNode {
  type: 'text' | 'bold' | 'italic' | 'code' | 'codeblock' | 'heading' | 'list' | 'listitem' | 'paragraph'
  content: string
  children?: MarkdownNode[]
  level?: number
  language?: string
}

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className,
  animated = true
}) => {
  const nodes = parseMarkdown(content)
  
  return (
    <div className={cn("space-y-3", className)}>
      {nodes.map((node, index) => (
        <MarkdownNode 
          key={index} 
          node={node} 
          animated={animated}
          delay={animated ? index * 0.1 : 0}
        />
      ))}
    </div>
  )
}

interface MarkdownNodeProps {
  node: MarkdownNode
  animated: boolean
  delay: number
}

const MarkdownNode: React.FC<MarkdownNodeProps> = ({ node, animated, delay }) => {
  const content = animated ? (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.3 }}
    >
      {renderNode(node)}
    </motion.div>
  ) : (
    renderNode(node)
  )

  return content
}

function renderNode(node: MarkdownNode): React.ReactNode {
  switch (node.type) {
    case 'heading':
      const HeadingComponent = node.level === 1 ? 'h3' : node.level === 2 ? 'h4' : 'h4'
      return (
        <Typography 
          as={HeadingComponent}
          variant={node.level === 1 ? 'h3' : 'h4'}
          className="mt-6 mb-2 first:mt-0"
        >
          {node.content}
        </Typography>
      )
    
    case 'paragraph':
      return (
        <Typography variant="body" className="leading-relaxed">
          {node.children?.map((child, index) => (
            <span key={index}>{renderInlineNode(child)}</span>
          ))}
        </Typography>
      )
    
    case 'codeblock':
      return (
        <div className="my-4">
          <div className="bg-gray-900/50 border border-gray-700/50 rounded-lg overflow-hidden">
            {node.language && (
              <div className="px-3 py-2 bg-gray-800/50 border-b border-gray-700/50 text-xs text-gray-400">
                {node.language}
              </div>
            )}
            <pre className="p-4 overflow-x-auto">
              <code className="text-sm font-mono text-gray-200 whitespace-pre">
                {node.content}
              </code>
            </pre>
          </div>
        </div>
      )
    

    
    default:
      return (
        <Typography variant="body">
          {node.content}
        </Typography>
      )
  }
}

function renderInlineNode(node: MarkdownNode): React.ReactNode {
  switch (node.type) {
    case 'bold':
      return <strong className="font-semibold text-white">{node.content}</strong>
    
    case 'italic':
      return <em className="italic text-gray-200">{node.content}</em>
    
    case 'code':
      return <Code>{node.content}</Code>
    
    default:
      return node.content
  }
}

// Simple markdown parser
function parseMarkdown(content: string): MarkdownNode[] {
  const lines = content.split('\n')
  const nodes: MarkdownNode[] = []
  let currentParagraph: string[] = []
  
  const flushParagraph = () => {
    if (currentParagraph.length > 0) {
      const paragraphContent = currentParagraph.join(' ')
      nodes.push({
        type: 'paragraph',
        content: paragraphContent,
        children: parseInlineMarkdown(paragraphContent)
      })
      currentParagraph = []
    }
  }
  
  for (const line of lines) {
    const trimmed = line.trim()
    
    // Empty line
    if (!trimmed) {
      flushParagraph()
      continue
    }
    
    // Headings
    if (trimmed.startsWith('#')) {
      flushParagraph()
      const level = trimmed.match(/^#+/)?.[0].length || 1
      const content = trimmed.replace(/^#+\s*/, '')
      nodes.push({ type: 'heading', content, level })
      continue
    }
    
    // Code blocks
    if (trimmed.startsWith('```')) {
      flushParagraph()
      const language = trimmed.slice(3).trim()
      // Note: This is a simplified implementation
      // In a real app, you'd need to handle multi-line code blocks
      nodes.push({ type: 'codeblock', content: 'Code block', language })
      continue
    }
    
    // List items - convert to regular paragraphs to remove numbering
    if (trimmed.startsWith('- ') || trimmed.startsWith('* ') || /^\d+\.\s/.test(trimmed)) {
      flushParagraph()
      // Remove list markers and treat as regular paragraph
      const content = trimmed.replace(/^[-*]\s/, '').replace(/^\d+\.\s/, '')
      currentParagraph.push(content)
      continue
    }
    
    // Regular paragraph content
    currentParagraph.push(trimmed)
  }
  
  flushParagraph()
  return nodes
}

// Parse inline markdown (bold, italic, code)
function parseInlineMarkdown(text: string): MarkdownNode[] {
  const nodes: MarkdownNode[] = []
  let current = ''
  let i = 0
  
  while (i < text.length) {
    // Bold text
    if (text.slice(i, i + 2) === '**') {
      if (current) {
        nodes.push({ type: 'text', content: current })
        current = ''
      }
      i += 2
      const start = i
      while (i < text.length && text.slice(i, i + 2) !== '**') {
        i++
      }
      if (i < text.length) {
        nodes.push({ type: 'bold', content: text.slice(start, i) })
        i += 2
      }
      continue
    }
    
    // Italic text
    if (text[i] === '*') {
      if (current) {
        nodes.push({ type: 'text', content: current })
        current = ''
      }
      i++
      const start = i
      while (i < text.length && text[i] !== '*') {
        i++
      }
      if (i < text.length) {
        nodes.push({ type: 'italic', content: text.slice(start, i) })
        i++
      }
      continue
    }
    
    // Inline code
    if (text[i] === '`') {
      if (current) {
        nodes.push({ type: 'text', content: current })
        current = ''
      }
      i++
      const start = i
      while (i < text.length && text[i] !== '`') {
        i++
      }
      if (i < text.length) {
        nodes.push({ type: 'code', content: text.slice(start, i) })
        i++
      }
      continue
    }
    
    current += text[i]
    i++
  }
  
  if (current) {
    nodes.push({ type: 'text', content: current })
  }
  
  return nodes
}

export default MarkdownRenderer
