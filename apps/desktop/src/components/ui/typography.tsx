/**
 * Typography.tsx
 * 
 * Typography components for consistent text rendering throughout the application.
 * Provides proper text hierarchy, spacing, and styling that matches the design system.
 */

import React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

// Typography variants for consistent text styling
const typographyVariants = cva(
  "text-white", // Base text color for dark theme
  {
    variants: {
      variant: {
        h1: "text-2xl font-bold tracking-tight",
        h2: "text-xl font-semibold tracking-tight",
        h3: "text-lg font-semibold",
        h4: "text-base font-semibold",
        body: "text-sm leading-relaxed",
        caption: "text-xs text-gray-400",
        label: "text-sm font-medium text-gray-300",
        muted: "text-sm text-gray-400",
        code: "text-sm font-mono bg-gray-800/50 px-1.5 py-0.5 rounded border border-gray-600/50",
      },
      color: {
        default: "text-white",
        muted: "text-gray-400",
        accent: "text-primary",
        success: "text-green-400",
        warning: "text-yellow-400",
        error: "text-red-400",
      },
      align: {
        left: "text-left",
        center: "text-center",
        right: "text-right",
      },
    },
    defaultVariants: {
      variant: "body",
      color: "default",
      align: "left",
    },
  }
)

export interface TypographyProps
  extends Omit<React.HTMLAttributes<HTMLElement>, 'color'>,
    VariantProps<typeof typographyVariants> {
  as?: keyof JSX.IntrinsicElements
}

export const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  ({ className, variant, color, align, as, ...props }, ref) => {
    const Comp = as || getDefaultElement(variant)
    
    return React.createElement(
      Comp,
      {
        className: cn(typographyVariants({ variant, color: color as any, align, className })),
        ref: ref as any,
        ...props
      }
    )
  }
)

Typography.displayName = "Typography"

// Helper function to get default HTML element for each variant
function getDefaultElement(variant: string | null | undefined): keyof JSX.IntrinsicElements {
  switch (variant) {
    case 'h1':
    case 'h2':
    case 'h3':
    case 'h4':
      return variant as keyof JSX.IntrinsicElements
    case 'code':
      return 'code'
    case 'caption':
    case 'label':
    case 'muted':
      return 'span'
    default:
      return 'p'
  }
}

// Convenience components for common use cases
export const Heading1 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography {...props} variant="h1" as="h1" ref={ref} />
)
Heading1.displayName = "Heading1"

export const Heading2 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography {...props} variant="h2" as="h2" ref={ref} />
)
Heading2.displayName = "Heading2"

export const Heading3 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography {...props} variant="h3" as="h3" ref={ref} />
)
Heading3.displayName = "Heading3"

export const Body = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography {...props} variant="body" as="p" ref={ref} />
)
Body.displayName = "Body"

export const Caption = React.forwardRef<HTMLSpanElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography {...props} variant="caption" as="span" ref={ref} />
)
Caption.displayName = "Caption"

export const Label = React.forwardRef<HTMLSpanElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography {...props} variant="label" as="span" ref={ref} />
)
Label.displayName = "Label"

export const Code = React.forwardRef<HTMLElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography {...props} variant="code" as="code" ref={ref} />
)
Code.displayName = "Code"

// Export variants for external use
export { typographyVariants }
