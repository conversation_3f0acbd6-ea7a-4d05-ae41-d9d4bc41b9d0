/**
 * FeatureWindowRouter.tsx
 * 
 * Routes content for feature windows based on URL parameters.
 * This allows each feature window to show only its relevant content.
 */

import React, { useEffect, useState } from 'react'
import { AIResponseDisplay } from './AIResponseDisplay'
import InlineVoiceRecording from './InlineVoiceRecording'
import { UndetectableSettings } from './UndetectableSettings'
import KnowledgeUsageIndicator from './KnowledgeUsageIndicator'

interface FeatureWindowRouterProps {
  // Props will be passed from the main app state
}

const FeatureWindowRouter: React.FC<FeatureWindowRouterProps> = () => {
  const [featureType, setFeatureType] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get the feature type from URL parameters
    const urlParams = new URLSearchParams(window.location.search)
    const feature = urlParams.get('feature')
    
    console.log('[FeatureWindowRouter] Feature type:', feature)
    setFeatureType(feature)
    setIsLoading(false)
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-black/90 text-white">
        <div className="text-sm">Loading...</div>
      </div>
    )
  }

  // Render content based on feature type
  switch (featureType) {
    case 'askAI':
      return <AskAIWindow />
    
    case 'microphone':
      return <MicrophoneWindow />
    
    case 'settings':
      return <SettingsWindow />
    
    default:
      // If no feature specified, show the main app
      return null
  }
}

/**
 * Ask AI Window Component - Exact same UI as main window body area
 */
const AskAIWindow: React.FC = () => {
  // Same state as main App.tsx
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisStatusText, setAnalysisStatusText] = useState('')
  const [showQueryInput, setShowQueryInput] = useState(false) // Start collapsed like main app
  const [queryInput, setQueryInput] = useState('')
  const [useScreenContext, setUseScreenContext] = useState(false) // Default OFF like main app
  const [ragMetadata, setRagMetadata] = useState<{
    contextUsed?: boolean;
    sources?: Array<{
      id: string;
      content: string;
      filename: string;
      similarity: number;
    }>;
  } | null>(null);

  // Auto-start Ask AI processing on mount (like original behavior)
  useEffect(() => {
    handleAskAIClick() // Automatically take screenshot and process
  }, [])

  const handleQuerySubmit = async () => {
    if (!queryInput.trim()) return

    console.log('[AskAIWindow] Query submitted:', queryInput, 'Use screen context:', useScreenContext)
    setIsAnalyzing(true)
    setAnalysisStatusText('Processing your request...')

    try {
      const result = await window.electronAPI.processManualQuery(queryInput.trim(), useScreenContext)

      if (result.success) {
        setAnalysisStatusText(result.response || 'Analysis complete')
        setShowQueryInput(false)
        setRagMetadata({
          contextUsed: result.contextUsed,
          sources: result.sources // Keep original format for KnowledgeUsageIndicator
        })
      } else {
        setAnalysisStatusText(`Error: ${result.error || 'Failed to process query'}`)
      }
    } catch (error) {
      console.error('[AskAIWindow] Error processing query:', error)
      setAnalysisStatusText('Error: Failed to connect to AI service')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleStartOver = () => {
    console.log('[AskAIWindow] handleStartOver called - closing Ask AI window')
    // Close the Ask AI window (equivalent to collapsing body area in original)
    window.electronAPI.hideFeatureWindow('askAI')
  }

  const handleShowQueryInput = () => {
    setShowQueryInput(true)
    setAnalysisStatusText('')
    setIsAnalyzing(false)
  }

  const handleAskAIClick = async () => {
    console.log('[AskAIWindow] handleAskAIClick called - taking screenshot and processing')

    // Check permissions first (like original)
    try {
      const permissionResult = await window.electronAPI.checkAllPermissions();
      if (permissionResult.success) {
        const hasPermissionIssues = Object.values(permissionResult.results).some((p: any) => p.status !== 'granted');

        if (hasPermissionIssues) {
          setAnalysisStatusText('Permission Error: Please grant required permissions');
          return;
        }
      }
    } catch (error) {
      console.error('Failed to check permissions before AI processing:', error);
    }

    setIsAnalyzing(true)
    setAnalysisStatusText("Analyzing screen context...")

    try {
      // Trigger screenshot and AI processing (exact same as original)
      const response = await window.electronAPI.takeScreenshotAndProcess()

      if (response.success) {
        setIsAnalyzing(false)

        // Capture RAG metadata (exact same as original)
        setRagMetadata({
          contextUsed: response.contextUsed,
          sources: response.sources // Keep original format for KnowledgeUsageIndicator
        })

        if (response.suggestions && response.suggestions.length > 0) {
          // Display suggestions in a formatted way (exact same as original)
          const suggestionText = response.suggestions
            .map((s: any, index: number) => `${index + 1}. ${s.text}`)
            .join('\n\n');
          setAnalysisStatusText(suggestionText);
        } else if (response.response) {
          setAnalysisStatusText(response.response);
        } else {
          setAnalysisStatusText("AI analysis complete. No specific suggestions at this time.");
        }
      } else {
        setIsAnalyzing(false)
        setRagMetadata(null) // Clear metadata on error
        // Check if the error is permission-related (exact same as original)
        if (response.error && response.error.includes('permission')) {
          setAnalysisStatusText(`Permission Error: ${response.error}`);
        } else {
          setAnalysisStatusText(`Error: ${response.error || 'Failed to process screenshot'}`);
        }
      }
    } catch (error) {
      console.error('[AskAIWindow] Error in handleAskAIClick:', error)
      setIsAnalyzing(false)
      setAnalysisStatusText('Error: Failed to connect to AI service')
    }
  }

  return (
    <div className="flex flex-col h-screen w-full bg-transparent text-white antialiased">
      {/* Body Area - Exact same as main window */}
      <div className="mx-4 mb-4 p-4 bg-black/40 backdrop-blur-lg border border-white/15 rounded-xl shadow-xl">
        {showQueryInput ? (
          <div className="space-y-3">
            <div className="flex flex-col space-y-2">
              <label className="text-sm text-white/80">Ask me anything about your conversation:</label>
              <textarea
                value={queryInput}
                onChange={(e) => setQueryInput(e.target.value)}
                placeholder="e.g., How should I handle their price objection?"
                className="w-full h-16 p-3 text-sm bg-black/25 border border-white/15 text-white rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-blue-400/60 focus:border-blue-400/60 placeholder-white/50 backdrop-blur-md"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleQuerySubmit();
                  }
                }}
              />
            </div>
            {/* Screen Context Toggle */}
            <div className="flex items-center space-x-2">
              <label className="flex items-center space-x-2 text-sm text-white/80 cursor-pointer">
                <input
                  type="checkbox"
                  checked={useScreenContext}
                  onChange={(e) => setUseScreenContext(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-black/25 border-white/20 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span>Include screen context in this follow-up</span>
              </label>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleQuerySubmit}
                disabled={!queryInput.trim()}
                className="flex-1 h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Send
              </button>
              <button
                onClick={() => setShowQueryInput(false)}
                className="h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : isAnalyzing ? (
          <div className="flex flex-col items-center justify-center py-8 space-y-3">
            <div className="w-6 h-6 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
            <p className="text-white/80 text-center text-sm">
              {analysisStatusText}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Knowledge Usage Indicator */}
            {analysisStatusText && analysisStatusText !== "Click 'Ask AI' to analyze your screen, or ask a specific question below." && (
              <KnowledgeUsageIndicator
                contextUsed={ragMetadata?.contextUsed}
                sources={ragMetadata?.sources}
                className="mb-2"
              />
            )}

            <div className="prose prose-invert max-w-none text-sm">
              <p className="text-white/90 whitespace-pre-wrap leading-relaxed">
                {analysisStatusText || "Click 'Ask AI' to analyze your screen, or ask a specific question below."}
              </p>
            </div>

            {/* Action Buttons */}
            {analysisStatusText ? (
              <div className="mt-3 pt-3 border-t border-white/10">
                <div className="flex space-x-2">
                  <button
                    onClick={handleShowQueryInput}
                    className="flex-1 h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                  >
                    Ask Follow-up
                  </button>
                  <button
                    onClick={handleAskAIClick}
                    className="flex-1 h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                  >
                    Re-analyze Screen
                  </button>
                </div>
              </div>
            ) : (
              <button
                onClick={handleShowQueryInput}
                className="w-full h-8 px-3 text-sm font-medium border-white/20 bg-white/5 hover:bg-white/10 text-white/80 hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
              >
                Ask a Question
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * Microphone Window Component - Exact same UI as main window inline recording
 */
const MicrophoneWindow: React.FC = () => {
  // Same state as main App.tsx
  const [showInlineRecording, setShowInlineRecording] = useState(true) // Always visible in feature window
  const [isVoiceRecording, setIsVoiceRecording] = useState(false)
  const [autoSendVoiceTranscript, setAutoSendVoiceTranscript] = useState(true) // Default ON like main app

  // Auto-start recording when microphone window opens (like original behavior)
  useEffect(() => {
    handleStartVoiceRecording() // Automatically start recording
  }, [])

  const handleStartVoiceRecording = async () => {
    try {
      console.log('[MicrophoneWindow] Starting voice recording...')
      setIsVoiceRecording(true)

      const result = await window.electronAPI.startVoiceRecording()
      if (!result.success) {
        console.error('[MicrophoneWindow] Failed to start voice recording:', result.error)
        setIsVoiceRecording(false)
      }
    } catch (error) {
      console.error('[MicrophoneWindow] Error starting voice recording:', error)
      setIsVoiceRecording(false)
    }
  }

  const handleStopVoiceRecording = async () => {
    try {
      console.log('[MicrophoneWindow] Stopping voice recording...')
      setIsVoiceRecording(false)

      const result = await window.electronAPI.stopVoiceRecording()
      if (result.success && result.text) {
        console.log('[MicrophoneWindow] Voice recording completed:', result.text)
        handleSendVoiceTranscript(result.text)
      } else {
        console.error('[MicrophoneWindow] Failed to stop voice recording:', result.error)
      }
    } catch (error) {
      console.error('[MicrophoneWindow] Error stopping voice recording:', error)
    }
  }

  const handleCancelVoiceRecording = async () => {
    try {
      console.log('[MicrophoneWindow] Cancelling voice recording...')
      setIsVoiceRecording(false)
      setShowInlineRecording(false)

      const result = await window.electronAPI.cancelVoiceRecording()
      if (!result.success) {
        console.error('[MicrophoneWindow] Failed to cancel voice recording:', result.error)
      }
    } catch (error) {
      console.error('[MicrophoneWindow] Error cancelling voice recording:', error)
    }
  }

  const handleSendVoiceTranscript = async (transcript: string) => {
    console.log('[MicrophoneWindow] Processing voice transcript:', transcript)

    // Close the inline recording interface
    setShowInlineRecording(false)
    setIsVoiceRecording(false)

    if (autoSendVoiceTranscript) {
      // Auto-send mode: directly process the transcript with AI
      console.log('[MicrophoneWindow] Auto-sending voice transcript to AI')

      try {
        const response = await window.electronAPI.processManualQuery(transcript.trim(), false)

        if (response.success) {
          console.log('[MicrophoneWindow] Voice query processed successfully')
        } else {
          console.error('[MicrophoneWindow] Failed to process voice query:', response.error)
        }
      } catch (error) {
        console.error('[MicrophoneWindow] Error processing voice query:', error)
      }
    }
  }

  return (
    <div className="flex flex-col h-screen w-full bg-transparent text-white antialiased">
      {/* Inline Recording Area - Exact same as main window */}
      {showInlineRecording && (
        <div className="mx-4 mb-4">
          <InlineVoiceRecording
            isVisible={showInlineRecording}
            isRecording={isVoiceRecording}
            onStartRecording={handleStartVoiceRecording}
            onStopRecording={handleStopVoiceRecording}
            onCancelRecording={handleCancelVoiceRecording}
            onSendTranscript={handleSendVoiceTranscript}
            autoSend={autoSendVoiceTranscript}
            className="w-full"
          />
        </div>
      )}
    </div>
  )
}

/**
 * Settings Window Component - Exact same UI as main window settings
 */
const SettingsWindow: React.FC = () => {
  return (
    <div className="flex flex-col h-screen w-full bg-transparent text-white antialiased">
      {/* Settings Area - Exact same as main window */}
      <div className="mx-4 mb-4 p-4 bg-black/40 backdrop-blur-lg border border-white/15 rounded-xl shadow-xl">
        <UndetectableSettings />
      </div>
    </div>
  )
}

export default FeatureWindowRouter
