/**
 * AIResponseDisplay.tsx
 * 
 * Modern AI response display component with proper design system integration,
 * smooth animations, and enhanced user experience patterns.
 * 
 * Replaces the basic AI response section in App.tsx with a sophisticated
 * component that matches the design quality of InlineVoiceRecording.
 */

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Typography, Label } from '@/components/ui/typography'
import MarkdownRenderer from '@/components/ui/markdown-renderer'
import { cn } from '@/lib/utils'
import { Loader2, Copy, RotateCcw } from 'lucide-react'

// TypeScript interfaces for component props and state
export interface AIResponseDisplayProps {
  // Core state management (preserve existing functionality)
  isAnalyzing: boolean
  analysisStatusText: string
  showQueryInput: boolean
  queryInput: string
  includeScreenContext: boolean
  
  // Event handlers (preserve existing integration points)
  onQuerySubmit: (query: string, includeContext: boolean) => void
  onShowQueryInput: () => void
  onStartOver: () => void
  onQueryInputChange: (value: string) => void
  onIncludeScreenContextChange: (include: boolean) => void
  onCancelQueryInput: () => void
  
  // Enhanced functionality
  onCopyResponse?: () => void
  onRegenerateResponse?: () => void
  
  // Optional customization
  className?: string
  statusMessage?: string
}

export interface AIMessage {
  id: string
  content: string
  type: 'user' | 'ai' | 'system'
  timestamp: Date
  isError?: boolean
}

export interface AIResponseState {
  messages: AIMessage[]
  isTyping: boolean
  error: string | null
  lastResponseId: string | null
}

// Enhanced animation variants for smooth transitions
const containerVariants = {
  hidden: {
    opacity: 0,
    y: 10,
    scale: 0.95,
    filter: "blur(4px)"
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    filter: "blur(0px)",
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94] as const,
      staggerChildren: 0.1
    }
  },
  exit: {
    opacity: 0,
    y: -10,
    scale: 0.95,
    filter: "blur(4px)",
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.6, 1] as const
    }
  }
}

const messageVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
    filter: "blur(2px)"
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    filter: "blur(0px)",
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94] as const,
      staggerChildren: 0.05
    }
  }
}

// Button animation variants
const buttonVariants = {
  idle: {
    scale: 1,
    boxShadow: "0 0 0 0 rgba(59, 130, 246, 0)"
  },
  hover: {
    scale: 1.02,
    boxShadow: "0 0 0 2px rgba(59, 130, 246, 0.2)",
    transition: {
      duration: 0.2,
      ease: "easeOut" as const
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: "easeInOut" as const
    }
  }
}

// Text animation variants for typewriter effect
const textVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.02
    }
  }
}

const charVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
}

// Core component implementation
export const AIResponseDisplay: React.FC<AIResponseDisplayProps> = ({
  isAnalyzing,
  analysisStatusText,
  showQueryInput,
  queryInput,
  includeScreenContext,
  onQuerySubmit,
  onShowQueryInput,
  onStartOver,
  onQueryInputChange,
  onIncludeScreenContextChange,
  onCancelQueryInput,
  onCopyResponse,
  onRegenerateResponse,
  className,
  statusMessage
}) => {
  // Internal state for enhanced functionality
  const [internalState, setInternalState] = useState<AIResponseState>({
    messages: [],
    isTyping: false,
    error: null,
    lastResponseId: null
  })
  
  const scrollRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-scroll to bottom when new content appears
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [analysisStatusText, internalState.messages])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [queryInput])

  // Announce state changes to screen readers
  useEffect(() => {
    if (isAnalyzing) {
      // Announce when AI starts processing
      const announcement = document.createElement('div')
      announcement.setAttribute('aria-live', 'assertive')
      announcement.setAttribute('aria-atomic', 'true')
      announcement.className = 'sr-only'
      announcement.textContent = 'AI is processing your request'
      document.body.appendChild(announcement)

      setTimeout(() => {
        document.body.removeChild(announcement)
      }, 1000)
    }
  }, [isAnalyzing])

  // Announce when AI response is received
  useEffect(() => {
    if (analysisStatusText && !isAnalyzing) {
      const announcement = document.createElement('div')
      announcement.setAttribute('aria-live', 'polite')
      announcement.setAttribute('aria-atomic', 'true')
      announcement.className = 'sr-only'
      announcement.textContent = 'AI response received'
      document.body.appendChild(announcement)

      setTimeout(() => {
        document.body.removeChild(announcement)
      }, 1000)
    }
  }, [analysisStatusText, isAnalyzing])

  // Handle form submission with validation
  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault()
    if (!queryInput.trim() || isAnalyzing) return
    onQuerySubmit(queryInput.trim(), includeScreenContext)
  }

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
    if (e.key === 'Escape') {
      onCancelQueryInput()
    }
    // Ctrl/Cmd + Enter for quick submit
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleSubmit()
    }
  }

  // Global keyboard shortcuts
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when component is visible and not in input mode
      if (!showQueryInput && !isAnalyzing) {
        if (e.key === 'q' && (e.altKey || e.metaKey)) {
          e.preventDefault()
          onShowQueryInput()
        }
        if (e.key === 'c' && (e.altKey || e.metaKey) && analysisStatusText && onCopyResponse) {
          e.preventDefault()
          onCopyResponse()
        }
        if (e.key === 'r' && (e.altKey || e.metaKey) && analysisStatusText && onRegenerateResponse) {
          e.preventDefault()
          onRegenerateResponse()
        }
      }
    }

    document.addEventListener('keydown', handleGlobalKeyDown)
    return () => document.removeEventListener('keydown', handleGlobalKeyDown)
  }, [showQueryInput, isAnalyzing, analysisStatusText, onShowQueryInput, onCopyResponse, onRegenerateResponse])

  // Focus management
  useEffect(() => {
    if (showQueryInput && textareaRef.current) {
      // Focus textarea when query input mode is activated
      setTimeout(() => {
        textareaRef.current?.focus()
      }, 100)
    }
  }, [showQueryInput])

  return (
    <motion.div
      layout
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      className={cn(
        // Base styling with dynamic sizing
        "p-4 pt-3 bg-black/40 backdrop-blur-lg border border-white/15",
        "rounded-xl shadow-xl overflow-hidden mt-4",
        "flex flex-col relative w-full",
        className
      )}
      style={{
        background: "linear-gradient(135deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.3) 100%)",
        minHeight: showQueryInput ? "280px" : isAnalyzing ? "320px" : analysisStatusText ? "200px" : "120px"
      }}
      transition={{
        layout: {
          duration: 0.4,
          ease: [0.25, 0.46, 0.45, 0.94]
        }
      }}
      role="region"
      aria-label="AI Response Display"
      aria-live="polite"
      aria-busy={isAnalyzing}
    >
      {/* Screen Reader Status Region */}
      <div
        id="ai-response-status"
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
      >
        {isAnalyzing ? 'AI is processing your request' :
         analysisStatusText ? 'AI response is ready' :
         showQueryInput ? 'Question input is active' :
         'AI response area is ready'}
      </div>

      {/* Header Section */}
      <AIResponseHeader
        statusMessage={statusMessage}
        isAnalyzing={isAnalyzing}
      />

      {/* Skip Links for Screen Readers */}
      <div className="sr-only">
        <a
          href="#ai-response-content"
          className="focus:not-sr-only focus:absolute focus:top-2 focus:left-2 focus:z-50 focus:px-2 focus:py-1 focus:bg-primary focus:text-white focus:rounded"
        >
          Skip to AI response content
        </a>
        {showQueryInput && (
          <a
            href="#query-input"
            className="focus:not-sr-only focus:absolute focus:top-2 focus:left-32 focus:z-50 focus:px-2 focus:py-1 focus:bg-primary focus:text-white focus:rounded"
          >
            Skip to question input
          </a>
        )}
      </div>

      {/* Main Content Area */}
      <div
        ref={scrollRef}
        className="flex-1 overflow-y-auto"
        id="ai-response-content"
        tabIndex={-1}
      >
        <AnimatePresence mode="wait">
          {showQueryInput ? (
            <AIQueryInputMode
              key="query-input"
              queryInput={queryInput}
              includeScreenContext={includeScreenContext}
              isAnalyzing={isAnalyzing}
              onQueryInputChange={onQueryInputChange}
              onIncludeScreenContextChange={onIncludeScreenContextChange}
              onSubmit={handleSubmit}
              onCancel={onCancelQueryInput}
              onKeyDown={handleKeyDown}
              textareaRef={textareaRef}
            />
          ) : isAnalyzing ? (
            <AILoadingMode
              key="loading"
              statusText={analysisStatusText}
            />
          ) : (
            <AIResponseMode
              key="response"
              analysisStatusText={analysisStatusText}
              onShowQueryInput={onShowQueryInput}
              onStartOver={onStartOver}
              onCopyResponse={onCopyResponse}
              onRegenerateResponse={onRegenerateResponse}
            />
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}

// Header component with status indicators
interface AIResponseHeaderProps {
  statusMessage?: string
  isAnalyzing: boolean
}

const AIResponseHeader: React.FC<AIResponseHeaderProps> = ({
  statusMessage,
  isAnalyzing
}) => (
  <div className="flex items-center justify-between mb-3">
    <div className="flex items-center space-x-2">
      <Typography variant="h4" className="text-gray-300">AI Response</Typography>
      {isAnalyzing && (
        <Badge variant="secondary" className="text-xs flex items-center space-x-1">
          <Loader2 className="w-3 h-3 animate-spin" />
          <span>Processing...</span>
        </Badge>
      )}
    </div>
    {statusMessage && (
      <span className="text-xs text-gray-400 italic">{statusMessage}</span>
    )}
  </div>
)

// Query Input Mode Component with proper shadcn Button integration
interface AIQueryInputModeProps {
  queryInput: string
  includeScreenContext: boolean
  isAnalyzing: boolean
  onQueryInputChange: (value: string) => void
  onIncludeScreenContextChange: (include: boolean) => void
  onSubmit: () => void
  onCancel: () => void
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
  textareaRef: React.RefObject<HTMLTextAreaElement>
}

const AIQueryInputMode: React.FC<AIQueryInputModeProps> = ({
  queryInput,
  includeScreenContext,
  isAnalyzing,
  onQueryInputChange,
  onIncludeScreenContextChange,
  onSubmit,
  onCancel,
  onKeyDown,
  textareaRef
}) => (
  <motion.div
    variants={messageVariants}
    initial="hidden"
    animate="visible"
    exit="hidden"
    className="space-y-3"
  >
    <div className="flex flex-col space-y-3">
      <Label>
        Ask me anything about your sales conversation:
      </Label>

      <motion.textarea
        ref={textareaRef}
        id="query-input"
        value={queryInput}
        onChange={(e) => onQueryInputChange(e.target.value)}
        onKeyDown={onKeyDown}
        placeholder="e.g., How should I handle their price objection?"
        className={cn(
          "w-full p-3 bg-gray-800/50 border border-gray-600/50 rounded-lg",
          "text-white placeholder-gray-400 resize-none",
          "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
          "backdrop-blur-sm transition-all duration-200"
        )}
        rows={3}
        disabled={isAnalyzing}
        aria-label="Ask AI a question about your sales conversation"
        aria-describedby="query-help-text keyboard-shortcuts"
        aria-invalid={false}
        aria-required="true"
        initial={{ scale: 1, borderColor: "rgba(75, 85, 99, 0.5)" }}
        whileFocus={{
          scale: 1.01,
          borderColor: "rgba(59, 130, 246, 0.5)",
          boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)"
        }}
        transition={{ duration: 0.2, ease: "easeOut" }}
      />
      <div id="query-help-text" className="sr-only">
        Enter your question and press Enter to send, or Escape to cancel
      </div>
      <div id="keyboard-shortcuts" className="sr-only">
        Keyboard shortcuts: Enter to send, Shift+Enter for new line, Escape to cancel, Ctrl+Enter or Cmd+Enter for quick send
      </div>

      {/* Screen Context Toggle with Animation */}
      <motion.div
        className="flex items-center space-x-2 p-3 bg-gray-800/30 rounded-lg border border-gray-600/30"
        whileHover={{
          backgroundColor: "rgba(31, 41, 55, 0.4)",
          borderColor: "rgba(75, 85, 99, 0.4)"
        }}
        transition={{ duration: 0.2 }}
      >
        <motion.input
          type="checkbox"
          id="includeScreenContext"
          checked={includeScreenContext}
          onChange={(e) => onIncludeScreenContextChange(e.target.checked)}
          className="w-4 h-4 text-primary bg-gray-700 border-gray-600 rounded focus:ring-primary focus:ring-2"
          disabled={isAnalyzing}
          aria-describedby="screenshot-help"
          aria-label="Include screen context with your question"
          whileTap={{ scale: 0.9 }}
          transition={{ duration: 0.1 }}
        />
        <div id="screenshot-help" className="sr-only">
          When enabled, a screenshot will be taken and included with your question to provide visual context to the AI
        </div>
        <label htmlFor="includeScreenContext" className="cursor-pointer text-sm font-medium text-white">
          Include screen context (takes screenshot)
        </label>
        <div className="ml-auto">
          <motion.div
            initial={false}
            animate={{
              scale: includeScreenContext ? 1.05 : 1,
              rotate: includeScreenContext ? [0, 5, -5, 0] : 0
            }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <Badge variant={includeScreenContext ? "default" : "secondary"} className="text-xs">
              {includeScreenContext ? '📸 Screenshot' : '💬 Text-only'}
            </Badge>
          </motion.div>
        </div>
      </motion.div>
    </div>

    {/* Clean Action Buttons */}
    <div className="flex space-x-2">
      <motion.div
        variants={buttonVariants}
        initial="idle"
        whileHover="hover"
        whileTap="tap"
        className="flex-1"
      >
        <Button
          onClick={onSubmit}
          disabled={!queryInput.trim() || isAnalyzing}
          size="sm"
          className="w-full"
          aria-label={isAnalyzing ? "Processing your question" : "Send question to AI"}
          aria-describedby={isAnalyzing ? "processing-status" : undefined}
        >
          {isAnalyzing ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin mr-2" aria-hidden="true" />
              <span id="processing-status">Processing...</span>
            </>
          ) : (
            'Send'
          )}
        </Button>
      </motion.div>

      <motion.div
        variants={buttonVariants}
        initial="idle"
        whileHover="hover"
        whileTap="tap"
      >
        <Button
          onClick={onCancel}
          variant="outline"
          size="sm"
          disabled={isAnalyzing}
        >
          Cancel
        </Button>
      </motion.div>
    </div>
  </motion.div>
)

// Loading Mode Component with advanced animations
interface AILoadingModeProps {
  statusText: string
}

const AILoadingMode: React.FC<AILoadingModeProps> = ({ statusText }) => (
  <motion.div
    variants={messageVariants}
    initial="hidden"
    animate="visible"
    exit="hidden"
    className="flex flex-col items-center justify-center py-8 space-y-6"
    role="status"
    aria-live="polite"
    aria-label="AI is processing your request"
  >


    {/* Simple Status Text */}
    <div className="flex flex-col items-center space-y-3" role="status" aria-label="AI is thinking">
      <Typography variant="body" color="muted" className="text-center">
        AI is thinking...
      </Typography>
    </div>

    {/* Status Text with proper typography */}
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3, duration: 0.4 }}
      className="text-center max-w-xs"
    >
      <Typography variant="body" color="muted" align="center">
        {statusText}
      </Typography>
    </motion.div>

    {/* Simple Progress bars */}
    <div className="w-full max-w-xs space-y-2">
      {[85, 70, 60].map((width, index) => (
        <motion.div
          key={index}
          className="h-1 bg-white/10 rounded-full overflow-hidden"
          style={{ width: `${width}%` }}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 + index * 0.1, duration: 0.4 }}
        >
          <motion.div
            className="h-full bg-primary/60 rounded-full"
            animate={{
              x: ['-100%', '100%']
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: index * 0.3,
              ease: "easeInOut"
            }}
          />
        </motion.div>
      ))}
    </div>
  </motion.div>
)

// Response Mode Component with proper shadcn Button integration
interface AIResponseModeProps {
  analysisStatusText: string
  onShowQueryInput: () => void
  onStartOver: () => void
  onCopyResponse?: () => void
  onRegenerateResponse?: () => void
}

const AIResponseMode: React.FC<AIResponseModeProps> = ({
  analysisStatusText,
  onShowQueryInput,
  onStartOver,
  onCopyResponse,
  onRegenerateResponse
}) => (
  <motion.div
    variants={messageVariants}
    initial="hidden"
    animate="visible"
    exit="hidden"
    className="space-y-6"
  >
    {/* Show AI Response when available */}
    {analysisStatusText ? (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.6,
          ease: [0.25, 0.46, 0.45, 0.94],
          staggerChildren: 0.1
        }}
        className="space-y-4"
      >
        <MarkdownRenderer
          content={analysisStatusText}
          animated={true}
        />
      </motion.div>
    ) : (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        className="text-center py-4"
      >
        <Typography variant="h3" color="default" align="center" className="mb-2">
          Ready to Assist
        </Typography>
        <Typography variant="body" color="muted" align="center">
          Ask me anything about your screen or start a conversation
        </Typography>
      </motion.div>
    )}

    {/* Clean Follow-up Action Buttons */}
    {analysisStatusText && (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.3 }}
        className="flex flex-wrap gap-2 pt-3 border-t border-gray-600/50"
      >
        <motion.div
          variants={buttonVariants}
          initial="idle"
          whileHover="hover"
          whileTap="tap"
          className="flex-1 min-w-[120px]"
        >
          <Button
            onClick={onShowQueryInput}
            variant="default"
            size="sm"
            className="w-full"
            aria-label="Ask a follow-up question to the AI"
          >
            Ask Follow-up
          </Button>
        </motion.div>

        <motion.div
          variants={buttonVariants}
          initial="idle"
          whileHover="hover"
          whileTap="tap"
          className="flex-1 min-w-[120px]"
        >
          <Button
            onClick={onStartOver}
            variant="secondary"
            size="sm"
            className="w-full"
            aria-label="Take a new screenshot and re-analyze the screen"
          >
            Re-analyze Screen
          </Button>
        </motion.div>

        {onCopyResponse && (
          <motion.div
            variants={buttonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
          >
            <Button
              onClick={onCopyResponse}
              variant="ghost"
              size="sm"
              className="px-3"
              aria-label="Copy AI response to clipboard"
            >
              <Copy className="w-4 h-4 mr-1" aria-hidden="true" />
              Copy
            </Button>
          </motion.div>
        )}

        {onRegenerateResponse && (
          <motion.div
            variants={buttonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
          >
            <Button
              onClick={onRegenerateResponse}
              variant="ghost"
              size="sm"
              className="px-3"
              aria-label="Generate a new AI response"
            >
              <RotateCcw className="w-4 h-4 mr-1" aria-hidden="true" />
              Regenerate
            </Button>
          </motion.div>
        )}
      </motion.div>
    )}

    {/* Clean Initial State Button */}
    {!analysisStatusText && (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.3 }}
      >
        <motion.div
          variants={buttonVariants}
          initial="idle"
          whileHover="hover"
          whileTap="tap"
        >
          <Button
            onClick={onShowQueryInput}
            variant="default"
            size="sm"
            className="w-full"
          >
            Ask a Question
          </Button>
        </motion.div>
      </motion.div>
    )}
  </motion.div>
)

export default AIResponseDisplay
