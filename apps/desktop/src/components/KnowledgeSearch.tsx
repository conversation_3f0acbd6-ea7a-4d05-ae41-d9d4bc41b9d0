import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SearchResult {
  id: string;
  content: string;
  similarity: number;
  documentId: string;
  filename: string;
  chunkIndex: number;
  metadata: {
    mimetype: string;
    uploadedAt: string;
    processedAt?: string;
  };
}

interface KnowledgeSearchProps {
  isVisible: boolean;
  onClose: () => void;
  onResultSelect?: (result: SearchResult) => void;
  placeholder?: string;
  maxResults?: number;
}

const KnowledgeSearch: React.FC<KnowledgeSearchProps> = ({ 
  isVisible, 
  onClose, 
  onResultSelect,
  placeholder = "Search your knowledge base...",
  maxResults = 5
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setIsSearching(true);
      setError(null);
      
      const response = await window.electronAPI.searchKnowledge(searchQuery, {
        limit: maxResults,
        threshold: 0.3
      });
      
      if (response.success && response.data?.results) {
        setSearchResults(response.data.results);
      } else {
        setError(response.error || 'Search failed');
        setSearchResults([]);
      }
    } catch (err) {
      setError('Search failed');
      setSearchResults([]);
      console.error('Error searching knowledge:', err);
    } finally {
      setIsSearching(false);
    }
  };

  const handleResultClick = (result: SearchResult) => {
    if (onResultSelect) {
      onResultSelect(result);
    }
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setError(null);
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-96 overflow-hidden"
    >
      {/* Search Input */}
      <div className="p-4 border-b border-gray-100">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            className="w-full px-4 py-2 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            autoFocus
          />
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1">
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="Clear search"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            <button
              onClick={handleSearch}
              disabled={isSearching || !searchQuery.trim()}
              className="p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Search"
            >
              {isSearching ? (
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="max-h-80 overflow-y-auto">
        {error && (
          <div className="p-4 text-red-600 text-sm border-b border-gray-100">
            {error}
          </div>
        )}

        {searchResults.length === 0 && !error && searchQuery && !isSearching && (
          <div className="p-4 text-gray-500 text-sm text-center">
            No results found for "{searchQuery}"
          </div>
        )}

        {searchResults.length === 0 && !searchQuery && (
          <div className="p-4 text-gray-500 text-sm text-center">
            Enter a search query to find relevant content from your documents
          </div>
        )}

        <AnimatePresence>
          {searchResults.map((result, index) => (
            <motion.div
              key={result.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ delay: index * 0.05 }}
              className="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
              onClick={() => handleResultClick(result)}
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-medium text-gray-900 text-sm">{result.filename}</h4>
                <span className="text-xs text-blue-600 font-medium ml-2 flex-shrink-0">
                  {Math.round(result.similarity * 100)}%
                </span>
              </div>
              <p className="text-gray-700 text-sm leading-relaxed line-clamp-3">
                {result.content}
              </p>
              <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                <span>Chunk {result.chunkIndex + 1}</span>
                <span>{result.metadata.mimetype}</span>
                <span>{new Date(result.metadata.uploadedAt).toLocaleDateString()}</span>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Footer */}
      {searchResults.length > 0 && (
        <div className="p-3 bg-gray-50 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span>Found {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}</span>
            <span>Click a result to use it</span>
          </div>
        </div>
      )}

      {/* Keyboard shortcuts hint */}
      <div className="p-2 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
          <span><kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Enter</kbd> to search</span>
          <span><kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Esc</kbd> to close</span>
        </div>
      </div>
    </motion.div>
  );
};

export default KnowledgeSearch;
