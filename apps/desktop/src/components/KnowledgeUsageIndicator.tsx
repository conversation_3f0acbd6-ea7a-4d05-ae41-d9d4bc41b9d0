import React from 'react';
import { motion } from 'framer-motion';

interface KnowledgeUsageIndicatorProps {
  contextUsed?: boolean;
  sources?: Array<{
    id: string;
    content: string;
    filename: string;
    similarity: number;
  }>;
  className?: string;
}

const KnowledgeUsageIndicator: React.FC<KnowledgeUsageIndicatorProps> = ({
  contextUsed,
  sources,
  className = ''
}) => {
  // Always show indicator for AI responses
  // If contextUsed is undefined, assume no RAG was used (red indicator)
  const isKnowledgeUsed = contextUsed === true && sources && sources.length > 0;
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`flex items-center space-x-2 ${className}`}
    >
      {/* Knowledge Usage Indicator */}
      <div className="flex items-center space-x-2">
        <div
          className={`w-3 h-3 rounded-full flex items-center justify-center ${
            isKnowledgeUsed 
              ? 'bg-green-500 shadow-green-500/50 shadow-lg' 
              : 'bg-red-500 shadow-red-500/50 shadow-lg'
          }`}
          title={isKnowledgeUsed 
            ? `Knowledge used from ${sources?.length || 0} document(s)` 
            : 'Standard LLM response (no knowledge used)'
          }
        >
          {/* Inner glow effect */}
          <div
            className={`w-1.5 h-1.5 rounded-full ${
              isKnowledgeUsed ? 'bg-green-300' : 'bg-red-300'
            }`}
          />
        </div>
        
        <span className="text-xs text-gray-400 font-medium">
          {isKnowledgeUsed ? 'Knowledge Enhanced' : 'Standard Response'}
        </span>
      </div>

      {/* Sources tooltip/info */}
      {isKnowledgeUsed && sources && sources.length > 0 && (
        <div className="group relative">
          <div className="text-xs text-blue-400 cursor-help hover:text-blue-300 transition-colors">
            ({sources.length} source{sources.length !== 1 ? 's' : ''})
          </div>
          
          {/* Tooltip with source details - Positioned below */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 hidden group-hover:block z-[9999]">
            <div className="bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-xl min-w-80 max-w-96">
              {/* Arrow pointer at top */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-600"></div>

              <div className="text-xs font-semibold text-gray-300 mb-2">
                Knowledge Sources ({sources.length}):
              </div>
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {sources.map((source, index) => (
                  <div key={source.id} className="text-xs border-b border-gray-700 pb-2 last:border-b-0">
                    <div className="text-blue-400 font-medium mb-1">
                      {index + 1}. {source.filename}
                    </div>
                    <div className="text-gray-400 text-xs mb-1">
                      Similarity: {Math.round(source.similarity * 100)}%
                    </div>
                    <div className="text-gray-300 text-xs leading-relaxed">
                      {source.content.substring(0, 150)}...
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default KnowledgeUsageIndicator;
