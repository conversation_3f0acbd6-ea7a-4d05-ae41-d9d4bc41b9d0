/**
 * audio-processor-worklet.js
 * 
 * AudioWorklet processor for real-time audio capture in the Closezly desktop app.
 * This runs in the audio worklet thread and processes audio chunks efficiently.
 */

class AudioProcessorWorklet extends AudioWorkletProcessor {
  constructor(options) {
    super()
    
    // Get options from the processor options
    this.bufferSize = options.processorOptions?.bufferSize || 4096
    this.sampleRate = options.processorOptions?.sampleRate || 16000
    this.channels = options.processorOptions?.channels || 1
    this.source = options.processorOptions?.source || 'microphone'
    
    // Buffer for accumulating audio samples
    this.buffer = new Float32Array(this.bufferSize)
    this.bufferIndex = 0
    
    console.log(`[AudioWorklet] Initialized processor for ${this.source} with buffer size ${this.bufferSize}`)
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0]
    
    // If no input, return true to keep processor alive
    if (!input || input.length === 0) {
      return true
    }
    
    const inputChannel = input[0] // Get first channel
    
    // If no audio data, return true to keep processor alive
    if (!inputChannel || inputChannel.length === 0) {
      return true
    }
    
    // Process each sample in the input
    for (let i = 0; i < inputChannel.length; i++) {
      this.buffer[this.bufferIndex] = inputChannel[i]
      this.bufferIndex++
      
      // When buffer is full, send it to main thread
      if (this.bufferIndex >= this.bufferSize) {
        this.sendAudioChunk()
        this.bufferIndex = 0
      }
    }
    
    // Keep the processor alive
    return true
  }
  
  sendAudioChunk() {
    try {
      // Convert Float32Array to 16-bit PCM ArrayBuffer
      const pcmBuffer = new ArrayBuffer(this.bufferSize * 2) // 2 bytes per sample
      const pcmView = new DataView(pcmBuffer)
      
      for (let i = 0; i < this.bufferSize; i++) {
        // Convert float sample (-1 to 1) to 16-bit integer (-32768 to 32767)
        const sample = Math.max(-1, Math.min(1, this.buffer[i]))
        const pcmSample = Math.round(sample * 32767)
        pcmView.setInt16(i * 2, pcmSample, true) // little endian
      }
      
      // Send audio chunk to main thread
      this.port.postMessage({
        type: 'audioChunk',
        data: pcmBuffer,
        timestamp: Date.now(),
        source: this.source
      })
      
    } catch (error) {
      console.error('[AudioWorklet] Error sending audio chunk:', error)
    }
  }
}

// Register the processor
registerProcessor('audio-processor-worklet', AudioProcessorWorklet)
