#!/usr/bin/env node

/**
 * Development Process Cleanup Script
 * 
 * Cleans up stale development processes that might interfere
 * with starting fresh development servers.
 */

const { execSync, spawn } = require('child_process');
const path = require('path');

const PORTS_TO_CHECK = [5173, 5174]; // Vite and HMR ports
const PROJECT_PATH = path.resolve(__dirname, '../..');

function log(message) {
  console.log(`[Cleanup] ${message}`);
}

function executeCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    return { success: true, output: result };
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout };
  }
}

function findProcessesOnPorts() {
  const processes = [];
  
  for (const port of PORTS_TO_CHECK) {
    const result = executeCommand(`lsof -i :${port}`, { silent: true });
    if (result.success && result.output.trim()) {
      const lines = result.output.split('\n').slice(1); // Skip header
      for (const line of lines) {
        if (line.trim()) {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 2) {
            processes.push({
              port,
              pid: parts[1],
              command: parts[0],
              line: line.trim()
            });
          }
        }
      }
    }
  }
  
  return processes;
}

function findProjectProcesses() {
  const processes = [];
  const currentPid = process.pid;
  const parentPid = process.ppid;

  // Find Node.js processes related to this project
  const result = executeCommand('ps aux | grep node', { silent: true });
  if (result.success) {
    const lines = result.output.split('\n');
    for (const line of lines) {
      if (line.includes(PROJECT_PATH) ||
          line.includes('vite') ||
          line.includes('electron') ||
          line.includes('concurrently') ||
          line.includes('tsc --watch')) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 2) {
          const pid = parseInt(parts[1]);

          // Skip current process, parent process, and cleanup script
          if (pid === currentPid ||
              pid === parentPid ||
              line.includes('cleanup-dev-processes.js') ||
              line.includes('pnpm run electron:dev')) {
            continue;
          }

          processes.push({
            pid: parts[1],
            command: parts.slice(10).join(' '),
            line: line.trim()
          });
        }
      }
    }
  }

  return processes;
}

function killProcess(pid, signal = 'TERM') {
  try {
    process.kill(parseInt(pid), signal);
    return true;
  } catch (error) {
    return false;
  }
}

function killProcesses(processes, signal = 'TERM') {
  const killed = [];
  const failed = [];
  
  for (const proc of processes) {
    log(`Attempting to kill process ${proc.pid} (${proc.command || 'unknown'})`);
    if (killProcess(proc.pid, signal)) {
      killed.push(proc);
    } else {
      failed.push(proc);
    }
  }
  
  return { killed, failed };
}

function waitForProcessesToDie(pids, timeout = 5000) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    const checkInterval = 100;
    
    const check = () => {
      const stillRunning = pids.filter(pid => {
        try {
          process.kill(parseInt(pid), 0); // Check if process exists
          return true;
        } catch {
          return false;
        }
      });
      
      if (stillRunning.length === 0) {
        resolve({ success: true, stillRunning: [] });
      } else if (Date.now() - startTime > timeout) {
        resolve({ success: false, stillRunning });
      } else {
        setTimeout(check, checkInterval);
      }
    };
    
    check();
  });
}

async function cleanup(force = false) {
  log('🧹 Starting development process cleanup...');
  
  // Find processes on development ports
  const portProcesses = findProcessesOnPorts();
  if (portProcesses.length > 0) {
    log(`Found ${portProcesses.length} processes on development ports:`);
    portProcesses.forEach(proc => {
      log(`  Port ${proc.port}: PID ${proc.pid} (${proc.command})`);
    });
  }
  
  // Find project-related processes
  const projectProcesses = findProjectProcesses();
  if (projectProcesses.length > 0) {
    log(`Found ${projectProcesses.length} project-related processes:`);
    projectProcesses.forEach(proc => {
      log(`  PID ${proc.pid}: ${proc.command.substring(0, 80)}...`);
    });
  }
  
  const allProcesses = [...portProcesses, ...projectProcesses];
  
  // Remove duplicates by PID
  const uniqueProcesses = allProcesses.filter((proc, index, arr) => 
    arr.findIndex(p => p.pid === proc.pid) === index
  );
  
  if (uniqueProcesses.length === 0) {
    log('✅ No stale processes found');
    return true;
  }
  
  // Kill processes gracefully first
  log(`🛑 Terminating ${uniqueProcesses.length} processes gracefully...`);
  const { killed, failed } = killProcesses(uniqueProcesses, 'TERM');
  
  if (killed.length > 0) {
    log(`✅ Sent TERM signal to ${killed.length} processes`);
    
    // Wait for processes to die
    const pids = killed.map(p => p.pid);
    const waitResult = await waitForProcessesToDie(pids, 3000);
    
    if (!waitResult.success && waitResult.stillRunning.length > 0) {
      if (force) {
        log(`💀 Force killing ${waitResult.stillRunning.length} stubborn processes...`);
        const stubborn = uniqueProcesses.filter(p => waitResult.stillRunning.includes(p.pid));
        killProcesses(stubborn, 'KILL');
        
        // Wait a bit more
        await waitForProcessesToDie(waitResult.stillRunning, 1000);
      } else {
        log(`⚠️  ${waitResult.stillRunning.length} processes still running. Use --force to kill them.`);
      }
    }
  }
  
  if (failed.length > 0) {
    log(`❌ Failed to kill ${failed.length} processes`);
    failed.forEach(proc => {
      log(`  PID ${proc.pid}: ${proc.command || 'unknown'}`);
    });
  }
  
  // Final check
  const remainingPortProcesses = findProcessesOnPorts();
  if (remainingPortProcesses.length === 0) {
    log('✅ All development ports are now free');
    return true;
  } else {
    log(`⚠️  ${remainingPortProcesses.length} processes still using development ports`);
    return false;
  }
}

// Command line interface
async function main() {
  const args = process.argv.slice(2);
  const force = args.includes('--force') || args.includes('-f');
  
  try {
    const success = await cleanup(force);
    process.exit(success ? 0 : 1);
  } catch (error) {
    log(`💥 Error during cleanup: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
