#!/usr/bin/env node

/**
 * Dependency Analysis Script
 * 
 * Analyzes component dependencies, detects circular dependencies,
 * and identifies potential HMR issues in the React application.
 */

const fs = require('fs');
const path = require('path');

const SRC_DIR = path.join(__dirname, '../src');
const COMPONENTS_DIR = path.join(SRC_DIR, 'components');
const UI_COMPONENTS_DIR = path.join(COMPONENTS_DIR, 'ui');

function log(message) {
  console.log(`[DepAnalysis] ${message}`);
}

function extractImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = [];
    
    // Match ES6 imports
    const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"]([^'"]+)['"]/g;
    
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      
      // Skip node_modules imports
      if (!importPath.startsWith('.') && !importPath.startsWith('@/')) {
        continue;
      }
      
      imports.push({
        path: importPath,
        line: content.substring(0, match.index).split('\n').length,
        raw: match[0]
      });
    }
    
    return imports;
  } catch (error) {
    log(`Error reading file ${filePath}: ${error.message}`);
    return [];
  }
}

function resolveImportPath(importPath, currentFile) {
  if (importPath.startsWith('@/')) {
    // Resolve @/ alias to src directory
    return path.resolve(SRC_DIR, importPath.substring(2));
  } else if (importPath.startsWith('./') || importPath.startsWith('../')) {
    // Resolve relative imports
    return path.resolve(path.dirname(currentFile), importPath);
  }
  
  return null;
}

function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      log(`Error reading directory ${currentDir}: ${error.message}`);
    }
  }
  
  traverse(dir);
  return files;
}

function buildDependencyGraph() {
  const files = findFiles(SRC_DIR);
  const graph = new Map();
  
  log(`Analyzing ${files.length} files...`);
  
  for (const file of files) {
    const imports = extractImports(file);
    const dependencies = [];
    
    for (const imp of imports) {
      const resolvedPath = resolveImportPath(imp.path, file);
      if (resolvedPath) {
        // Try with different extensions
        const extensions = ['.ts', '.tsx', '.js', '.jsx', ''];
        let actualPath = null;
        
        for (const ext of extensions) {
          const testPath = resolvedPath + ext;
          if (fs.existsSync(testPath)) {
            actualPath = testPath;
            break;
          }
        }
        
        // Check for index files
        if (!actualPath && fs.existsSync(resolvedPath)) {
          const stat = fs.statSync(resolvedPath);
          if (stat.isDirectory()) {
            for (const ext of ['.ts', '.tsx', '.js', '.jsx']) {
              const indexPath = path.join(resolvedPath, `index${ext}`);
              if (fs.existsSync(indexPath)) {
                actualPath = indexPath;
                break;
              }
            }
          }
        }
        
        if (actualPath) {
          dependencies.push({
            file: actualPath,
            import: imp
          });
        }
      }
    }
    
    graph.set(file, dependencies);
  }
  
  return graph;
}

function detectCircularDependencies(graph) {
  const visited = new Set();
  const recursionStack = new Set();
  const cycles = [];
  
  function dfs(node, path = []) {
    if (recursionStack.has(node)) {
      // Found a cycle
      const cycleStart = path.indexOf(node);
      const cycle = path.slice(cycleStart).concat([node]);
      cycles.push(cycle);
      return;
    }
    
    if (visited.has(node)) {
      return;
    }
    
    visited.add(node);
    recursionStack.add(node);
    path.push(node);
    
    const dependencies = graph.get(node) || [];
    for (const dep of dependencies) {
      dfs(dep.file, [...path]);
    }
    
    recursionStack.delete(node);
  }
  
  for (const node of graph.keys()) {
    if (!visited.has(node)) {
      dfs(node);
    }
  }
  
  return cycles;
}

function analyzeUIComponents() {
  log('Analyzing UI components...');
  
  if (!fs.existsSync(UI_COMPONENTS_DIR)) {
    log('UI components directory not found');
    return;
  }
  
  const uiFiles = findFiles(UI_COMPONENTS_DIR);
  const problematicComponents = ['tooltip', 'button', 'popover', 'badge', 'dropdown-menu'];
  
  log(`Found ${uiFiles.length} UI component files`);
  
  const componentAnalysis = {};
  
  for (const file of uiFiles) {
    const fileName = path.basename(file, path.extname(file));
    const imports = extractImports(file);
    
    componentAnalysis[fileName] = {
      file,
      imports: imports.length,
      externalDeps: imports.filter(imp => !imp.path.startsWith('.')).length,
      internalDeps: imports.filter(imp => imp.path.startsWith('.')).length,
      isProblematic: problematicComponents.includes(fileName),
      dependencies: imports
    };
  }
  
  // Report on problematic components
  log('\n📊 UI Component Analysis:');
  for (const [name, analysis] of Object.entries(componentAnalysis)) {
    const status = analysis.isProblematic ? '🔴' : '🟢';
    log(`${status} ${name}: ${analysis.imports} imports (${analysis.externalDeps} external, ${analysis.internalDeps} internal)`);
    
    if (analysis.isProblematic) {
      log(`   Dependencies:`);
      analysis.dependencies.forEach(dep => {
        log(`     - ${dep.path} (line ${dep.line})`);
      });
    }
  }
  
  return componentAnalysis;
}

function analyzeSharedUtilities() {
  log('\nAnalyzing shared utilities...');
  
  const libDir = path.join(SRC_DIR, 'lib');
  if (!fs.existsSync(libDir)) {
    log('Lib directory not found');
    return;
  }
  
  const libFiles = findFiles(libDir);
  const utilsAnalysis = {};
  
  for (const file of libFiles) {
    const fileName = path.basename(file, path.extname(file));
    const imports = extractImports(file);
    
    utilsAnalysis[fileName] = {
      file,
      imports: imports.length,
      dependencies: imports
    };
  }
  
  log('📚 Shared Utilities:');
  for (const [name, analysis] of Object.entries(utilsAnalysis)) {
    log(`  ${name}: ${analysis.imports} imports`);
    if (analysis.imports > 0) {
      analysis.dependencies.forEach(dep => {
        log(`    - ${dep.path}`);
      });
    }
  }
  
  return utilsAnalysis;
}

function generateReport(graph, cycles, componentAnalysis, utilsAnalysis) {
  log('\n📋 DEPENDENCY ANALYSIS REPORT');
  log('================================');
  
  log(`\n🔍 Overview:`);
  log(`  Total files analyzed: ${graph.size}`);
  log(`  Circular dependencies found: ${cycles.length}`);
  log(`  UI components analyzed: ${Object.keys(componentAnalysis).length}`);
  log(`  Shared utilities: ${Object.keys(utilsAnalysis).length}`);
  
  if (cycles.length > 0) {
    log(`\n🔄 Circular Dependencies:`);
    cycles.forEach((cycle, index) => {
      log(`  Cycle ${index + 1}:`);
      cycle.forEach((file, i) => {
        const relativePath = path.relative(SRC_DIR, file);
        const arrow = i < cycle.length - 1 ? ' → ' : '';
        log(`    ${relativePath}${arrow}`);
      });
    });
  } else {
    log(`\n✅ No circular dependencies detected`);
  }
  
  // Recommendations
  log(`\n💡 HMR Optimization Recommendations:`);
  
  if (cycles.length > 0) {
    log(`  1. Fix circular dependencies to improve HMR reliability`);
  }
  
  const problematicComponents = Object.entries(componentAnalysis)
    .filter(([_, analysis]) => analysis.isProblematic && analysis.imports > 5);
  
  if (problematicComponents.length > 0) {
    log(`  2. Consider reducing dependencies in: ${problematicComponents.map(([name]) => name).join(', ')}`);
  }
  
  log(`  3. Ensure shared utilities are stable and don't change frequently`);
  log(`  4. Consider using React.memo() for components with many dependencies`);
  log(`  5. Use dynamic imports for heavy dependencies when possible`);
}

async function main() {
  log('🔍 Starting dependency analysis...');
  
  try {
    const graph = buildDependencyGraph();
    const cycles = detectCircularDependencies(graph);
    const componentAnalysis = analyzeUIComponents();
    const utilsAnalysis = analyzeSharedUtilities();
    
    generateReport(graph, cycles, componentAnalysis, utilsAnalysis);
    
    const hasIssues = cycles.length > 0;
    process.exit(hasIssues ? 1 : 0);
    
  } catch (error) {
    log(`💥 Error during analysis: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
