#!/usr/bin/env node

/**
 * GitIgnore Optimization Script
 * 
 * Analyzes .gitignore files and ensures proper patterns are in place
 * to prevent unnecessary files from being watched by development servers.
 */

const fs = require('fs');
const path = require('path');

const PROJECT_ROOT = path.join(__dirname, '../../..');
const DESKTOP_ROOT = path.join(__dirname, '..');

function log(message) {
  console.log(`[GitIgnore] ${message}`);
}

function readGitignore(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return [];
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    return content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && !line.startsWith('#'));
  } catch (error) {
    log(`Error reading ${filePath}: ${error.message}`);
    return [];
  }
}

function getRecommendedPatterns() {
  return {
    // Development and build outputs
    build: [
      'dist/',
      'build/',
      'out/',
      '.next/',
      '.nuxt/',
      '.vite/',
      'coverage/',
      '.nyc_output/'
    ],
    
    // Dependencies
    dependencies: [
      'node_modules/',
      'bower_components/',
      'jspm_packages/'
    ],
    
    // Environment and configuration
    environment: [
      '.env.local',
      '.env.development.local',
      '.env.test.local',
      '.env.production.local',
      '*.env.backup',
      '*.env.checksum'
    ],
    
    // Logs and temporary files
    logs: [
      'logs/',
      '*.log',
      'npm-debug.log*',
      'yarn-debug.log*',
      'yarn-error.log*',
      'lerna-debug.log*',
      'dev-server.log'
    ],
    
    // IDE and editor files
    ide: [
      '.vscode/',
      '.idea/',
      '*.swp',
      '*.swo',
      '*~',
      '.DS_Store',
      'Thumbs.db'
    ],
    
    // Testing
    testing: [
      '.jest/',
      'jest-coverage/',
      '__tests__/coverage/',
      '*.test.js.snap'
    ],
    
    // Electron specific
    electron: [
      'dist-electron/',
      'release/',
      '*.dmg',
      '*.exe',
      '*.deb',
      '*.rpm',
      '*.AppImage'
    ],
    
    // Development tools
    devTools: [
      '.cache/',
      '.parcel-cache/',
      '.eslintcache',
      '.stylelintcache',
      'storybook-static/'
    ]
  };
}

function analyzeGitignore(gitignorePath) {
  const patterns = readGitignore(gitignorePath);
  const recommended = getRecommendedPatterns();
  
  const analysis = {
    existing: patterns,
    missing: [],
    categories: {}
  };
  
  // Check each category
  for (const [category, categoryPatterns] of Object.entries(recommended)) {
    const missing = categoryPatterns.filter(pattern => {
      // Check if pattern or similar pattern exists
      return !patterns.some(existing => 
        existing === pattern || 
        existing.includes(pattern.replace('/', '')) ||
        pattern.includes(existing.replace('/', ''))
      );
    });
    
    analysis.categories[category] = {
      total: categoryPatterns.length,
      missing: missing.length,
      patterns: missing
    };
    
    analysis.missing.push(...missing);
  }
  
  return analysis;
}

function generateOptimizedGitignore(analysis) {
  const lines = [
    '# Dependencies',
    'node_modules/',
    'bower_components/',
    'jspm_packages/',
    '',
    '# Build outputs',
    'dist/',
    'build/',
    'out/',
    '.next/',
    '.nuxt/',
    '.vite/',
    'dist-electron/',
    'release/',
    '',
    '# Environment files',
    '.env.local',
    '.env.development.local',
    '.env.test.local',
    '.env.production.local',
    '*.env.backup',
    '*.env.checksum',
    '',
    '# Logs',
    'logs/',
    '*.log',
    'npm-debug.log*',
    'yarn-debug.log*',
    'yarn-error.log*',
    'lerna-debug.log*',
    'dev-server.log',
    '',
    '# Testing',
    'coverage/',
    '.nyc_output/',
    '.jest/',
    'jest-coverage/',
    '__tests__/coverage/',
    '*.test.js.snap',
    '',
    '# IDE and editor files',
    '.vscode/',
    '.idea/',
    '*.swp',
    '*.swo',
    '*~',
    '.DS_Store',
    'Thumbs.db',
    '',
    '# Development tools',
    '.cache/',
    '.parcel-cache/',
    '.eslintcache',
    '.stylelintcache',
    'storybook-static/',
    '',
    '# Electron distributables',
    '*.dmg',
    '*.exe',
    '*.deb',
    '*.rpm',
    '*.AppImage'
  ];
  
  return lines.join('\n') + '\n';
}

function updateGitignore(gitignorePath, analysis, dryRun = false) {
  if (analysis.missing.length === 0) {
    log('✅ .gitignore is already optimized');
    return false;
  }
  
  if (dryRun) {
    log(`📋 Would add ${analysis.missing.length} missing patterns:`);
    analysis.missing.forEach(pattern => {
      log(`  + ${pattern}`);
    });
    return true;
  }
  
  // Create backup
  const backupPath = `${gitignorePath}.backup`;
  if (fs.existsSync(gitignorePath)) {
    fs.copyFileSync(gitignorePath, backupPath);
    log(`📋 Created backup: ${backupPath}`);
  }
  
  // Generate optimized .gitignore
  const optimizedContent = generateOptimizedGitignore(analysis);
  
  try {
    fs.writeFileSync(gitignorePath, optimizedContent);
    log(`✅ Updated .gitignore with ${analysis.missing.length} new patterns`);
    return true;
  } catch (error) {
    log(`❌ Error updating .gitignore: ${error.message}`);
    return false;
  }
}

function generateReport(analysis, gitignorePath) {
  log('\n📊 GITIGNORE ANALYSIS REPORT');
  log('============================');
  
  log(`\n📁 File: ${gitignorePath}`);
  log(`📈 Current patterns: ${analysis.existing.length}`);
  log(`📉 Missing patterns: ${analysis.missing.length}`);
  
  if (analysis.missing.length > 0) {
    log(`\n🔍 Missing Patterns by Category:`);
    
    for (const [category, data] of Object.entries(analysis.categories)) {
      if (data.missing > 0) {
        log(`  ${category}: ${data.missing}/${data.total} missing`);
        data.patterns.forEach(pattern => {
          log(`    + ${pattern}`);
        });
      }
    }
  }
  
  log(`\n💡 Recommendations:`);
  if (analysis.missing.length > 0) {
    log(`  1. Add ${analysis.missing.length} missing ignore patterns`);
    log(`  2. Run with --update to apply changes`);
  } else {
    log(`  ✅ .gitignore is well optimized`);
  }
  
  log(`  3. Regularly review and update ignore patterns`);
  log(`  4. Ensure development tools respect .gitignore`);
  log(`  5. Consider project-specific patterns`);
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const flags = args.slice(1);
  
  const dryRun = flags.includes('--dry-run');
  const update = flags.includes('--update');
  
  if (command === 'analyze') {
    log('🔍 Analyzing .gitignore files...');
    
    // Check project root .gitignore
    const rootGitignore = path.join(PROJECT_ROOT, '.gitignore');
    const desktopGitignore = path.join(DESKTOP_ROOT, '.gitignore');
    
    const files = [
      { path: rootGitignore, name: 'Project Root' },
      { path: desktopGitignore, name: 'Desktop App' }
    ];
    
    let hasIssues = false;
    
    for (const file of files) {
      if (fs.existsSync(file.path)) {
        log(`\n📁 Analyzing ${file.name} .gitignore...`);
        const analysis = analyzeGitignore(file.path);
        generateReport(analysis, file.path);
        
        if (update && !dryRun) {
          updateGitignore(file.path, analysis, false);
        } else if (dryRun || analysis.missing.length > 0) {
          updateGitignore(file.path, analysis, true);
        }
        
        if (analysis.missing.length > 0) {
          hasIssues = true;
        }
      } else {
        log(`⚠️  ${file.name} .gitignore not found: ${file.path}`);
        
        if (update && !dryRun) {
          log(`📝 Creating ${file.name} .gitignore...`);
          const optimizedContent = generateOptimizedGitignore({ missing: [] });
          fs.writeFileSync(file.path, optimizedContent);
          log(`✅ Created ${file.path}`);
        }
      }
    }
    
    process.exit(hasIssues ? 1 : 0);
    
  } else {
    console.log('Usage: node optimize-gitignore.js [analyze] [--dry-run] [--update]');
    console.log('  analyze    - Analyze .gitignore files');
    console.log('  --dry-run  - Show what would be changed without making changes');
    console.log('  --update   - Apply recommended changes');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    log(`💥 Error: ${error.message}`);
    process.exit(1);
  });
}
