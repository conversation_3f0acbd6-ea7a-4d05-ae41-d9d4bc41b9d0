#!/usr/bin/env node

/**
 * Development Server Health Check Script
 * 
 * Monitors the Vite development server for stability issues
 * and provides diagnostics for connection problems.
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const VITE_PORT = 5173;
const HMR_PORT = 5174;
const CHECK_INTERVAL = 2000; // 2 seconds
const MAX_RETRIES = 30; // 1 minute total
const LOG_FILE = path.join(__dirname, '../dev-server.log');

let retryCount = 0;
let isServerHealthy = false;

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  
  // Append to log file
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

function checkPort(port, name) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      method: 'GET',
      path: '/',
      timeout: 3000, // Increased timeout for better reliability
      headers: {
        'User-Agent': 'dev-server-health-check'
      }
    }, (res) => {
      // Consider any 2xx or 3xx status as healthy
      const isHealthy = res.statusCode >= 200 && res.statusCode < 400;
      resolve({
        port,
        name,
        status: isHealthy ? 'healthy' : 'unhealthy',
        statusCode: res.statusCode
      });
    });

    req.on('error', (err) => {
      resolve({ port, name, status: 'error', error: err.message });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({ port, name, status: 'timeout' });
    });

    req.end();
  });
}

async function checkServerHealth() {
  const checks = await Promise.all([
    checkPort(VITE_PORT, 'Vite Dev Server'),
    checkPort(HMR_PORT, 'HMR Server')
  ]);

  const viteCheck = checks.find(c => c.port === VITE_PORT);
  const hmrCheck = checks.find(c => c.port === HMR_PORT);

  // Primary requirement: Vite server must be healthy
  if (viteCheck.status === 'healthy') {
    if (!isServerHealthy) {
      log('✅ Vite development server is healthy');
      if (hmrCheck.status === 'healthy') {
        log('✅ HMR server is also healthy');
      } else {
        log('⚠️  HMR server not ready yet, but Vite is working');
      }
      isServerHealthy = true;
      retryCount = 0;
    }
    return true;
  } else {
    isServerHealthy = false;
    retryCount++;

    log(`❌ Server health check failed (attempt ${retryCount}/${MAX_RETRIES})`);
    log(`   Vite Server (${VITE_PORT}): ${viteCheck.status} ${viteCheck.error || viteCheck.statusCode || ''}`);
    log(`   HMR Server (${HMR_PORT}): ${hmrCheck.status} ${hmrCheck.error || hmrCheck.statusCode || ''}`);

    if (retryCount >= MAX_RETRIES) {
      log('🚨 Server failed to become healthy after maximum retries');
      return false;
    }

    return null; // Continue checking
  }
}

function checkProcesses() {
  const { execSync } = require('child_process');
  
  try {
    // Check for existing Vite processes
    const viteProcesses = execSync(`lsof -i :${VITE_PORT} 2>/dev/null || true`, { encoding: 'utf8' });
    if (viteProcesses.trim()) {
      log('📊 Processes using Vite port:');
      log(viteProcesses);
    }
    
    // Check for Node.js processes that might interfere
    const nodeProcesses = execSync('ps aux | grep node | grep -v grep | head -5', { encoding: 'utf8' });
    if (nodeProcesses.trim()) {
      log('📊 Active Node.js processes:');
      log(nodeProcesses);
    }
  } catch (error) {
    log(`⚠️  Error checking processes: ${error.message}`);
  }
}

function cleanup() {
  log('🧹 Cleaning up development server health check');
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

async function main() {
  const command = process.argv[2];
  
  if (command === 'check') {
    // Single health check
    log('🔍 Performing single health check...');
    const result = await checkServerHealth();
    if (result === true) {
      log('✅ Server is healthy');
      process.exit(0);
    } else {
      log('❌ Server is not healthy');
      checkProcesses();
      process.exit(1);
    }
  } else if (command === 'monitor') {
    // Continuous monitoring
    log('👀 Starting continuous server monitoring...');
    log(`   Checking every ${CHECK_INTERVAL}ms`);
    log(`   Max retries: ${MAX_RETRIES}`);
    
    const interval = setInterval(async () => {
      const result = await checkServerHealth();
      if (result === false) {
        log('🛑 Stopping monitoring due to persistent failures');
        checkProcesses();
        clearInterval(interval);
        process.exit(1);
      }
    }, CHECK_INTERVAL);
    
  } else if (command === 'wait') {
    // Wait for server to become healthy
    log('⏳ Waiting for server to become healthy...');
    
    const interval = setInterval(async () => {
      const result = await checkServerHealth();
      if (result === true) {
        log('✅ Server is ready!');
        clearInterval(interval);
        process.exit(0);
      } else if (result === false) {
        log('❌ Server failed to start');
        checkProcesses();
        clearInterval(interval);
        process.exit(1);
      }
    }, CHECK_INTERVAL);
    
  } else {
    console.log('Usage: node dev-server-health.js [check|monitor|wait]');
    console.log('  check   - Perform single health check');
    console.log('  monitor - Continuously monitor server health');
    console.log('  wait    - Wait for server to become healthy');
    process.exit(1);
  }
}

main().catch(error => {
  log(`💥 Unexpected error: ${error.message}`);
  process.exit(1);
});
