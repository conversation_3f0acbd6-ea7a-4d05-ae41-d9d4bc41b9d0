#!/usr/bin/env node

/**
 * HMR Optimization Script
 * 
 * Analyzes and optimizes Hot Module Replacement (HMR) performance
 * by identifying components that cause excessive reloads and providing
 * optimization suggestions.
 */

const fs = require('fs');
const path = require('path');

const SRC_DIR = path.join(__dirname, '../src');
const COMPONENTS_DIR = path.join(SRC_DIR, 'components');

function log(message) {
  console.log(`[HMR-Optimizer] ${message}`);
}

function findReactComponents(dir) {
  const components = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (item.endsWith('.tsx') || item.endsWith('.jsx')) {
          components.push(fullPath);
        }
      }
    } catch (error) {
      log(`Error reading directory ${currentDir}: ${error.message}`);
    }
  }
  
  traverse(dir);
  return components;
}

function analyzeComponent(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath, path.extname(filePath));
    
    const analysis = {
      file: filePath,
      name: fileName,
      hasDefaultExport: /export\s+default/.test(content),
      hasNamedExports: /export\s+(?:const|function|class)/.test(content),
      usesReactMemo: /React\.memo|memo\s*\(/.test(content),
      usesCallback: /useCallback/.test(content),
      usesMemo: /useMemo/.test(content),
      hasInlineStyles: /style\s*=\s*\{/.test(content),
      hasConditionalRendering: /\?\s*<|&&\s*</.test(content),
      importCount: (content.match(/^import\s+/gm) || []).length,
      exportCount: (content.match(/^export\s+/gm) || []).length,
      componentSize: content.length,
      lineCount: content.split('\n').length
    };
    
    // Calculate HMR risk score
    let riskScore = 0;
    
    if (!analysis.hasDefaultExport) riskScore += 1;
    if (analysis.importCount > 10) riskScore += 2;
    if (analysis.componentSize > 5000) riskScore += 2;
    if (analysis.hasInlineStyles) riskScore += 1;
    if (!analysis.usesReactMemo && analysis.lineCount > 50) riskScore += 1;
    if (analysis.hasConditionalRendering && !analysis.usesMemo) riskScore += 1;
    
    analysis.hmrRiskScore = riskScore;
    analysis.hmrRiskLevel = riskScore <= 2 ? 'LOW' : riskScore <= 4 ? 'MEDIUM' : 'HIGH';
    
    return analysis;
  } catch (error) {
    log(`Error analyzing component ${filePath}: ${error.message}`);
    return null;
  }
}

function generateOptimizationSuggestions(analysis) {
  const suggestions = [];
  
  if (!analysis.hasDefaultExport) {
    suggestions.push('Consider using default export for better HMR support');
  }
  
  if (analysis.importCount > 10) {
    suggestions.push('Reduce number of imports - consider code splitting or lazy loading');
  }
  
  if (analysis.componentSize > 5000) {
    suggestions.push('Component is large - consider breaking into smaller components');
  }
  
  if (analysis.hasInlineStyles) {
    suggestions.push('Move inline styles to CSS modules or styled-components for better HMR');
  }
  
  if (!analysis.usesReactMemo && analysis.lineCount > 50) {
    suggestions.push('Consider using React.memo() to prevent unnecessary re-renders');
  }
  
  if (analysis.hasConditionalRendering && !analysis.usesMemo) {
    suggestions.push('Use useMemo() for expensive conditional rendering logic');
  }
  
  if (!analysis.usesCallback && analysis.lineCount > 30) {
    suggestions.push('Consider using useCallback() for event handlers to prevent re-renders');
  }
  
  return suggestions;
}

function createHMRBoundaryFile(componentPath) {
  const componentName = path.basename(componentPath, path.extname(componentPath));
  const boundaryPath = path.join(path.dirname(componentPath), `${componentName}.hmr.ts`);
  
  const boundaryContent = `/**
 * HMR Boundary for ${componentName}
 * 
 * This file helps Vite understand the HMR boundaries for this component,
 * preventing unnecessary reloads of parent components.
 */

// Accept HMR updates for this module
if (import.meta.hot) {
  import.meta.hot.accept();
  
  // Optionally, you can add custom HMR logic here
  import.meta.hot.accept('./${componentName}', (newModule) => {
    // Custom update logic if needed
    console.log('HMR: ${componentName} updated');
  });
}

export { default } from './${componentName}';
`;

  try {
    fs.writeFileSync(boundaryPath, boundaryContent);
    log(`Created HMR boundary file: ${boundaryPath}`);
    return boundaryPath;
  } catch (error) {
    log(`Error creating HMR boundary file: ${error.message}`);
    return null;
  }
}

function optimizeComponent(analysis) {
  const optimizations = [];
  
  // Check if component needs React.memo
  if (!analysis.usesReactMemo && analysis.hmrRiskScore > 2) {
    optimizations.push({
      type: 'memo',
      description: 'Wrap component with React.memo()',
      priority: 'HIGH'
    });
  }
  
  // Check if component needs HMR boundary
  if (analysis.hmrRiskLevel === 'HIGH') {
    optimizations.push({
      type: 'hmr-boundary',
      description: 'Create HMR boundary file',
      priority: 'MEDIUM'
    });
  }
  
  // Check for code splitting opportunities
  if (analysis.importCount > 8) {
    optimizations.push({
      type: 'code-splitting',
      description: 'Consider lazy loading heavy dependencies',
      priority: 'MEDIUM'
    });
  }
  
  return optimizations;
}

function generateReport(analyses) {
  log('\n📊 HMR OPTIMIZATION REPORT');
  log('==========================');
  
  const totalComponents = analyses.length;
  const highRiskComponents = analyses.filter(a => a.hmrRiskLevel === 'HIGH').length;
  const mediumRiskComponents = analyses.filter(a => a.hmrRiskLevel === 'MEDIUM').length;
  const lowRiskComponents = analyses.filter(a => a.hmrRiskLevel === 'LOW').length;
  
  log(`\n📈 Overview:`);
  log(`  Total components analyzed: ${totalComponents}`);
  log(`  High risk components: ${highRiskComponents}`);
  log(`  Medium risk components: ${mediumRiskComponents}`);
  log(`  Low risk components: ${lowRiskComponents}`);
  
  // Show high-risk components
  const highRisk = analyses.filter(a => a.hmrRiskLevel === 'HIGH');
  if (highRisk.length > 0) {
    log(`\n🔴 High Risk Components:`);
    highRisk.forEach(analysis => {
      const relativePath = path.relative(SRC_DIR, analysis.file);
      log(`  ${analysis.name} (${relativePath})`);
      log(`    Risk Score: ${analysis.hmrRiskScore}`);
      log(`    Size: ${analysis.componentSize} chars, ${analysis.lineCount} lines`);
      log(`    Imports: ${analysis.importCount}`);
      
      const suggestions = generateOptimizationSuggestions(analysis);
      if (suggestions.length > 0) {
        log(`    Suggestions:`);
        suggestions.forEach(suggestion => {
          log(`      - ${suggestion}`);
        });
      }
    });
  }
  
  // Show optimization opportunities
  log(`\n💡 Optimization Opportunities:`);
  
  const needsMemo = analyses.filter(a => !a.usesReactMemo && a.lineCount > 50).length;
  if (needsMemo > 0) {
    log(`  ${needsMemo} components could benefit from React.memo()`);
  }
  
  const needsCallback = analyses.filter(a => !a.usesCallback && a.lineCount > 30).length;
  if (needsCallback > 0) {
    log(`  ${needsCallback} components could benefit from useCallback()`);
  }
  
  const largeComponents = analyses.filter(a => a.componentSize > 3000).length;
  if (largeComponents > 0) {
    log(`  ${largeComponents} components are large and could be split`);
  }
  
  // HMR-specific recommendations
  log(`\n🔥 HMR-Specific Recommendations:`);
  log(`  1. Use default exports for better HMR support`);
  log(`  2. Minimize component dependencies and imports`);
  log(`  3. Use React.memo() for components that re-render frequently`);
  log(`  4. Consider lazy loading for heavy components`);
  log(`  5. Use CSS modules instead of inline styles`);
  log(`  6. Create HMR boundaries for complex components`);
}

async function main() {
  const command = process.argv[2];
  
  if (command === 'analyze') {
    log('🔍 Analyzing components for HMR optimization...');
    
    const componentFiles = findReactComponents(COMPONENTS_DIR);
    log(`Found ${componentFiles.length} React components`);
    
    const analyses = componentFiles
      .map(analyzeComponent)
      .filter(analysis => analysis !== null);
    
    generateReport(analyses);
    
  } else if (command === 'create-boundaries') {
    log('🛡️ Creating HMR boundaries for high-risk components...');
    
    const componentFiles = findReactComponents(COMPONENTS_DIR);
    const analyses = componentFiles
      .map(analyzeComponent)
      .filter(analysis => analysis !== null && analysis.hmrRiskLevel === 'HIGH');
    
    let created = 0;
    for (const analysis of analyses) {
      const boundaryFile = createHMRBoundaryFile(analysis.file);
      if (boundaryFile) created++;
    }
    
    log(`✅ Created ${created} HMR boundary files`);
    
  } else {
    console.log('Usage: node optimize-hmr.js [analyze|create-boundaries]');
    console.log('  analyze          - Analyze components for HMR issues');
    console.log('  create-boundaries - Create HMR boundary files for high-risk components');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    log(`💥 Error: ${error.message}`);
    process.exit(1);
  });
}
