#!/usr/bin/env node

/**
 * Environment File Protection Script
 * 
 * This script helps protect .env files from unauthorized modifications
 * and provides validation for required environment variables.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const ENV_FILE = path.join(__dirname, '../.env');
const ENV_BACKUP = path.join(__dirname, '../.env.backup');
const ENV_CHECKSUM = path.join(__dirname, '../.env.checksum');

function calculateChecksum(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }
  const content = fs.readFileSync(filePath, 'utf8');
  return crypto.createHash('sha256').update(content).digest('hex');
}

function saveChecksum() {
  const checksum = calculateChecksum(ENV_FILE);
  if (checksum) {
    fs.writeFileSync(ENV_CHECKSUM, checksum);
    console.log('✅ Environment file checksum saved');
  }
}

function validateChecksum() {
  if (!fs.existsSync(ENV_CHECKSUM)) {
    console.log('⚠️  No checksum file found. Creating initial checksum...');
    saveChecksum();
    return true;
  }

  const savedChecksum = fs.readFileSync(ENV_CHECKSUM, 'utf8').trim();
  const currentChecksum = calculateChecksum(ENV_FILE);

  if (savedChecksum !== currentChecksum) {
    console.log('🚨 WARNING: .env file has been modified!');
    console.log('Expected checksum:', savedChecksum);
    console.log('Current checksum:', currentChecksum);
    
    if (fs.existsSync(ENV_BACKUP)) {
      console.log('📋 Backup file available at:', ENV_BACKUP);
    }
    
    return false;
  }

  console.log('✅ Environment file integrity verified');
  return true;
}

function validateRequiredVars() {
  if (!fs.existsSync(ENV_FILE)) {
    console.log('❌ .env file not found');
    return false;
  }

  const content = fs.readFileSync(ENV_FILE, 'utf8');
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'BACKEND_URL',
    'WEB_PORTAL_URL',
    'ELECTRON_STORE_ENCRYPTION_KEY'
  ];

  let allValid = true;
  console.log('📋 Validating required environment variables:');

  requiredVars.forEach(varName => {
    const regex = new RegExp(`^${varName}=.+`, 'm');
    if (regex.test(content)) {
      console.log(`✅ ${varName}: Present`);
    } else {
      console.log(`❌ ${varName}: Missing or empty`);
      allValid = false;
    }
  });

  return allValid;
}

function createBackup() {
  if (fs.existsSync(ENV_FILE)) {
    fs.copyFileSync(ENV_FILE, ENV_BACKUP);
    console.log('📋 Backup created:', ENV_BACKUP);
  }
}

function restoreFromBackup() {
  if (fs.existsSync(ENV_BACKUP)) {
    fs.copyFileSync(ENV_BACKUP, ENV_FILE);
    console.log('🔄 Restored from backup');
    saveChecksum();
  } else {
    console.log('❌ No backup file found');
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'validate':
    validateChecksum();
    validateRequiredVars();
    break;
  case 'backup':
    createBackup();
    break;
  case 'restore':
    restoreFromBackup();
    break;
  case 'checksum':
    saveChecksum();
    break;
  default:
    console.log('Usage: node protect-env.js [validate|backup|restore|checksum]');
    console.log('  validate - Check file integrity and required variables');
    console.log('  backup   - Create backup of current .env file');
    console.log('  restore  - Restore .env from backup');
    console.log('  checksum - Update checksum for current .env file');
}
