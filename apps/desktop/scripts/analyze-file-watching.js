#!/usr/bin/env node

/**
 * File Watching Analysis Script
 * 
 * Analyzes file watching patterns, identifies files that shouldn't trigger
 * development server restarts, and provides optimization recommendations.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PROJECT_ROOT = path.join(__dirname, '../../..');
const DESKTOP_ROOT = path.join(__dirname, '..');

function log(message) {
  console.log(`[FileWatch] ${message}`);
}

function getFileStats(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return {
      size: stats.size,
      modified: stats.mtime,
      isDirectory: stats.isDirectory(),
      isFile: stats.isFile()
    };
  } catch (error) {
    return null;
  }
}

function findFilesRecursively(dir, options = {}) {
  const {
    maxDepth = 10,
    currentDepth = 0,
    includeHidden = false,
    extensions = null
  } = options;

  const files = [];
  
  if (currentDepth >= maxDepth) {
    return files;
  }

  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      if (!includeHidden && item.startsWith('.')) {
        continue;
      }

      const fullPath = path.join(dir, item);
      const stats = getFileStats(fullPath);
      
      if (!stats) continue;

      if (stats.isDirectory) {
        files.push(...findFilesRecursively(fullPath, {
          ...options,
          currentDepth: currentDepth + 1
        }));
      } else if (stats.isFile) {
        if (!extensions || extensions.some(ext => item.endsWith(ext))) {
          files.push({
            path: fullPath,
            name: item,
            size: stats.size,
            modified: stats.modified,
            relativePath: path.relative(DESKTOP_ROOT, fullPath)
          });
        }
      }
    }
  } catch (error) {
    log(`Error reading directory ${dir}: ${error.message}`);
  }

  return files;
}

function categorizeFiles(files) {
  const categories = {
    source: [],
    config: [],
    build: [],
    docs: [],
    tests: [],
    assets: [],
    logs: [],
    temp: [],
    nodeModules: [],
    git: [],
    other: []
  };

  for (const file of files) {
    const { relativePath, name } = file;
    const ext = path.extname(name).toLowerCase();
    
    if (relativePath.includes('node_modules')) {
      categories.nodeModules.push(file);
    } else if (relativePath.includes('.git')) {
      categories.git.push(file);
    } else if (relativePath.includes('dist') || relativePath.includes('build')) {
      categories.build.push(file);
    } else if (name.includes('.log') || relativePath.includes('logs')) {
      categories.logs.push(file);
    } else if (name.includes('.test.') || name.includes('.spec.') || relativePath.includes('__tests__')) {
      categories.tests.push(file);
    } else if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
      categories.source.push(file);
    } else if (['.json', '.toml', '.yaml', '.yml', '.env'].includes(ext) || 
               ['package.json', 'tsconfig.json', 'vite.config.ts'].includes(name)) {
      categories.config.push(file);
    } else if (['.md', '.txt', '.rst'].includes(ext)) {
      categories.docs.push(file);
    } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'].includes(ext)) {
      categories.assets.push(file);
    } else if (['.tmp', '.temp', '.cache'].includes(ext) || name.includes('temp')) {
      categories.temp.push(file);
    } else {
      categories.other.push(file);
    }
  }

  return categories;
}

function analyzeWatchPatterns() {
  log('🔍 Analyzing current file watching patterns...');
  
  // Read current Vite config
  const viteConfigPath = path.join(DESKTOP_ROOT, 'vite.config.ts');
  let currentIgnorePatterns = [];
  
  if (fs.existsSync(viteConfigPath)) {
    const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
    
    // Extract ignore patterns from config
    const ignoreMatch = viteConfig.match(/ignored:\s*\[([\s\S]*?)\]/);
    if (ignoreMatch) {
      const patterns = ignoreMatch[1]
        .split(',')
        .map(p => p.trim().replace(/['"]/g, ''))
        .filter(p => p.length > 0);
      currentIgnorePatterns = patterns;
    }
  }
  
  log(`Found ${currentIgnorePatterns.length} ignore patterns in Vite config`);
  currentIgnorePatterns.forEach(pattern => {
    log(`  - ${pattern}`);
  });
  
  return currentIgnorePatterns;
}

function findLargeFiles(files, sizeThreshold = 1024 * 1024) { // 1MB
  return files.filter(file => file.size > sizeThreshold)
    .sort((a, b) => b.size - a.size);
}

function findFrequentlyChangedFiles(files) {
  const recentThreshold = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
  
  return files.filter(file => file.modified.getTime() > recentThreshold)
    .sort((a, b) => b.modified.getTime() - a.modified.getTime());
}

function generateOptimizationRecommendations(categories, currentIgnorePatterns) {
  const recommendations = [];
  
  // Check for files that should be ignored but aren't
  const shouldIgnore = [
    ...categories.build,
    ...categories.logs,
    ...categories.temp,
    ...categories.nodeModules,
    ...categories.git
  ];
  
  if (shouldIgnore.length > 0) {
    recommendations.push({
      type: 'ignore-patterns',
      priority: 'HIGH',
      description: `Add ignore patterns for ${shouldIgnore.length} files that shouldn't trigger restarts`,
      files: shouldIgnore.slice(0, 10) // Show first 10 as examples
    });
  }
  
  // Check for large files
  const largeFiles = findLargeFiles([...categories.source, ...categories.assets]);
  if (largeFiles.length > 0) {
    recommendations.push({
      type: 'large-files',
      priority: 'MEDIUM',
      description: `Consider lazy loading or code splitting for ${largeFiles.length} large files`,
      files: largeFiles.slice(0, 5)
    });
  }
  
  // Check for test files in watch patterns
  if (categories.tests.length > 0) {
    recommendations.push({
      type: 'test-exclusion',
      priority: 'MEDIUM',
      description: `Exclude ${categories.tests.length} test files from watch patterns`,
      files: categories.tests.slice(0, 5)
    });
  }
  
  return recommendations;
}

function generateReport(categories, currentIgnorePatterns, recommendations) {
  log('\n📊 FILE WATCHING ANALYSIS REPORT');
  log('=================================');
  
  const totalFiles = Object.values(categories).reduce((sum, cat) => sum + cat.length, 0);
  
  log(`\n📈 Overview:`);
  log(`  Total files analyzed: ${totalFiles}`);
  log(`  Current ignore patterns: ${currentIgnorePatterns.length}`);
  log(`  Optimization recommendations: ${recommendations.length}`);
  
  log(`\n📁 File Categories:`);
  Object.entries(categories).forEach(([category, files]) => {
    if (files.length > 0) {
      const totalSize = files.reduce((sum, f) => sum + f.size, 0);
      const sizeStr = totalSize > 1024 * 1024 ? 
        `${(totalSize / (1024 * 1024)).toFixed(1)}MB` :
        `${(totalSize / 1024).toFixed(1)}KB`;
      
      log(`  ${category}: ${files.length} files (${sizeStr})`);
    }
  });
  
  // Show large files
  const allFiles = Object.values(categories).flat();
  const largeFiles = findLargeFiles(allFiles, 100 * 1024); // 100KB threshold
  if (largeFiles.length > 0) {
    log(`\n📦 Large Files (>100KB):`);
    largeFiles.slice(0, 10).forEach(file => {
      const sizeStr = file.size > 1024 * 1024 ? 
        `${(file.size / (1024 * 1024)).toFixed(1)}MB` :
        `${(file.size / 1024).toFixed(1)}KB`;
      log(`  ${file.relativePath} (${sizeStr})`);
    });
  }
  
  // Show recently changed files
  const recentFiles = findFrequentlyChangedFiles(allFiles);
  if (recentFiles.length > 0) {
    log(`\n🕒 Recently Changed Files (last 24h):`);
    recentFiles.slice(0, 10).forEach(file => {
      const timeAgo = Math.round((Date.now() - file.modified.getTime()) / (1000 * 60));
      log(`  ${file.relativePath} (${timeAgo} minutes ago)`);
    });
  }
  
  // Show recommendations
  if (recommendations.length > 0) {
    log(`\n💡 Optimization Recommendations:`);
    recommendations.forEach((rec, index) => {
      const priority = rec.priority === 'HIGH' ? '🔴' : rec.priority === 'MEDIUM' ? '🟡' : '🟢';
      log(`  ${index + 1}. ${priority} ${rec.description}`);
      
      if (rec.files && rec.files.length > 0) {
        log(`     Examples:`);
        rec.files.slice(0, 3).forEach(file => {
          log(`       - ${file.relativePath}`);
        });
      }
    });
  }
  
  log(`\n🎯 File Watching Best Practices:`);
  log(`  1. Exclude build outputs (dist/, build/)`);
  log(`  2. Exclude dependency directories (node_modules/)`);
  log(`  3. Exclude version control files (.git/)`);
  log(`  4. Exclude log files and temporary files`);
  log(`  5. Exclude test files from production builds`);
  log(`  6. Use specific file extensions for watching`);
  log(`  7. Consider file size when including in watch patterns`);
}

async function main() {
  const command = process.argv[2];
  
  if (command === 'analyze') {
    log('🔍 Starting file watching analysis...');
    
    const files = findFilesRecursively(DESKTOP_ROOT, {
      maxDepth: 8,
      includeHidden: false,
      extensions: null // Include all files for analysis
    });
    
    const categories = categorizeFiles(files);
    const currentIgnorePatterns = analyzeWatchPatterns();
    const recommendations = generateOptimizationRecommendations(categories, currentIgnorePatterns);
    
    generateReport(categories, currentIgnorePatterns, recommendations);
    
    const hasHighPriorityIssues = recommendations.some(r => r.priority === 'HIGH');
    process.exit(hasHighPriorityIssues ? 1 : 0);
    
  } else {
    console.log('Usage: node analyze-file-watching.js [analyze]');
    console.log('  analyze - Analyze file watching patterns and provide recommendations');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    log(`💥 Error: ${error.message}`);
    process.exit(1);
  });
}
