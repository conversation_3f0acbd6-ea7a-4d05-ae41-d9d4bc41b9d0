#!/usr/bin/env node

/**
 * File Watching Monitor Script
 * 
 * Monitors file system events and detects when files that should be ignored
 * are triggering development server restarts.
 */

const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');

const DESKTOP_ROOT = path.join(__dirname, '..');
const MONITOR_DURATION = 30000; // 30 seconds

function log(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [FileMonitor] ${message}`);
}

function shouldBeIgnored(filePath) {
  const ignoredPatterns = [
    /node_modules/,
    /dist\//,
    /build\//,
    /\.git\//,
    /\.env/,
    /\.log$/,
    /\.backup$/,
    /\.checksum$/,
    /\.cache\//,
    /coverage\//,
    /__tests__\//,
    /\.test\./,
    /\.spec\./,
    /\.md$/,
    /README/,
    /LICENSE/,
    /package-lock\.json$/,
    /yarn\.lock$/,
    /tsconfig.*\.json$/,
    /\.DS_Store$/,
    /Thumbs\.db$/,
    /\.swp$/,
    /\.swo$/,
    /~$/
  ];
  
  return ignoredPatterns.some(pattern => pattern.test(filePath));
}

function categorizeFileChange(filePath, eventType) {
  const relativePath = path.relative(DESKTOP_ROOT, filePath);
  const ext = path.extname(filePath).toLowerCase();
  
  let category = 'other';
  let shouldIgnore = shouldBeIgnored(relativePath);
  
  if (relativePath.includes('node_modules')) {
    category = 'dependencies';
  } else if (relativePath.includes('dist') || relativePath.includes('build')) {
    category = 'build-output';
  } else if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
    category = 'source-code';
    shouldIgnore = false; // Source code should not be ignored
  } else if (['.json', '.toml', '.yaml', '.yml'].includes(ext)) {
    category = 'configuration';
  } else if (['.css', '.scss', '.sass', '.less'].includes(ext)) {
    category = 'styles';
    shouldIgnore = false; // Styles should not be ignored
  } else if (ext === '.md' || relativePath.includes('README')) {
    category = 'documentation';
  } else if (relativePath.includes('test') || ext.includes('.test.') || ext.includes('.spec.')) {
    category = 'tests';
  } else if (ext === '.log' || relativePath.includes('logs')) {
    category = 'logs';
  }
  
  return {
    category,
    shouldIgnore,
    relativePath,
    eventType,
    timestamp: new Date()
  };
}

function startMonitoring(duration = MONITOR_DURATION) {
  log(`🔍 Starting file watching monitor for ${duration / 1000} seconds...`);
  log(`📁 Monitoring directory: ${DESKTOP_ROOT}`);
  
  const events = [];
  const stats = {
    total: 0,
    byCategory: {},
    shouldBeIgnored: 0,
    problematic: []
  };
  
  // Configure watcher with same patterns as Vite
  const watcher = chokidar.watch(DESKTOP_ROOT, {
    ignored: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/.env*',
      '**/*.log',
      '**/coverage/**',
      '**/__tests__/**',
      '**/*.test.*',
      '**/*.spec.*',
      '**/*.md',
      '**/README*',
      '**/LICENSE*',
      '**/.DS_Store',
      '**/Thumbs.db'
    ],
    persistent: true,
    ignoreInitial: true,
    followSymlinks: false,
    depth: 10
  });
  
  // Track file events
  watcher.on('all', (eventType, filePath) => {
    const analysis = categorizeFileChange(filePath, eventType);
    events.push(analysis);
    stats.total++;
    
    // Update category stats
    if (!stats.byCategory[analysis.category]) {
      stats.byCategory[analysis.category] = 0;
    }
    stats.byCategory[analysis.category]++;
    
    // Track problematic files
    if (analysis.shouldIgnore) {
      stats.shouldBeIgnored++;
      stats.problematic.push(analysis);
      log(`⚠️  Potentially problematic: ${analysis.eventType} ${analysis.relativePath} (${analysis.category})`);
    } else {
      log(`✅ Valid change: ${analysis.eventType} ${analysis.relativePath} (${analysis.category})`);
    }
  });
  
  watcher.on('error', error => {
    log(`❌ Watcher error: ${error.message}`);
  });
  
  // Stop monitoring after duration
  setTimeout(() => {
    watcher.close();
    generateMonitoringReport(events, stats);
  }, duration);
  
  return watcher;
}

function generateMonitoringReport(events, stats) {
  log('\n📊 FILE WATCHING MONITORING REPORT');
  log('===================================');
  
  log(`\n📈 Summary:`);
  log(`  Total file events: ${stats.total}`);
  log(`  Events by category:`);
  
  Object.entries(stats.byCategory)
    .sort(([,a], [,b]) => b - a)
    .forEach(([category, count]) => {
      const percentage = ((count / stats.total) * 100).toFixed(1);
      log(`    ${category}: ${count} (${percentage}%)`);
    });
  
  if (stats.shouldBeIgnored > 0) {
    log(`\n⚠️  Problematic Events: ${stats.shouldBeIgnored}`);
    log(`  These files triggered events but should probably be ignored:`);
    
    const groupedProblematic = {};
    stats.problematic.forEach(event => {
      if (!groupedProblematic[event.category]) {
        groupedProblematic[event.category] = [];
      }
      groupedProblematic[event.category].push(event);
    });
    
    Object.entries(groupedProblematic).forEach(([category, events]) => {
      log(`\n  ${category} (${events.length} events):`);
      events.slice(0, 5).forEach(event => {
        log(`    ${event.eventType}: ${event.relativePath}`);
      });
      if (events.length > 5) {
        log(`    ... and ${events.length - 5} more`);
      }
    });
  } else {
    log(`\n✅ No problematic file events detected`);
  }
  
  // Recommendations
  log(`\n💡 Recommendations:`);
  
  if (stats.shouldBeIgnored > 0) {
    log(`  1. Add ignore patterns for ${stats.shouldBeIgnored} problematic file events`);
    log(`  2. Review Vite watch configuration`);
    log(`  3. Update .gitignore patterns`);
  } else {
    log(`  ✅ File watching configuration appears optimal`);
  }
  
  log(`  4. Run this monitor regularly during development`);
  log(`  5. Consider file watching performance impact`);
  
  // Performance insights
  const sourceCodeEvents = stats.byCategory['source-code'] || 0;
  const totalRelevantEvents = sourceCodeEvents + (stats.byCategory['styles'] || 0);
  const efficiency = totalRelevantEvents / stats.total * 100;
  
  log(`\n📊 Performance Insights:`);
  log(`  Relevant events (source + styles): ${totalRelevantEvents}/${stats.total} (${efficiency.toFixed(1)}%)`);
  
  if (efficiency < 70) {
    log(`  ⚠️  Low efficiency - consider improving ignore patterns`);
  } else if (efficiency > 90) {
    log(`  ✅ High efficiency - file watching is well optimized`);
  } else {
    log(`  🟡 Moderate efficiency - some room for improvement`);
  }
}

async function main() {
  const command = process.argv[2];
  const duration = parseInt(process.argv[3]) || MONITOR_DURATION;
  
  if (command === 'monitor') {
    try {
      // Check if chokidar is available
      require.resolve('chokidar');
    } catch (error) {
      log('❌ chokidar package not found. Installing...');
      const { execSync } = require('child_process');
      try {
        execSync('npm install chokidar --save-dev', { stdio: 'inherit' });
        log('✅ chokidar installed successfully');
      } catch (installError) {
        log('❌ Failed to install chokidar. Please install manually: npm install chokidar --save-dev');
        process.exit(1);
      }
    }
    
    const watcher = startMonitoring(duration);
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      log('🛑 Monitoring interrupted by user');
      watcher.close();
      process.exit(0);
    });
    
  } else {
    console.log('Usage: node monitor-file-watching.js [monitor] [duration_ms]');
    console.log('  monitor      - Start monitoring file watching events');
    console.log('  duration_ms  - Duration to monitor in milliseconds (default: 30000)');
    console.log('');
    console.log('Examples:');
    console.log('  node monitor-file-watching.js monitor          # Monitor for 30 seconds');
    console.log('  node monitor-file-watching.js monitor 60000    # Monitor for 60 seconds');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    log(`💥 Error: ${error.message}`);
    process.exit(1);
  });
}
