{"name": "desktop", "version": "0.1.0", "private": true, "main": "./dist/electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:electron": "tsc --project tsconfig.electron.json", "watch:electron": "tsc --project tsconfig.electron.json --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron": "wait-on tcp:5173 && electron .", "electron:dev": "concurrently \"npm run dev\" \"npm run build:electron && npm run watch:electron\" \"wait-on tcp:5173 && wait-on -d 1000 ./dist/electron/main.js && cross-env NODE_ENV=development electron .\"", "start": "npm run build:electron && cross-env NODE_ENV=production electron .", "test-fixes": "npm run build:electron && node test-fixes.js", "test-fixes:standalone": "npm run build:electron && node test-fixes-standalone.js", "test-fixes:electron": "npm run build:electron && cross-env NODE_ENV=test electron test-fixes.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "package": "npm run build && npm run build:electron && electron-forge package", "make": "npm run build && npm run build:electron && electron-forge make"}, "dependencies": {"@napi-rs/whisper": "^0.0.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-squirrel-startup": "^1.0.0", "framer-motion": "^12.12.1", "node-fetch": "^2.7.0", "nodejs-whisper": "^0.2.9", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@types/node-fetch": "^2.6.12", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^30.0.1", "electron-store": "^8.1.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "lucide-react": "^0.511.0", "postcss": "^8.5.3", "tailwindcss": "^3.3.0", "typescript": "^5.2.2", "vite": "^5.2.0", "wait-on": "^7.2.0"}}