{"name": "web-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:fast": "next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "perf:check": "node scripts/optimize-performance.js"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.1", "lucide-react": "^0.511.0", "next": "^14.0.4", "next-safe-action": "^7.9.8", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.2", "styled-jsx": "^5.1.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.4", "@shadcn/ui": "^0.0.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}