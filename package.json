{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev --workspace=packages/backend-services\" \"npm run dev --workspace=apps/web-portal\" \"npm run electron:dev --workspace=apps/desktop\"", "dev:web": "npm run dev --workspace=apps/web-portal", "dev:backend": "npm run dev --workspace=packages/backend-services", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev\"", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --check .", "format:fix": "prettier --write ."}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "concurrently": "^8.2.2", "electron": "^37.2.0", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "typescript": "^5.8.3"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@supabase/supabase-js": "^2.49.7", "dotenv": "^16.5.0", "form-data": "^4.0.3", "framer-motion": "^12.12.1", "node-fetch": "^3.3.2", "node-record-lpcm16": "^1.0.1", "nodejs-whisper": "^0.2.9"}}