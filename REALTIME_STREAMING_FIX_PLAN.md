# Real-time Streaming Fix Implementation Plan

## Root Cause Summary
The intermittent live transcription failure is caused by:
1. **IPC Event Listeners**: Using `once` instead of `on` for lifecycle events
2. **State Reset Issues**: Improper streaming state management between sessions
3. **Initialization Problems**: Streaming processor not properly reinitialized

## Critical Fixes Required

### 1. Fix IPC Event Listener Issue (Primary Fix)

**File:** `apps/desktop/electron/helpers/ipcHandlers.ts`
**Lines:** 457, 462

**Problem:**
```typescript
voiceService.once('recording-started', (data: any) => { ... })  // ❌ Removed after first use
voiceService.once('recording-stopped', (data: any) => { ... })  // ❌ Removed after first use
```

**Solution:**
```typescript
voiceService.on('recording-started', (data: any) => { ... })    // ✅ Persistent listener
voiceService.on('recording-stopped', (data: any) => { ... })    // ✅ Persistent listener
```

### 2. Fix Streaming State Reset Issue

**File:** `apps/desktop/electron/helpers/RealTimeVoiceService.ts`
**Method:** `cleanup()` around line 1100

**Problem:**
```typescript
private async cleanup(): Promise<void> {
  // Reset streaming state
  this.isStreamingMode = false        // ❌ Prevents subsequent streaming
  this.streamingSessionId = null
  // streamingInitialized not reset    // ❌ Skips reinitialization
}
```

**Solution:**
```typescript
private async cleanup(): Promise<void> {
  // Reset streaming state properly
  this.streamingSessionId = null
  // DON'T reset isStreamingMode here - let startRecording() determine it
  // Reset initialization flag to allow reinitialization
  this.streamingInitialized = false
}
```

### 3. Improve Streaming Processor Reinitialization

**File:** `apps/desktop/electron/helpers/RealTimeVoiceService.ts`
**Method:** `startRecording()` around line 446

**Current Logic:**
```typescript
if (!this.streamingInitialized && this.streamingProcessor) {
  const initialized = await this.streamingProcessor.startProcessing()
  // ...
}
```

**Enhanced Logic:**
```typescript
if (this.isStreamingMode && this.streamingProcessor) {
  // Always try to start processing for streaming mode
  if (!this.streamingInitialized) {
    const initialized = await this.streamingProcessor.startProcessing()
    if (!initialized) {
      console.warn('[RealTimeVoice] Failed to initialize streaming, falling back to batch mode')
      this.isStreamingMode = false
    } else {
      this.streamingInitialized = true
      this.streamingSessionId = `streaming-${Date.now()}`
    }
  } else {
    // Restart processing for new session
    await this.streamingProcessor.stopProcessing()
    const reinitialized = await this.streamingProcessor.startProcessing()
    if (reinitialized) {
      this.streamingSessionId = `streaming-${Date.now()}`
    } else {
      this.isStreamingMode = false
    }
  }
}
```

## Implementation Steps

### Step 1: Fix IPC Handlers (5 minutes)
```typescript
// In ipcHandlers.ts, change lines 457 and 462:
voiceService.on('recording-started', (data: any) => {
  forwardEvent('started', data)
  console.log('[IPC] Voice recording started, streaming mode:', data?.streamingMode)
})

voiceService.on('recording-stopped', (data: any) => {
  forwardEvent('stopped', data)
  console.log('[IPC] Voice recording stopped')
})
```

### Step 2: Fix Cleanup Method (5 minutes)
```typescript
// In RealTimeVoiceService.ts cleanup method:
private async cleanup(): Promise<void> {
  try {
    // Reset session but preserve streaming capability
    this.streamingSessionId = null
    this.streamingInitialized = false  // Allow reinitialization
    
    // Cleanup file
    if (this.currentFilePath) {
      await fs.unlink(this.currentFilePath).catch(() => {})
      this.currentFilePath = null
    }
    
    // Clear accumulated audio chunks
    this.audioChunks = []
    
    await this.checkStopAudioCapture()
  } catch (error) {
    console.error('[RealTimeVoice] Cleanup error:', error)
  }
}
```

### Step 3: Add Session Reset Method (10 minutes)
```typescript
// Add new method to RealTimeVoiceService:
public async resetForNewSession(): Promise<void> {
  if (this.isRecording) {
    console.warn('[RealTimeVoice] Cannot reset during active recording')
    return
  }
  
  try {
    // Reset streaming state
    this.streamingInitialized = false
    this.streamingSessionId = null
    
    // Restart streaming processor if available
    if (this.streamingProcessor) {
      await this.streamingProcessor.stopProcessing()
      console.log('[RealTimeVoice] Streaming processor reset for new session')
    }
  } catch (error) {
    console.error('[RealTimeVoice] Error resetting for new session:', error)
  }
}
```

### Step 4: Call Reset Between Sessions (5 minutes)
```typescript
// In InlineVoiceRecording.tsx, before starting new recording:
const handleStartRecording = async () => {
  // Reset transcription state
  transcription.reset(true)
  
  // Reset voice service for new session
  if (window.electronAPI?.resetVoiceSession) {
    await window.electronAPI.resetVoiceSession()
  }
  
  onStartRecording()
}
```

## Testing Plan

### Test Scenarios
1. **First Recording**: Verify live transcription works
2. **Second Recording**: Verify live transcription still works
3. **Multiple Sessions**: Test 5+ consecutive recordings
4. **Error Recovery**: Test after streaming errors
5. **Mode Switching**: Test batch fallback scenarios

### Success Criteria
- ✅ Live transcription works on first attempt
- ✅ Live transcription works on subsequent attempts
- ✅ No "Maximum update depth exceeded" errors
- ✅ Consistent streaming mode detection
- ✅ Proper event listener management

## Risks and Considerations

### Complexity Risk
- Still maintains complex streaming architecture
- Multiple failure points remain
- Difficult to debug when issues arise

### Performance Risk
- Continuous streaming processing overhead
- Memory usage from multiple event listeners
- Potential for new race conditions

### Maintenance Risk
- Complex state management across services
- Event listener lifecycle management
- Streaming processor state synchronization

## Recommendation

While these fixes should resolve the immediate issues, the **fundamental complexity remains**. The real-time streaming approach requires:
- 5+ interconnected services
- Complex event listener management
- Multiple state synchronization points
- Continuous background processing

**I still strongly recommend Option A (Simplified Batch Processing)** for long-term reliability and maintainability.
