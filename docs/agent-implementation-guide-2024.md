# AI Agent Implementation Guide 2024

## Overview

This guide provides step-by-step instructions for implementing a production-ready AI agent architecture that enhances the existing LLM orchestration system. The implementation follows a hybrid approach, combining custom development with selective framework integration.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                     Agent Orchestration Layer                   │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Memory    │  │  Planning   │  │ Tool Use    │  │ Reasoning│ │
│  │  Manager    │  │   Engine    │  │Orchestrator │  │  Engine │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    Enhanced LLM Service                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │           Existing LLM Orchestration Service               │ │
│  │        (Google Gemini + Prompt Templates)                  │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                      Tool Integration Layer                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ CRM Tools   │  │ RAG System  │  │Communication│  │Analysis │ │
│  │(SF, HubSpot)│  │ Integration │  │   Tools     │  │ Tools   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     Shared Infrastructure                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Redis     │  │  pgvector   │  │ Monitoring  │  │ Logging │ │
│  │  (State)    │  │ (Memory)    │  │(Prometheus) │  │(Winston)│ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Phase 1: Foundation Implementation (4 weeks)

### 1.1 Agent State Management

Create `src/services/agentStateService.ts`:

```typescript
import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';

export interface AgentState {
  conversationId: string;
  userId: string;
  context: CallContext;
  memory: ConversationMemory;
  currentGoal?: string;
  planningSteps?: PlanningStep[];
  toolResults?: ToolResult[];
  lastUpdated: Date;
}

export interface ConversationMemory {
  shortTerm: Message[];
  workingMemory: WorkingMemoryItem[];
  longTermSummary?: string;
  userPreferences?: UserPreferences;
}

export interface PlanningStep {
  id: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  toolsRequired?: string[];
  dependencies?: string[];
  result?: any;
}

export class AgentStateService {
  private redis: Redis;
  private supabase: any;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL);
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!
    );
  }

  async getAgentState(conversationId: string): Promise<AgentState | null> {
    // Try Redis first for active conversations
    const cached = await this.redis.get(`agent_state:${conversationId}`);
    if (cached) {
      return JSON.parse(cached);
    }

    // Fallback to database for persistent state
    const { data, error } = await this.supabase
      .from('agent_states')
      .select('*')
      .eq('conversation_id', conversationId)
      .single();

    if (error || !data) return null;

    const state = this.deserializeState(data);
    // Cache in Redis for future access
    await this.redis.setex(`agent_state:${conversationId}`, 3600, JSON.stringify(state));
    
    return state;
  }

  async updateAgentState(state: AgentState): Promise<void> {
    state.lastUpdated = new Date();

    // Update Redis cache
    await this.redis.setex(
      `agent_state:${state.conversationId}`, 
      3600, 
      JSON.stringify(state)
    );

    // Persist to database
    const serializedState = this.serializeState(state);
    await this.supabase
      .from('agent_states')
      .upsert(serializedState);
  }

  async createNewAgentState(conversationId: string, userId: string, context: CallContext): Promise<AgentState> {
    const state: AgentState = {
      conversationId,
      userId,
      context,
      memory: {
        shortTerm: [],
        workingMemory: [],
      },
      lastUpdated: new Date()
    };

    await this.updateAgentState(state);
    return state;
  }

  private serializeState(state: AgentState): any {
    return {
      conversation_id: state.conversationId,
      user_id: state.userId,
      context: JSON.stringify(state.context),
      memory: JSON.stringify(state.memory),
      current_goal: state.currentGoal,
      planning_steps: JSON.stringify(state.planningSteps || []),
      tool_results: JSON.stringify(state.toolResults || []),
      last_updated: state.lastUpdated.toISOString()
    };
  }

  private deserializeState(data: any): AgentState {
    return {
      conversationId: data.conversation_id,
      userId: data.user_id,
      context: JSON.parse(data.context),
      memory: JSON.parse(data.memory),
      currentGoal: data.current_goal,
      planningSteps: JSON.parse(data.planning_steps || '[]'),
      toolResults: JSON.parse(data.tool_results || '[]'),
      lastUpdated: new Date(data.last_updated)
    };
  }
}
```

### 1.2 Memory Management System

Create `src/services/memoryService.ts`:

```typescript
import { VectorSearchService } from './vectorSearchService';
import { AgentState } from './agentStateService';

export interface MemoryItem {
  id: string;
  content: string;
  type: 'conversation' | 'insight' | 'preference' | 'outcome';
  timestamp: Date;
  importance: number; // 1-10 scale
  embedding?: number[];
  metadata?: any;
}

export class MemoryService {
  private vectorSearch: VectorSearchService;
  private maxShortTermMemory = 20;
  private maxWorkingMemory = 10;

  constructor() {
    this.vectorSearch = new VectorSearchService();
  }

  async addMemory(state: AgentState, content: string, type: MemoryItem['type'], importance: number = 5): Promise<void> {
    const memoryItem: MemoryItem = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content,
      type,
      timestamp: new Date(),
      importance
    };

    // Add to short-term memory
    state.memory.shortTerm.push({
      role: 'system',
      content: `[MEMORY] ${type}: ${content}`,
      timestamp: memoryItem.timestamp
    });

    // Manage memory limits
    if (state.memory.shortTerm.length > this.maxShortTermMemory) {
      await this.consolidateMemory(state);
    }

    // Store important memories in vector database for long-term retrieval
    if (importance >= 7) {
      await this.storeInLongTermMemory(state.conversationId, memoryItem);
    }
  }

  async retrieveRelevantMemories(query: string, conversationId: string, limit: number = 5): Promise<MemoryItem[]> {
    try {
      const searchResults = await this.vectorSearch.search(
        `conversation:${conversationId} ${query}`, 
        limit
      );

      return searchResults.map(result => ({
        id: result.id,
        content: result.content,
        type: result.metadata?.type || 'conversation',
        timestamp: new Date(result.metadata?.timestamp),
        importance: result.metadata?.importance || 5,
        metadata: result.metadata
      }));
    } catch (error) {
      console.error('Memory retrieval error:', error);
      return [];
    }
  }

  async addToWorkingMemory(state: AgentState, item: any): Promise<void> {
    state.memory.workingMemory.push({
      id: `working_${Date.now()}`,
      content: JSON.stringify(item),
      timestamp: new Date(),
      type: 'working'
    });

    // Limit working memory size
    if (state.memory.workingMemory.length > this.maxWorkingMemory) {
      state.memory.workingMemory = state.memory.workingMemory.slice(-this.maxWorkingMemory);
    }
  }

  private async consolidateMemory(state: AgentState): Promise<void> {
    // Move older, less important memories to long-term storage
    const oldMemories = state.memory.shortTerm.slice(0, -this.maxShortTermMemory);
    
    for (const memory of oldMemories) {
      if (this.isImportantMemory(memory)) {
        await this.storeInLongTermMemory(state.conversationId, {
          id: `consolidated_${Date.now()}`,
          content: memory.content,
          type: 'conversation',
          timestamp: memory.timestamp || new Date(),
          importance: 6
        });
      }
    }

    // Keep only recent memories in short-term
    state.memory.shortTerm = state.memory.shortTerm.slice(-this.maxShortTermMemory);
  }

  private async storeInLongTermMemory(conversationId: string, memory: MemoryItem): Promise<void> {
    // Generate embedding for the memory
    const embedding = await this.vectorSearch.generateQueryEmbedding(memory.content);
    
    // Store in vector database with conversation context
    await this.vectorSearch.storeMemory({
      id: memory.id,
      content: memory.content,
      embedding,
      metadata: {
        conversation_id: conversationId,
        type: memory.type,
        timestamp: memory.timestamp.toISOString(),
        importance: memory.importance,
        ...memory.metadata
      }
    });
  }

  private isImportantMemory(memory: any): boolean {
    const importantKeywords = ['decision', 'preference', 'objection', 'commitment', 'next steps'];
    return importantKeywords.some(keyword => 
      memory.content.toLowerCase().includes(keyword)
    );
  }
}
```

### 1.3 Tool Integration Framework

Create `src/services/toolOrchestrator.ts`:

```typescript
import { AgentState } from './agentStateService';

export interface Tool {
  name: string;
  description: string;
  parameters: ToolParameter[];
  execute: (params: any, context: ToolContext) => Promise<ToolResult>;
  category: 'crm' | 'communication' | 'analysis' | 'knowledge' | 'utility';
  requiredPermissions?: string[];
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  required: boolean;
  default?: any;
}

export interface ToolContext {
  agentState: AgentState;
  userId: string;
  conversationId: string;
}

export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: any;
  followUpActions?: string[];
}

export class ToolOrchestrator {
  private tools: Map<string, Tool> = new Map();

  constructor() {
    this.registerDefaultTools();
  }

  registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
  }

  async executeTool(toolName: string, parameters: any, context: ToolContext): Promise<ToolResult> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return {
        success: false,
        error: `Tool '${toolName}' not found`
      };
    }

    try {
      // Validate parameters
      const validationResult = this.validateParameters(tool.parameters, parameters);
      if (!validationResult.valid) {
        return {
          success: false,
          error: `Parameter validation failed: ${validationResult.error}`
        };
      }

      // Check permissions
      if (tool.requiredPermissions && !this.hasPermissions(context.userId, tool.requiredPermissions)) {
        return {
          success: false,
          error: 'Insufficient permissions to execute this tool'
        };
      }

      // Execute tool
      const result = await tool.execute(parameters, context);
      
      // Log tool usage
      console.log(`[ToolOrchestrator] Executed ${toolName}:`, {
        success: result.success,
        userId: context.userId,
        conversationId: context.conversationId
      });

      return result;
    } catch (error) {
      console.error(`[ToolOrchestrator] Error executing ${toolName}:`, error);
      return {
        success: false,
        error: `Tool execution failed: ${error.message}`
      };
    }
  }

  getAvailableTools(category?: string): Tool[] {
    const allTools = Array.from(this.tools.values());
    return category ? allTools.filter(tool => tool.category === category) : allTools;
  }

  private registerDefaultTools(): void {
    // CRM Search Tool
    this.registerTool({
      name: 'search_crm',
      description: 'Search for customer information in CRM system',
      category: 'crm',
      parameters: [
        { name: 'query', type: 'string', description: 'Search query', required: true },
        { name: 'type', type: 'string', description: 'Type of record (contact, account, opportunity)', required: false, default: 'contact' }
      ],
      execute: async (params, context) => {
        // Implementation would integrate with actual CRM API
        return {
          success: true,
          data: { message: `Searching CRM for: ${params.query}` },
          metadata: { source: 'crm', type: params.type }
        };
      }
    });

    // Knowledge Search Tool (RAG Integration)
    this.registerTool({
      name: 'search_knowledge',
      description: 'Search company knowledge base for relevant information',
      category: 'knowledge',
      parameters: [
        { name: 'query', type: 'string', description: 'Search query', required: true },
        { name: 'limit', type: 'number', description: 'Maximum results', required: false, default: 5 }
      ],
      execute: async (params, context) => {
        // Integration with existing RAG system
        const vectorSearch = new (await import('./vectorSearchService')).VectorSearchService();
        const results = await vectorSearch.search(params.query, params.limit);
        
        return {
          success: true,
          data: results,
          metadata: { source: 'knowledge_base', query: params.query }
        };
      }
    });

    // Email Tool
    this.registerTool({
      name: 'send_email',
      description: 'Send email to prospect or team member',
      category: 'communication',
      parameters: [
        { name: 'to', type: 'string', description: 'Recipient email', required: true },
        { name: 'subject', type: 'string', description: 'Email subject', required: true },
        { name: 'body', type: 'string', description: 'Email body', required: true },
        { name: 'cc', type: 'array', description: 'CC recipients', required: false }
      ],
      execute: async (params, context) => {
        // Implementation would integrate with email service
        return {
          success: true,
          data: { message: `Email sent to ${params.to}` },
          followUpActions: ['log_activity', 'schedule_followup']
        };
      }
    });
  }

  private validateParameters(toolParams: ToolParameter[], providedParams: any): { valid: boolean; error?: string } {
    for (const param of toolParams) {
      if (param.required && !(param.name in providedParams)) {
        return { valid: false, error: `Missing required parameter: ${param.name}` };
      }

      if (param.name in providedParams) {
        const value = providedParams[param.name];
        const expectedType = param.type;
        
        if (!this.isValidType(value, expectedType)) {
          return { valid: false, error: `Parameter ${param.name} must be of type ${expectedType}` };
        }
      }
    }

    return { valid: true };
  }

  private isValidType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string': return typeof value === 'string';
      case 'number': return typeof value === 'number';
      case 'boolean': return typeof value === 'boolean';
      case 'object': return typeof value === 'object' && value !== null;
      case 'array': return Array.isArray(value);
      default: return true;
    }
  }

  private hasPermissions(userId: string, requiredPermissions: string[]): boolean {
    // Implementation would check user permissions
    // For now, return true (implement proper permission checking)
    return true;
  }
}
```

### 1.4 Enhanced LLM Orchestration

Update `src/services/llmOrchestrationService.ts`:

```typescript
// Add agent capabilities to existing LLM service
import { AgentStateService, AgentState } from './agentStateService';
import { MemoryService } from './memoryService';
import { ToolOrchestrator } from './toolOrchestrator';

export class EnhancedLLMOrchestrationService {
  private agentStateService: AgentStateService;
  private memoryService: MemoryService;
  private toolOrchestrator: ToolOrchestrator;

  constructor() {
    this.agentStateService = new AgentStateService();
    this.memoryService = new MemoryService();
    this.toolOrchestrator = new ToolOrchestrator();
  }

  async generateWithAgent(
    prompt: string, 
    conversationId: string, 
    userId: string, 
    context: CallContext,
    options: any = {}
  ): Promise<any> {
    try {
      // Get or create agent state
      let agentState = await this.agentStateService.getAgentState(conversationId);
      if (!agentState) {
        agentState = await this.agentStateService.createNewAgentState(conversationId, userId, context);
      }

      // Update context
      agentState.context = { ...agentState.context, ...context };

      // Retrieve relevant memories
      const relevantMemories = await this.memoryService.retrieveRelevantMemories(
        prompt, 
        conversationId, 
        5
      );

      // Build enhanced prompt with agent context
      const enhancedPrompt = this.buildAgentPrompt(prompt, agentState, relevantMemories);

      // Generate response with tool calling capability
      const response = await this.generateWithToolCalling(enhancedPrompt, agentState, options);

      // Store interaction in memory
      await this.memoryService.addMemory(
        agentState,
        `User: ${prompt}\nAssistant: ${response.text}`,
        'conversation',
        6
      );

      // Update agent state
      await this.agentStateService.updateAgentState(agentState);

      return response;
    } catch (error) {
      console.error('Agent generation error:', error);
      // Fallback to regular generation
      return await this.generateText({ prompt, ...options });
    }
  }

  private buildAgentPrompt(prompt: string, agentState: AgentState, memories: any[]): string {
    let enhancedPrompt = `${CLOSEZLY_SYSTEM_PROMPT}

AGENT CONTEXT:
- Conversation ID: ${agentState.conversationId}
- Current Goal: ${agentState.currentGoal || 'Assist with sales conversation'}

RELEVANT MEMORIES:
${memories.map(m => `- ${m.content}`).join('\n')}

AVAILABLE TOOLS:
${this.toolOrchestrator.getAvailableTools().map(t => `- ${t.name}: ${t.description}`).join('\n')}

CURRENT CONTEXT:
${this.formatContext(agentState.context)}

USER REQUEST: ${prompt}

Instructions:
1. Consider the conversation history and relevant memories
2. If you need additional information, use available tools
3. Provide helpful, contextual assistance
4. Remember important information for future interactions

RESPONSE:`;

    return enhancedPrompt;
  }

  private async generateWithToolCalling(prompt: string, agentState: AgentState, options: any): Promise<any> {
    // First, generate initial response to determine if tools are needed
    const initialResponse = await this.generateText({ prompt, ...options });

    // Simple tool detection (in production, use more sophisticated parsing)
    const toolCallPattern = /\[TOOL:(\w+)\s*\((.*?)\)\]/g;
    let match;
    let finalResponse = initialResponse.text;

    while ((match = toolCallPattern.exec(initialResponse.text)) !== null) {
      const toolName = match[1];
      const toolParams = this.parseToolParams(match[2]);

      const toolResult = await this.toolOrchestrator.executeTool(
        toolName,
        toolParams,
        {
          agentState,
          userId: agentState.userId,
          conversationId: agentState.conversationId
        }
      );

      // Replace tool call with result
      finalResponse = finalResponse.replace(match[0], 
        toolResult.success ? 
          `[TOOL_RESULT: ${JSON.stringify(toolResult.data)}]` : 
          `[TOOL_ERROR: ${toolResult.error}]`
      );

      // Store tool result in working memory
      await this.memoryService.addToWorkingMemory(agentState, {
        tool: toolName,
        params: toolParams,
        result: toolResult
      });
    }

    return {
      ...initialResponse,
      text: finalResponse,
      toolsUsed: agentState.memory.workingMemory.filter(item => item.type === 'working')
    };
  }

  private parseToolParams(paramString: string): any {
    try {
      return JSON.parse(`{${paramString}}`);
    } catch {
      return {};
    }
  }

  private formatContext(context: any): string {
    return Object.entries(context)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  }
}
```

## Environment Configuration

Add to `.env`:

```env
# Agent Configuration
AGENT_MEMORY_TTL=3600
AGENT_MAX_TOOLS_PER_REQUEST=5
AGENT_PLANNING_ENABLED=true
AGENT_PROACTIVE_SUGGESTIONS=true

# Tool Configuration
TOOL_TIMEOUT_MS=10000
TOOL_RATE_LIMIT_PER_MINUTE=60

# Memory Configuration
MEMORY_CONSOLIDATION_INTERVAL=3600
MEMORY_IMPORTANCE_THRESHOLD=7
```

## Database Schema Updates

Add to Supabase migrations:

```sql
-- Agent States Table
CREATE TABLE agent_states (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  conversation_id TEXT UNIQUE NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  context JSONB,
  memory JSONB,
  current_goal TEXT,
  planning_steps JSONB DEFAULT '[]',
  tool_results JSONB DEFAULT '[]',
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Memory Storage Table
CREATE TABLE agent_memories (
  id TEXT PRIMARY KEY,
  conversation_id TEXT,
  content TEXT,
  embedding vector(3072),
  metadata JSONB,
  importance INTEGER DEFAULT 5,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_agent_states_conversation ON agent_states(conversation_id);
CREATE INDEX idx_agent_states_user ON agent_states(user_id);
CREATE INDEX idx_agent_memories_conversation ON agent_memories(conversation_id);
CREATE INDEX idx_agent_memories_embedding ON agent_memories USING ivfflat (embedding vector_cosine_ops);
```

## Testing Strategy

### Unit Tests
```typescript
// Example test for MemoryService
describe('MemoryService', () => {
  it('should store and retrieve memories correctly', async () => {
    const memoryService = new MemoryService();
    const agentState = createMockAgentState();
    
    await memoryService.addMemory(agentState, 'Test memory', 'insight', 8);
    
    const memories = await memoryService.retrieveRelevantMemories(
      'test', 
      agentState.conversationId
    );
    
    expect(memories).toHaveLength(1);
    expect(memories[0].content).toContain('Test memory');
  });
});
```

### Integration Tests
```typescript
describe('Agent Integration', () => {
  it('should handle complete agent workflow', async () => {
    const service = new EnhancedLLMOrchestrationService();
    
    const response = await service.generateWithAgent(
      'Help me prepare for my call with Acme Corp',
      'test-conversation',
      'test-user',
      { prospectName: 'John Doe', companyName: 'Acme Corp' }
    );
    
    expect(response.success).toBe(true);
    expect(response.text).toBeDefined();
  });
});
```

## Monitoring & Observability

### Metrics to Track
- Agent response time
- Tool usage frequency
- Memory retrieval accuracy
- User satisfaction scores
- Error rates by component

### Logging Strategy
```typescript
// Enhanced logging for agent operations
const agentLogger = {
  agentInteraction: (conversationId: string, action: string, metadata: any) => {
    console.log(`[AGENT] ${conversationId}: ${action}`, metadata);
  },
  toolUsage: (toolName: string, success: boolean, duration: number) => {
    console.log(`[TOOL] ${toolName}: ${success ? 'SUCCESS' : 'FAILED'} (${duration}ms)`);
  },
  memoryOperation: (operation: string, conversationId: string, details: any) => {
    console.log(`[MEMORY] ${operation} for ${conversationId}:`, details);
  }
};
```

## Next Steps

After Phase 1 completion:
1. Implement planning engine (Phase 2)
2. Add reasoning patterns (ReAct, CoT)
3. Integrate LangGraph for multi-agent scenarios (Phase 3)
4. Optimize performance and add advanced monitoring (Phase 4)

---

*This implementation guide provides the foundation for building a production-ready agent architecture that enhances the existing LLM orchestration system with memory, planning, and tool-use capabilities.*
