# RAG System Diagnostic Guide

Since the knowledge search is returning 0 results, let's diagnose the issue step by step.

## Step 1: Check Database Content

Run this SQL in your Supabase SQL Editor to see what data exists:

```sql
-- Check if we have any documents
SELECT 
  COUNT(*) as total_documents,
  COUNT(CASE WHEN processing_status = 'completed' THEN 1 END) as completed_documents
FROM documents;

-- Check if we have any chunks with embeddings
SELECT 
  COUNT(*) as total_chunks,
  COUNT(CASE WHEN embedding IS NOT NULL THEN 1 END) as chunks_with_embeddings,
  COUNT(CASE WHEN pg_typeof(embedding) = 'vector'::regtype THEN 1 END) as vector_embeddings,
  COUNT(CASE WHEN pg_typeof(embedding) = 'text'::regtype THEN 1 END) as text_embeddings
FROM document_chunks;

-- Check user data for test token user
SELECT 
  d.id,
  d.filename,
  d.processing_status,
  d.chunk_count,
  d.user_id
FROM documents d
WHERE d.user_id = '550e8400-e29b-41d4-a716-************'  -- Test token user
ORDER BY d.created_at DESC;

-- Check chunks for test token user
SELECT 
  dc.id,
  LEFT(dc.content, 100) as content_preview,
  pg_typeof(dc.embedding) as embedding_type,
  dc.user_id
FROM document_chunks dc
WHERE dc.user_id = '550e8400-e29b-41d4-a716-************'  -- Test token user
LIMIT 5;
```

## Step 2: Test Vector Search Function Directly

Run this SQL to test the search function:

```sql
-- Test the vector search function with a sample embedding
DO $$
DECLARE
  test_embedding vector(1536);
  result_count int;
  test_user_id uuid := '550e8400-e29b-41d4-a716-************';
BEGIN
  -- Get a sample embedding from the test user's data
  SELECT embedding INTO test_embedding 
  FROM document_chunks 
  WHERE user_id = test_user_id
    AND embedding IS NOT NULL 
    AND pg_typeof(embedding) = 'vector'::regtype
  LIMIT 1;
  
  IF test_embedding IS NOT NULL THEN
    RAISE NOTICE 'Found test embedding with % dimensions', vector_dims(test_embedding);
    
    -- Test the function with very low threshold
    SELECT COUNT(*) INTO result_count
    FROM search_document_chunks(
      test_embedding,
      0.001,  -- Very low threshold
      10,     -- Limit
      test_user_id, -- Filter by test user
      NULL    -- No document filter
    );
    
    RAISE NOTICE 'Vector search found % results for test user', result_count;
    
    -- Show actual results
    RAISE NOTICE 'Sample results:';
    FOR rec IN 
      SELECT id, LEFT(content, 50) as content_preview, similarity
      FROM search_document_chunks(test_embedding, 0.001, 3, test_user_id, NULL)
    LOOP
      RAISE NOTICE 'ID: %, Content: %, Similarity: %', rec.id, rec.content_preview, rec.similarity;
    END LOOP;
    
  ELSE
    RAISE NOTICE 'No valid vector embeddings found for test user';
  END IF;
END;
$$;
```

## Step 3: Manual cURL Tests

Run these commands in your terminal:

```bash
# Test 1: Check server health
curl -s http://localhost:4000/api/v1/knowledge/health | jq .

# Test 2: Test with different user (the one that has documents)
curl -X POST http://localhost:4000/api/v1/knowledge/search \
  -H "Content-Type: application/json" \
  -H "x-user-id: 022daa89-07af-4a3e-a81a-6cce7f8b3b12" \
  -d '{"query": "artificial intelligence", "limit": 5, "threshold": 0.001}' | jq .

# Test 3: Test with very broad query
curl -X POST http://localhost:4000/api/v1/knowledge/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token-for-api-testing" \
  -d '{"query": "the", "limit": 10, "threshold": 0.001}' | jq .

# Test 4: Test LLM without RAG (should work)
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token-for-api-testing" \
  -d '{
    "context": {"currentTranscriptSegment": "AI discussion"},
    "assistanceType": "general_assistance", 
    "query": "What is AI?",
    "useRAG": false
  }' | jq .

# Test 5: Test LLM with RAG enabled
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token-for-api-testing" \
  -d '{
    "context": {"currentTranscriptSegment": "AI discussion"},
    "assistanceType": "general_assistance",
    "query": "What is machine learning?", 
    "useRAG": true
  }' | jq .
```

## Step 4: Check Server Logs

Look at your backend server terminal for any error messages, especially:
- `[VectorSearch]` messages
- `[LLMService]` RAG-related messages
- Any PostgreSQL errors

## Expected Results

**If everything is working:**
- Step 1 should show documents and chunks with vector embeddings
- Step 2 should find results with the vector search function
- Step 3 tests should return results with `"totalResults" > 0`

**If still 0 results:**
- Check if the test token user (`550e8400-e29b-41d4-a716-************`) has any documents
- Check if embeddings are properly converted to vector type
- Check if the vector search function is working correctly

## Common Issues and Solutions

1. **No documents for test user**: Upload a document through the web portal
2. **Embeddings still as text**: Re-run the embedding conversion SQL
3. **Vector search function not working**: Check PostgreSQL logs for errors
4. **User ID mismatch**: Verify which user ID the test token resolves to

Run through these steps and let me know what you find!
