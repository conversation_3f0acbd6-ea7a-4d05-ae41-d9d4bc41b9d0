# Undetectable Operation Implementation Guide for Closezly

## Overview

This guide provides a complete implementation plan for making the Closezly desktop app undetectable during screen recordings and screenshots while remaining fully visible to the user. The implementation is based on **actual code analysis** from Cheating Daddy and Glass by Pickle repositories, plus research from Free Cluely and Electron's native capabilities.

## ACTUAL IMPLEMENTATION RESEARCH FROM REPOSITORIES

### Cheating Daddy Implementation Analysis

**Key Findings from `src/utils/window.js`:**

```javascript
// Core BrowserWindow Configuration
const mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    frame: false,
    transparent: true,
    hasShadow: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    hiddenInMissionControl: true, // macOS specific
    webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        backgroundThrottling: false,
        enableBlinkFeatures: 'GetDisplayMedia',
        webSecurity: true,
        allowRunningInsecureContent: false,
    },
    backgroundColor: '#00000000',
});

// CRITICAL: Content Protection Implementation
mainWindow.setContentProtection(true);
mainWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });

// Platform-specific always-on-top enhancement
if (process.platform === 'win32') {
    mainWindow.setAlwaysOnTop(true, 'screen-saver', 1);
}
```

**Dynamic Content Protection Toggle:**
```javascript
// From src/index.js - IPC Handler
ipcMain.handle('update-content-protection', async (event, contentProtection) => {
    try {
        if (mainWindow) {
            // Get content protection setting from localStorage via cheddar
            const contentProtection = await mainWindow.webContents.executeJavaScript('cheddar.getContentProtection()');
            mainWindow.setContentProtection(contentProtection);
            console.log('Content protection updated:', contentProtection);
        }
        return { success: true };
    } catch (error) {
        console.error('Error updating content protection:', error);
        return { success: false, error: error.message };
    }
});
```

**Click-through Implementation:**
```javascript
// Toggle click-through with hotkey (Cmd/Ctrl+M)
globalShortcut.register(keybinds.toggleClickThrough, () => {
    mouseEventsIgnored = !mouseEventsIgnored;
    if (mouseEventsIgnored) {
        mainWindow.setIgnoreMouseEvents(true, { forward: true });
        console.log('Mouse events ignored');
    } else {
        mainWindow.setIgnoreMouseEvents(false);
        console.log('Mouse events enabled');
    }
    mainWindow.webContents.send('click-through-toggled', mouseEventsIgnored);
});
```

**Stealth Features from `src/utils/stealthFeatures.js`:**
```javascript
function applyStealthMeasures(mainWindow) {
    // Hide from alt-tab on Windows
    if (process.platform === 'win32') {
        mainWindow.setSkipTaskbar(true);
    }

    // Hide from Mission Control on macOS
    if (process.platform === 'darwin') {
        mainWindow.setHiddenInMissionControl(true);
    }

    // Set random app name in menu bar (macOS)
    if (process.platform === 'darwin') {
        const { app } = require('electron');
        const randomName = getCurrentRandomDisplayName();
        app.setName(randomName);
    }

    // Content protection
    mainWindow.setContentProtection(true);

    // Randomize window user agent
    const userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
    ];
    const randomUA = userAgents[Math.floor(Math.random() * userAgents.length)];
    mainWindow.webContents.setUserAgent(randomUA);
}
```

### Glass by Pickle Implementation Analysis

**Key Findings from `src/window/windowManager.js`:**

```javascript
// Content Protection Management
let isContentProtectionOn = true;

const setContentProtection = (status) => {
    isContentProtectionOn = status;
    console.log(`[Protection] Content protection toggled to: ${isContentProtectionOn}`);
    windowPool.forEach(win => {
        if (win && !win.isDestroyed()) {
            win.setContentProtection(isContentProtectionOn);
        }
    });
};

const toggleContentProtection = () => {
    const newStatus = !getContentProtectionStatus();
    setContentProtection(newStatus);
    return newStatus;
};
```

**Advanced Window Configuration:**
```javascript
const header = new BrowserWindow({
    width: DEFAULT_WINDOW_WIDTH,
    height: HEADER_HEIGHT,
    frame: false,
    transparent: true,
    vibrancy: false,
    hasShadow: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    hiddenInMissionControl: true,
    resizable: false,
    focusable: true,
    acceptFirstMouse: true,
    webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        backgroundThrottling: false,
        webSecurity: false,
    },
    useContentSize: true,
    disableAutoHideCursor: true,
});

// Apply content protection to all windows
header.setContentProtection(isContentProtectionOn);
header.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
```

**Multi-Window Content Protection:**
```javascript
// Apply to all feature windows
const createFeatureWindow = (name) => {
    const window = new BrowserWindow({
        // ... other options
        skipTaskbar: true,
        hiddenInMissionControl: true,
    });

    window.setContentProtection(isContentProtectionOn);
    window.setVisibleOnAllWorkspaces(true, {visibleOnFullScreen: true});

    if (process.platform === 'darwin') {
        window.setWindowButtonVisibility(false);
    }
};
```

## Current Closezly Codebase Analysis

### Existing Toggle and Hotkey Patterns

**From `apps/desktop/electron/helpers/ShortcutsHelper.ts`:**

```typescript
// Current hotkey patterns
private registerToggleOverlayShortcut(): void {
  // Toggle overlay visibility with Alt+H
  this.registerShortcut('Alt+H', () => {
    const isVisible = WindowHelper.toggleVisibility()
    AppState.getInstance().setOverlayVisibility(isVisible)

    // Notify renderer process
    const mainWindow = WindowHelper.getMainWindow()
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('closezly:visibility-changed-by-hotkey', isVisible)
    }
  })
}

// Other existing shortcuts:
// Alt+Q - Trigger AI query
// Alt+R - Toggle call recording
// Alt+V - Voice recording
// Ctrl+Shift+V - Alternative voice recording
// Escape - Cancel voice recording
```

**From `apps/desktop/electron/helpers/WindowHelper.ts`:**

```typescript
// Current visibility management
public setVisibility(visible: boolean): void {
  if (!this.mainWindow) return

  if (visible) {
    this.mainWindow.setFocusable(true)
    this.mainWindow.show()
    // Already has click-through logic!
    if (!this.isDevelopment) {
      this.mainWindow.setIgnoreMouseEvents(false)
    }
  } else {
    this.mainWindow.hide()
    this.mainWindow.setFocusable(false)
    if (!this.isDevelopment) {
      this.mainWindow.setIgnoreMouseEvents(true, { forward: true })
    }
  }
}

// Current BrowserWindow configuration
const mainWindow = new BrowserWindow({
  width: 400,
  height: 600,
  frame: false,
  transparent: true,
  alwaysOnTop: true,
  skipTaskbar: true,
  resizable: false,
  webPreferences: {
    nodeIntegration: false,
    contextIsolation: true,
    preload: path.join(__dirname, '../preload.ts')
  }
})
```

**From `apps/desktop/electron/helpers/AppState.ts`:**

```typescript
// Current state management pattern
public setOverlayVisibility(visible: boolean): void {
  this.state.overlayVisible = visible
  this.emit('overlay-visibility-changed', visible)
  this.syncStateToRenderer()
}

public toggleOverlayVisibility(): boolean {
  this.state.overlayVisible = !this.state.overlayVisible
  this.emit('overlay-visibility-changed', this.state.overlayVisible)
  this.syncStateToRenderer()
  return this.state.overlayVisible
}
```

**From `apps/desktop/electron/helpers/ipcHandlers.ts`:**

```typescript
// Current IPC pattern
ipcMain.on('closezly:toggle-visibility', (event) => {
  const isVisible = WindowHelper.toggleVisibility()
  AppState.getInstance().setOverlayVisibility(isVisible)
  event.returnValue = isVisible
})
```

### Key Insights from Closezly Codebase

1. **Already has click-through functionality** in WindowHelper.setVisibility()
2. **Established hotkey patterns** using Alt+ combinations
3. **Solid state management** with AppState and event emission
4. **IPC communication patterns** already in place
5. **Missing setContentProtection** implementation
6. **No undetectable mode state tracking**

## IMPLEMENTATION PLAN FOR CLOSEZLY

### Phase 1: Core Undetectable Operation (Following Actual Repository Patterns)

**1. Extend WindowHelper.createMainWindow() - Based on Cheating Daddy Pattern**

```typescript
// In apps/desktop/electron/helpers/WindowHelper.ts
public createMainWindow(): BrowserWindow {
  this.mainWindow = new BrowserWindow({
    width: 400,
    height: 600,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    skipTaskbar: true,
    hiddenInMissionControl: true, // Add this for macOS stealth
    resizable: false,
    hasShadow: false, // Add this to reduce visibility
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      backgroundThrottling: false, // Add this for consistent performance
      preload: path.join(__dirname, '../preload.ts')
    },
    backgroundColor: '#00000000', // Add transparent background
  })

  // CRITICAL: Apply content protection immediately after creation
  this.mainWindow.setContentProtection(true)
  this.mainWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true })

  // Platform-specific enhancements from Cheating Daddy
  if (process.platform === 'win32') {
    this.mainWindow.setAlwaysOnTop(true, 'screen-saver', 1)
  }

  // Apply comprehensive stealth measures from Cheating Daddy
  this.applyStealthMeasures()

  // Start periodic title randomization for additional stealth
  this.startTitleRandomization()

  return this.mainWindow
}

// Enhanced stealth measures based on Cheating Daddy's stealthFeatures.js
private applyStealthMeasures(): void {
  if (!this.mainWindow) return

  try {
    console.log('[WindowHelper] Applying comprehensive stealth measures...')

    // Hide from Mission Control on macOS (from Cheating Daddy)
    if (process.platform === 'darwin') {
      this.mainWindow.setHiddenInMissionControl(true)
      console.log('[WindowHelper] Hidden from macOS Mission Control')

      // Set random app name in menu bar for additional stealth
      const { app } = require('electron')
      const randomNames = [
        'System Configuration', 'Audio Settings', 'Network Monitor',
        'Performance Monitor', 'System Information', 'Device Manager',
        'Background Services', 'System Updates', 'Security Center'
      ]
      const randomName = randomNames[Math.floor(Math.random() * randomNames.length)]
      app.setName(randomName)
      console.log(`[WindowHelper] Set app name to: ${randomName}`)
    }

    // Hide from alt-tab on Windows (from Cheating Daddy)
    if (process.platform === 'win32') {
      this.mainWindow.setSkipTaskbar(true)
      console.log('[WindowHelper] Hidden from Windows taskbar')
    }

    // Randomize window user agent (from Cheating Daddy)
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
    ]
    const randomUA = userAgents[Math.floor(Math.random() * userAgents.length)]
    this.mainWindow.webContents.setUserAgent(randomUA)
    console.log('[WindowHelper] Set random user agent')

    console.log('[WindowHelper] All stealth measures applied successfully')
  } catch (error) {
    console.warn('[WindowHelper] Could not apply all stealth measures:', error)
  }
}

// Periodic title randomization for additional stealth (from Cheating Daddy)
private titleRandomizationInterval: NodeJS.Timeout | null = null

private startTitleRandomization(): void {
  if (!this.mainWindow) return

  const titles = [
    'System Configuration', 'Audio Settings', 'Network Monitor',
    'Performance Monitor', 'System Information', 'Device Manager',
    'Background Services', 'System Updates', 'Security Center',
    'Task Manager', 'Resource Monitor', 'System Properties',
    'Network Connections', 'Audio Devices', 'Display Settings',
    'Power Options', 'System Tools', 'Hardware Monitor'
  ]

  // Change title every 30-60 seconds (from Cheating Daddy pattern)
  this.titleRandomizationInterval = setInterval(() => {
    try {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        const randomTitle = titles[Math.floor(Math.random() * titles.length)]
        this.mainWindow.setTitle(randomTitle)
      } else if (this.titleRandomizationInterval) {
        clearInterval(this.titleRandomizationInterval)
        this.titleRandomizationInterval = null
      }
    } catch (error) {
      console.warn('[WindowHelper] Could not update window title:', error)
      if (this.titleRandomizationInterval) {
        clearInterval(this.titleRandomizationInterval)
        this.titleRandomizationInterval = null
      }
    }
  }, 30000 + Math.random() * 30000) // 30-60 seconds random interval
}
```

**2. Add Content Protection Management - Based on Glass Pattern**

```typescript
// In apps/desktop/electron/helpers/WindowHelper.ts
private contentProtectionEnabled: boolean = true // Default ON as requested

public setContentProtection(enabled: boolean): boolean {
  if (!this.mainWindow) return false

  try {
    // Check platform support
    if (process.platform === 'darwin' || process.platform === 'win32') {
      this.mainWindow.setContentProtection(enabled)
      this.contentProtectionEnabled = enabled

      // Update AppState
      AppState.getInstance().setUndetectableMode(enabled)

      console.log(`[WindowHelper] Content protection ${enabled ? 'enabled' : 'disabled'}`)
      return true
    } else {
      console.warn('[WindowHelper] Content protection not supported on this platform')
      return false
    }
  } catch (error) {
    console.error('[WindowHelper] Failed to set content protection:', error)
    return false
  }
}

public toggleContentProtection(): boolean {
  const newState = !this.contentProtectionEnabled
  const success = this.setContentProtection(newState)
  return success ? newState : this.contentProtectionEnabled
}

public isContentProtectionEnabled(): boolean {
  return this.contentProtectionEnabled
}
```

**3. Add AppState Management - Following Closezly Patterns**

```typescript
// In apps/desktop/electron/helpers/AppState.ts
interface AppStateData {
  // ... existing properties
  undetectableMode: boolean
}

private state: AppStateData = {
  // ... existing state
  undetectableMode: true, // Default ON as requested
}

// Undetectable mode methods (following existing overlay visibility pattern)
public setUndetectableMode(enabled: boolean): void {
  this.state.undetectableMode = enabled
  this.emit('undetectable-mode-changed', enabled)
  this.syncStateToRenderer()
}

public toggleUndetectableMode(): boolean {
  this.state.undetectableMode = !this.state.undetectableMode
  this.emit('undetectable-mode-changed', this.state.undetectableMode)
  this.syncStateToRenderer()
  return this.state.undetectableMode
}

public isUndetectableMode(): boolean {
  return this.state.undetectableMode
}

// Remove click-through mode methods - keeping it simple
```

**4. Add IPC Handlers - Following Existing Patterns**

```typescript
// In apps/desktop/electron/helpers/ipcHandlers.ts
function setupIpcHandlers(): void {
  // ... existing handlers

  // Toggle undetectable mode (following toggle-visibility pattern)
  ipcMain.on('closezly:toggle-undetectable-mode', (event) => {
    const currentState = AppState.getInstance().isUndetectableMode()
    const success = WindowHelper.setContentProtection(!currentState)
    const newState = success ? !currentState : currentState
    event.returnValue = newState
  })

  // Set undetectable mode
  ipcMain.on('closezly:set-undetectable-mode', (event, enabled: boolean) => {
    const success = WindowHelper.setContentProtection(enabled)
    event.returnValue = success
  })

  // Get undetectable status
  ipcMain.handle('closezly:get-undetectable-status', async () => {
    return {
      contentProtection: WindowHelper.isContentProtectionEnabled()
    }
  })
}
```

### Phase 2: Hotkey Implementation - Following Cheating Daddy Patterns

**1. Add Simple Undetectable Mode Toggle**

```typescript
// In apps/desktop/electron/helpers/ShortcutsHelper.ts
public registerShortcuts(): void {
  this.registerToggleOverlayShortcut()
  this.registerTriggerAIQueryShortcut()
  this.registerToggleCallRecordingShortcut()
  this.registerVoiceRecordingShortcuts()
  this.registerMoveOverlayShortcuts()
  this.registerUndetectableModeShortcut() // Add this single shortcut
}

private registerUndetectableModeShortcut(): void {
  // Simple toggle with Alt+I (Invisible) - following Alt+ pattern
  this.registerShortcut('Alt+I', () => {
    const mainWindow = WindowHelper.getMainWindow()
    if (mainWindow) {
      const newState = WindowHelper.toggleContentProtection()
      mainWindow.webContents.send('closezly:undetectable-mode-changed', newState)
    }
  })
}
```

**2. Add Preload API Extensions**

```typescript
// In apps/desktop/electron/preload.ts
const electronAPI = {
  // ... existing methods

  // Simple undetectable mode controls
  toggleUndetectableMode: () => ipcRenderer.sendSync('closezly:toggle-undetectable-mode'),
  setUndetectableMode: (enabled: boolean) =>
    ipcRenderer.sendSync('closezly:set-undetectable-mode', enabled),
  getUndetectableStatus: () => ipcRenderer.invoke('closezly:get-undetectable-status'),

  // Event listener for undetectable mode changes
  onUndetectableModeChanged: (callback: (enabled: boolean) => void) => {
    const listener = (_event: any, enabled: boolean) => callback(enabled)
    ipcRenderer.on('closezly:undetectable-mode-changed', listener)
    return () => {
      ipcRenderer.removeListener('closezly:undetectable-mode-changed', listener)
    }
  },
}
```

### Phase 3: UI Integration and Status Indicators

**1. Remove Status Indicators from Main App (Maintaining Minimal Design)**

```typescript
// In apps/desktop/src/components/App.tsx
// NO status indicators in main app header - keeping Closezly's clean, minimal design
// Status will only be shown in settings component when user accesses it

useEffect(() => {
  // Still listen for changes for internal state management
  const unsubscribe = window.electronAPI.onUndetectableModeChanged((enabled) => {
    // Update internal state if needed, but no UI indicators in main app
    console.log(`[App] Undetectable mode: ${enabled ? 'ON' : 'OFF'}`)
  })

  return unsubscribe
}, [])

// Main app header remains clean and minimal - no status indicators
```

**2. Enhanced Settings Component with Status Indicator and Scrolling**

```typescript
// Create new component: apps/desktop/src/components/UndetectableSettings.tsx
import React, { useState, useEffect } from 'react'

export const UndetectableSettings: React.FC = () => {
  const [isProtected, setIsProtected] = useState(true)

  useEffect(() => {
    window.electronAPI.getUndetectableStatus().then(status => {
      setIsProtected(status.contentProtection)
    })

    // Listen for changes to update status in real-time
    const unsubscribe = window.electronAPI.onUndetectableModeChanged((enabled) => {
      setIsProtected(enabled)
    })

    return unsubscribe
  }, [])

  const handleToggle = () => {
    const newState = !isProtected
    const success = window.electronAPI.setUndetectableMode(newState)
    if (success) {
      setIsProtected(newState)
    }
  }

  return (
    <div className="undetectable-settings" style={{
      maxHeight: '400px',
      overflowY: 'auto',
      padding: '16px',
      // Follow Closezly's minimal design patterns
      fontSize: '14px',
      color: 'var(--text-primary, #333)',
      backgroundColor: 'var(--bg-primary, #fff)'
    }}>
      {/* Status indicator - only shown in settings */}
      <div className="status-display" style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '16px',
        padding: '8px 12px',
        borderRadius: '6px',
        backgroundColor: isProtected ? 'var(--success-bg, #f0f9f0)' : 'var(--warning-bg, #fff8f0)',
        border: `1px solid ${isProtected ? 'var(--success-border, #d4edda)' : 'var(--warning-border, #ffeaa7)'}`,
        fontSize: '13px'
      }}>
        <span style={{ marginRight: '8px', fontSize: '16px' }}>
          {isProtected ? '🔒' : '👁️'}
        </span>
        <span style={{ fontWeight: '500' }}>
          {isProtected ? 'Protected - Invisible to screen recordings' : 'Visible - Will appear in screen recordings'}
        </span>
      </div>

      <h3 style={{
        margin: '0 0 16px 0',
        fontSize: '16px',
        fontWeight: '600',
        color: 'var(--text-primary, #333)'
      }}>
        Undetectable Mode
      </h3>

      <div className="setting-item" style={{ marginBottom: '16px' }}>
        <label style={{
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          fontSize: '14px'
        }}>
          <input
            type="checkbox"
            checked={isProtected}
            onChange={handleToggle}
            style={{
              marginRight: '8px',
              accentColor: 'var(--primary-color, #007bff)'
            }}
          />
          Screen Recording Protection
        </label>
        <small style={{
          display: 'block',
          marginTop: '4px',
          color: 'var(--text-secondary, #666)',
          fontSize: '12px',
          lineHeight: '1.4'
        }}>
          When enabled, the app won't appear in most screen recordings or screenshots.
          May not work with all screen sharing tools.
        </small>
      </div>

      <div className="hotkey-info" style={{
        padding: '12px',
        backgroundColor: 'var(--bg-secondary, #f8f9fa)',
        borderRadius: '6px',
        border: '1px solid var(--border-color, #e9ecef)'
      }}>
        <h4 style={{
          margin: '0 0 8px 0',
          fontSize: '13px',
          fontWeight: '600',
          color: 'var(--text-primary, #333)'
        }}>
          Keyboard Shortcut:
        </h4>
        <p style={{
          margin: '0',
          fontSize: '12px',
          color: 'var(--text-secondary, #666)'
        }}>
          <kbd style={{
            padding: '2px 6px',
            backgroundColor: 'var(--kbd-bg, #e9ecef)',
            border: '1px solid var(--kbd-border, #adb5bd)',
            borderRadius: '3px',
            fontSize: '11px',
            fontFamily: 'monospace'
          }}>Alt+I</kbd> - Toggle undetectable mode on/off
        </p>
      </div>

      <div className="stealth-info" style={{
        marginTop: '16px',
        padding: '12px',
        backgroundColor: 'var(--info-bg, #f8f9fa)',
        borderRadius: '6px',
        border: '1px solid var(--info-border, #dee2e6)'
      }}>
        <h4 style={{
          margin: '0 0 8px 0',
          fontSize: '13px',
          fontWeight: '600',
          color: 'var(--text-primary, #333)'
        }}>
          Additional Stealth Features:
        </h4>
        <ul style={{
          margin: '0',
          paddingLeft: '16px',
          fontSize: '12px',
          color: 'var(--text-secondary, #666)',
          lineHeight: '1.4'
        }}>
          <li>Hidden from Mission Control (macOS)</li>
          <li>Hidden from taskbar (Windows)</li>
          <li>Random app name in menu bar</li>
          <li>Periodic window title randomization</li>
          <li>Random user agent</li>
        </ul>
      </div>
    </div>
  )
}
```

## REALISTIC LIMITATIONS AND TESTING STRATEGY

### Known Limitations from Repository Research

**From Electron GitHub Issues:**
- **Issue #14415**: QuickTime can still capture on macOS despite setContentProtection
- **Issue #39452**: Doesn't work with always-on-top mode (which Closezly uses)
- **Issue #19880**: Not working on macOS in some cases
- **Modern web-based tools**: Zoom, Google Meet web clients may bypass protection

**From Cheating Daddy Implementation:**
- Uses stealth measures beyond just setContentProtection
- Implements random process names and window titles
- Uses platform-specific enhancements (screen-saver level on Windows)

**From Glass Implementation:**
- Claims "truly invisible" but likely uses multiple techniques
- Implements liquid glass effects for additional visual obfuscation
- Uses comprehensive window management across multiple displays

### Comprehensive Testing Strategy

**Screen Recording Software Testing:**
```bash
# Test with these applications:
# macOS:
- QuickTime Player (known to bypass setContentProtection)
- OBS Studio
- ScreenSearch
- CleanMyMac X screen recording
- Zoom screen sharing
- Google Meet screen sharing

# Windows:
- Windows Game Bar (Win+G)
- OBS Studio
- Bandicam
- Camtasia
- Teams screen sharing
- Zoom screen sharing

# Cross-platform:
- Discord screen sharing
- Slack screen sharing
- Chrome browser screen sharing
```

**Testing Methodology:**
1. **Start Closezly** with undetectable mode ON
2. **Start screen recording** with each application
3. **Verify invisibility** - app should not appear in recording
4. **Test toggle functionality** - Alt+I should toggle visibility in recordings
5. **Test click-through** - Alt+M should make window transparent to clicks
6. **Test emergency mode** - Alt+E should hide for 3 seconds
7. **Verify user visibility** - app should always be visible to user

### Platform-Specific Implementation Details

**Enhanced Platform-Specific Implementation (from Cheating Daddy Analysis):**

```typescript
// In WindowHelper.createMainWindow() - Platform-specific enhancements
public createMainWindow(): BrowserWindow {
  this.mainWindow = new BrowserWindow({
    width: 400,
    height: 600,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    skipTaskbar: true,
    hiddenInMissionControl: true, // Add this for macOS stealth
    resizable: false,
    hasShadow: false, // Add this to reduce visibility
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      backgroundThrottling: false, // Add this for consistent performance
      preload: path.join(__dirname, '../preload.ts')
    },
    backgroundColor: '#00000000', // Add transparent background
  })

  // Apply platform-specific always-on-top enhancements
  if (process.platform === 'win32') {
    // Use screen-saver level for maximum always-on-top (from Cheating Daddy)
    this.mainWindow.setAlwaysOnTop(true, 'screen-saver', 1)
    console.log('[WindowHelper] Applied Windows screen-saver level always-on-top')
  }

  // Enhanced visibility settings for all workspaces
  this.mainWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true })

  // Apply content protection and stealth measures
  this.mainWindow.setContentProtection(true)
  this.applyStealthMeasures()
  this.startTitleRandomization()

  return this.mainWindow
}
```

**Linux Fallback:**
```typescript
// Linux has limited setContentProtection support
if (process.platform === 'linux') {
  console.warn('[Stealth] Limited content protection on Linux')
  // Focus on other stealth measures:
  // - Transparent overlay
  // - Skip taskbar
  // - Random window titles
}
```

## IMPLEMENTATION CHECKLIST

### Core Implementation (Phase 1)
- [ ] **Extend WindowHelper.createMainWindow()** with content protection and enhanced BrowserWindow config
- [ ] **Add comprehensive stealth measures** from Cheating Daddy analysis:
  - [ ] `hiddenInMissionControl: true` for macOS
  - [ ] `skipTaskbar: true` for Windows
  - [ ] `hasShadow: false` to reduce visibility
  - [ ] `backgroundThrottling: false` for consistent performance
  - [ ] `backgroundColor: '#00000000'` for transparent background
- [ ] **Implement platform-specific enhancements**:
  - [ ] Windows: `setAlwaysOnTop(true, 'screen-saver', 1)` for maximum priority
  - [ ] macOS: Random app name in menu bar
  - [ ] All platforms: `setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true })`
- [ ] **Add periodic title randomization** (30-60 second intervals)
- [ ] **Implement random user agent** setting
- [ ] **Add AppState tracking** for undetectable mode
- [ ] **Create IPC handlers** for toggle functionality
- [ ] **Test basic content protection** with OBS/QuickTime

### Hotkey Implementation (Phase 2)
- [ ] **Add Alt+I shortcut** for simple undetectable mode toggle
- [ ] **Extend ShortcutsHelper** with single toggle method
- [ ] **Test hotkey works correctly** for on/off toggle

### UI Integration (Phase 3)
- [ ] **Extend preload.ts** with simple toggle API
- [ ] **Add status indicator** (🔒 Protected / 👁️ Visible)
- [ ] **Create simple settings toggle** component
- [ ] **Add event listener** for state changes
- [ ] **Test UI updates** when toggling on/off

### Platform-Specific Enhancements
- [ ] **macOS**: Test hiddenInMissionControl effectiveness
- [ ] **macOS**: Implement random app name setting
- [ ] **Windows**: Test screen-saver level always-on-top
- [ ] **Linux**: Implement graceful fallback behavior
- [ ] **Cross-platform**: Test user agent randomization

### Comprehensive Testing
- [ ] **OBS Studio**: Verify complete invisibility when ON
- [ ] **QuickTime**: Test known bypass scenario
- [ ] **Zoom/Teams**: Test enterprise screen sharing
- [ ] **Web-based tools**: Test Google Meet, Discord
- [ ] **Toggle functionality**: Verify Alt+I switches ON/OFF in real-time
- [ ] **Settings toggle**: Test UI toggle works correctly
- [ ] **User visibility**: Ensure app always visible to user

## FINAL IMPLEMENTATION SUMMARY

### What We Learned from Repository Research

**Cheating Daddy's Approach:**
- Uses `setContentProtection(true)` as primary method
- Implements comprehensive stealth measures beyond content protection
- Uses platform-specific enhancements (screen-saver level on Windows)
- Provides user control through localStorage settings and hotkeys
- Implements click-through functionality with Cmd/Ctrl+M

**Glass by Pickle's Approach:**
- Manages content protection across multiple windows
- Uses sophisticated window management and positioning
- Implements toggle functionality with proper state management
- Claims "truly invisible" through multiple techniques

**Key Technical Insights:**
1. **setContentProtection is not foolproof** - some tools can bypass it
2. **Multiple stealth techniques** are needed for maximum effectiveness
3. **Platform-specific enhancements** significantly improve stealth
4. **User control is essential** - toggle functionality is critical
5. **Proper state management** ensures consistent behavior

### Recommended Implementation for Closezly

**Default Behavior:**
- **Undetectable mode ON by default** (as requested)
- **Content protection enabled** immediately on window creation
- **Additional stealth measures** applied automatically
- **User retains full control** through hotkeys and settings

**Simple Hotkey:**
- **Alt+I** - Toggle undetectable mode ON/OFF (Invisible)

**Expected Effectiveness:**
- **Will work with**: OBS Studio, many desktop recording tools
- **May not work with**: QuickTime, some web-based screen sharing
- **Always-on-top limitation**: May reduce effectiveness (known Electron issue)
- **User visibility**: App remains fully visible to user at all times

### Next Steps

1. **Start with Phase 1** - implement core content protection
2. **Test immediately** with OBS Studio to verify basic functionality
3. **Add hotkeys** for user control (Phase 2)
4. **Implement UI integration** for status feedback (Phase 3)
5. **Test comprehensively** with all major screen recording tools
6. **Document limitations** clearly for users

This implementation provides the best possible undetectable functionality within Electron's constraints while maintaining Closezly's existing architecture and user experience patterns.

## USAGE GUIDE FOR USERS

### Default Behavior (As Requested)
- **Closezly starts with undetectable mode ON** by default
- **Screen recording protection active** immediately
- **App remains fully visible to you** at all times
- **All existing functionality unchanged** (AI queries, voice recording, etc.)

### Simple Keyboard Control
- **Alt+I** - Toggle undetectable mode ON/OFF
- **Alt+H** - Toggle app visibility (existing Closezly shortcut)

### Visual Feedback
- **🔒 Protected** - Undetectable mode is ON (invisible to screen recordings)
- **�️ Visible** - Undetectable mode is OFF (visible in screen recordings)

### Settings Panel
- **Screen Recording Protection** - Simple checkbox to toggle undetectable mode
- **Keyboard Shortcut** - Shows Alt+I for toggle

### What to Expect

**Will be invisible in:**
- OBS Studio recordings ✅
- Many desktop screen recording applications ✅
- Some video conferencing screen shares ✅

**May still be visible in:**
- QuickTime Player recordings ⚠️ (known Electron limitation)
- Some web-based screen sharing tools ⚠️
- Advanced screen capture software ⚠️

**Always visible to you:**
- App remains fully visible on your screen ✅
- All functionality works normally ✅
- Toggle controls always accessible ✅

### Troubleshooting
- **If protection seems ineffective**: Try Alt+I to toggle off and on
- **If hotkey doesn't work**: Check if another app is capturing Alt+I
- **For Linux users**: Limited protection available, but basic stealth features still work

This implementation provides the best possible undetectable functionality within Electron's technical constraints while maintaining full user control and Closezly's existing user experience.
