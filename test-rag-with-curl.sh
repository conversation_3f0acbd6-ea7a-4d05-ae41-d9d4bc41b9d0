#!/bin/bash

# Test RAG System with cURL
# This script tests the RAG system using cURL commands to avoid Node.js hanging issues

echo "🚀 Testing RAG System with cURL..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test 1: Check server health
echo "1️⃣ Testing server health..."
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/health_response.json http://localhost:4000/api/knowledge/health 2>/dev/null)
HTTP_CODE="${HEALTH_RESPONSE: -3}"

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Server is healthy${NC}"
    echo "Response: $(cat /tmp/health_response.json)"
else
    echo -e "${RED}❌ Server is not responding (HTTP: $HTTP_CODE)${NC}"
    echo "Please start the backend server: cd packages/backend-services && npm run dev"
    exit 1
fi

echo ""

# Test 2: Test knowledge search with test token
echo "2️⃣ Testing knowledge search..."
SEARCH_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/search_response.json \
  -X POST http://localhost:4000/api/knowledge/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token-for-api-testing" \
  -d '{
    "query": "artificial intelligence machine learning",
    "limit": 5,
    "threshold": 0.01
  }' 2>/dev/null)

SEARCH_HTTP_CODE="${SEARCH_RESPONSE: -3}"

if [ "$SEARCH_HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Knowledge search completed${NC}"
    
    # Parse results count
    RESULTS_COUNT=$(cat /tmp/search_response.json | grep -o '"totalResults":[0-9]*' | cut -d':' -f2)
    echo "Results found: $RESULTS_COUNT"
    
    if [ "$RESULTS_COUNT" -gt "0" ]; then
        echo -e "${GREEN}🎉 RAG search is working! Found $RESULTS_COUNT results${NC}"
        echo "Sample results:"
        cat /tmp/search_response.json | grep -o '"filename":"[^"]*"' | head -3
    else
        echo -e "${YELLOW}⚠️  RAG search returns 0 results - embedding format issue${NC}"
    fi
else
    echo -e "${RED}❌ Knowledge search failed (HTTP: $SEARCH_HTTP_CODE)${NC}"
    echo "Response: $(cat /tmp/search_response.json)"
fi

echo ""

# Test 3: Test LLM assist without RAG
echo "3️⃣ Testing LLM assist without RAG..."
LLM_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/llm_response.json \
  -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token-for-api-testing" \
  -d '{
    "context": {
      "currentTranscriptSegment": "User is asking about AI",
      "onScreenText": "AI discussion",
      "userQuery": "What is AI?"
    },
    "assistanceType": "general_assistance",
    "query": "What is artificial intelligence?",
    "useRAG": false
  }' 2>/dev/null)

LLM_HTTP_CODE="${LLM_RESPONSE: -3}"

if [ "$LLM_HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ LLM assist works without RAG${NC}"
    
    # Check if response contains text
    if grep -q '"success":true' /tmp/llm_response.json; then
        echo "LLM is responding correctly"
    else
        echo -e "${YELLOW}⚠️  LLM response might have issues${NC}"
    fi
else
    echo -e "${RED}❌ LLM assist failed (HTTP: $LLM_HTTP_CODE)${NC}"
    echo "Response: $(cat /tmp/llm_response.json)"
fi

echo ""

# Test 4: Test LLM assist with RAG enabled
echo "4️⃣ Testing LLM assist with RAG enabled..."
LLM_RAG_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/llm_rag_response.json \
  -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token-for-api-testing" \
  -d '{
    "context": {
      "currentTranscriptSegment": "User is asking about AI",
      "onScreenText": "AI discussion",
      "userQuery": "What is machine learning?"
    },
    "assistanceType": "general_assistance",
    "query": "What is machine learning and how does it work?",
    "useRAG": true
  }' 2>/dev/null)

LLM_RAG_HTTP_CODE="${LLM_RAG_RESPONSE: -3}"

if [ "$LLM_RAG_HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ LLM assist with RAG completed${NC}"
    
    # Check if response contains sources
    if grep -q '"sources"' /tmp/llm_rag_response.json; then
        SOURCES_COUNT=$(cat /tmp/llm_rag_response.json | grep -o '"sources":\[[^]]*\]' | wc -l)
        if [ "$SOURCES_COUNT" -gt "0" ]; then
            echo -e "${GREEN}🎉 RAG is working! LLM used knowledge base sources${NC}"
        else
            echo -e "${YELLOW}⚠️  RAG enabled but no sources used${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  RAG enabled but no sources in response${NC}"
    fi
else
    echo -e "${RED}❌ LLM assist with RAG failed (HTTP: $LLM_RAG_HTTP_CODE)${NC}"
    echo "Response: $(cat /tmp/llm_rag_response.json)"
fi

echo ""

# Summary
echo "🎯 RAG System Test Summary:"
echo ""

if [ "$HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ Backend server is running${NC}"
else
    echo -e "${RED}❌ Backend server issues${NC}"
fi

if [ "$SEARCH_HTTP_CODE" = "200" ] && [ "$RESULTS_COUNT" -gt "0" ]; then
    echo -e "${GREEN}✅ Knowledge search is working${NC}"
elif [ "$SEARCH_HTTP_CODE" = "200" ]; then
    echo -e "${YELLOW}⚠️  Knowledge search works but returns 0 results${NC}"
else
    echo -e "${RED}❌ Knowledge search is broken${NC}"
fi

if [ "$LLM_HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ LLM service is working${NC}"
else
    echo -e "${RED}❌ LLM service is broken${NC}"
fi

if [ "$LLM_RAG_HTTP_CODE" = "200" ]; then
    echo -e "${GREEN}✅ LLM with RAG is working${NC}"
else
    echo -e "${RED}❌ LLM with RAG is broken${NC}"
fi

echo ""
echo "💡 Next steps:"
if [ "$RESULTS_COUNT" = "0" ]; then
    echo "1. Fix embedding format: Run the SQL script to convert string embeddings to vectors"
    echo "2. Create vector search function: Run create-vector-search-function.sql in Supabase"
fi
echo "3. Test desktop app integration"
echo "4. Verify complete end-to-end flow"

# Cleanup
rm -f /tmp/health_response.json /tmp/search_response.json /tmp/llm_response.json /tmp/llm_rag_response.json
