# Closezly Marketing Website Implementation Guide

## Overview
Create a modern, professional marketing/landing website for Closezly - an AI sales co-pilot tool. This will be a separate site from the existing web portal, focused on email collection and showcasing the product's value proposition.

## Technology Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui components
- **Database**: Supabase (reuse existing setup)
- **Deployment**: Vercel
- **Email Collection**: Simple waitlist signup with FOMO counter (250+ already signed up)

## Project Structure
```
apps/marketing-site/
├── app/
│   ├── layout.tsx
│   ├── page.tsx (landing page)
│   ├── services/
│   │   └── page.tsx
│   ├── thank-you/
│   │   └── page.tsx
│   ├── api/
│   │   └── waitlist/
│   │       └── route.ts
│   └── globals.css
├── components/
│   ├── ui/ (shadcn components)
│   ├── hero.tsx
│   ├── features.tsx
│   ├── waitlist-form.tsx
│   ├── navigation.tsx
│   └── footer.tsx
├── lib/
│   ├── supabase.ts
│   └── utils.ts
├── package.json
├── tailwind.config.js
├── tsconfig.json
└── next.config.js
```

## Database Schema
Create a new table in your existing Supabase database:

```sql
-- Create waitlist_signups table
CREATE TABLE waitlist_signups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  source TEXT DEFAULT 'marketing-site'
);

-- Enable RLS
ALTER TABLE waitlist_signups ENABLE ROW LEVEL SECURITY;

-- Create policy for inserting (public can insert)
CREATE POLICY "Anyone can insert waitlist signups" ON waitlist_signups
  FOR INSERT WITH CHECK (true);

-- Create policy for reading count (public can read count)
CREATE POLICY "Anyone can read waitlist count" ON waitlist_signups
  FOR SELECT USING (true);
```

## Key Components

### 1. Hero Section
- Modern gradient background
- Compelling headline: "The Undetectable AI Sales Co-Pilot"
- Subheading highlighting key benefits
- Primary CTA: "Join Early Access Waitlist"
- FOMO counter: "250+ sales professionals already signed up"

### 2. Features Section
Highlight these key features from the PRD:
- **Real-Time Call Assistance**: AI suggestions during live sales calls
- **Undetectable Overlay**: Invisible during screen sharing
- **CRM Integration**: Seamless Salesforce & HubSpot integration
- **Knowledge Management**: Custom playbooks and product info
- **Post-Call Analytics**: AI-generated summaries and insights
- **Voice & Screen Context**: Understands what you see and hear

### 3. Waitlist Form
- Email input with validation
- Success/error states
- FOMO elements (counter, urgency messaging)
- Thank you page redirect
- Email confirmation

## Design Guidelines

### Color Scheme (Professional B2B)
- Primary: Deep blue (#1e40af) or dark navy
- Secondary: Emerald green (#059669) for CTAs
- Neutral: Gray scale for text and backgrounds
- Accent: Subtle gradients for modern feel

### Typography
- Headlines: Bold, large (text-4xl to text-6xl)
- Body: Clean, readable (text-lg)
- CTAs: Medium weight, clear contrast

### Layout Principles
- Clean, minimal design
- Generous white space
- Mobile-first responsive
- Fast loading with optimized images
- Professional B2B aesthetic

## Content Strategy

### Landing Page Copy
**Hero Headline**: "Close More Deals with Your Undetectable AI Sales Co-Pilot"

**Subheading**: "Get real-time AI guidance during sales calls without anyone knowing. Closezly helps B2B sales professionals handle objections, access product knowledge, and close deals faster."

**Features Headlines**:
- "Never Miss a Beat in Sales Calls"
- "Your Personal Sales Intelligence"
- "Seamless CRM Integration"
- "Undetectable & Professional"

### Value Propositions
- Increase close rates with real-time AI assistance
- Reduce call preparation time
- Handle objections confidently
- Access product knowledge instantly
- Professional and undetectable
- Works with existing CRM systems

## Implementation Steps

### Phase 1: Setup (Day 1)
1. Create new Next.js app in `apps/marketing-site`
2. Install dependencies (Next.js, Tailwind, Shadcn/ui, Supabase)
3. Set up basic project structure
4. Configure Tailwind and Shadcn/ui
5. Set up Supabase client connection

### Phase 2: Database & API (Day 1)
1. Create waitlist_signups table in Supabase
2. Set up RLS policies
3. Create API route for email submission
4. Test database connection and email insertion

### Phase 3: Components (Day 2)
1. Build Hero component with modern design
2. Create Features grid component
3. Build Waitlist form with validation
4. Create Navigation and Footer components
5. Set up Layout component

### Phase 4: Pages (Day 2)
1. Landing page (Hero + Features + Waitlist)
2. Services page (detailed features)
3. Thank you page (post-signup)
4. Add proper SEO meta tags

### Phase 5: Styling & Polish (Day 3)
1. Implement responsive design
2. Add animations and micro-interactions
3. Optimize for performance
4. Test across devices and browsers

### Phase 6: Deployment (Day 3)
1. Set up Vercel deployment
2. Configure environment variables
3. Set up custom domain (if needed)
4. Test production deployment

## FOMO Elements
- Counter: "250+ sales professionals already signed up"
- Urgency: "Join early access - limited spots available"
- Social proof: "Trusted by B2B sales teams"
- Scarcity: "Early access closing soon"

## Technical Requirements

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Package.json Dependencies
```json
{
  "dependencies": {
    "next": "^14.0.4",
    "react": "^18",
    "react-dom": "^18",
    "@supabase/supabase-js": "^2.49.7",
    "tailwindcss": "^3.3.0",
    "lucide-react": "^0.511.0",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "tailwind-merge": "^3.3.0"
  }
}
```

## Success Metrics
- Email signup conversion rate
- Page load speed (<3 seconds)
- Mobile responsiveness score
- SEO optimization score
- User engagement (time on page)

## Deployment Checklist
- [ ] Database table created and tested
- [ ] Environment variables configured
- [ ] All pages responsive and tested
- [ ] Email submission working
- [ ] Thank you page functional
- [ ] SEO meta tags added
- [ ] Performance optimized
- [ ] Vercel deployment successful
- [ ] Custom domain configured (optional)

## Next Steps After Launch
1. Monitor email signups and conversion rates
2. A/B test different headlines and CTAs
3. Add analytics tracking (Google Analytics)
4. Consider adding more landing pages for different audiences
5. Implement email marketing automation
6. Gather user feedback for improvements

---

This implementation should take 2-3 days for a skilled developer and result in a professional, conversion-optimized marketing website for Closezly.
