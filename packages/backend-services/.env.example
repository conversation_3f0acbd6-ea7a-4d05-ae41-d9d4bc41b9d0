# Existing environment variables
GEMINI_API_KEY=

# RAG System Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_EMBEDDING_MODEL=text-embedding-3-large
OPENAI_EMBEDDING_DIMENSIONS=3072

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# RAG Processing Configuration
MAX_FILE_SIZE_MB=50
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
DEFAULT_SIMILARITY_THRESHOLD=0.7
MAX_SEARCH_RESULTS=50

# Queue Configuration
BULL_REDIS_HOST=localhost
BULL_REDIS_PORT=6379
BULL_REDIS_PASSWORD=your_redis_password

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Rate Limiting
UPLOAD_RATE_LIMIT_WINDOW_MS=900000
UPLOAD_RATE_LIMIT_MAX=10
SEARCH_RATE_LIMIT_WINDOW_MS=60000
SEARCH_RATE_LIMIT_MAX=60

# Agent Configuration
AGENT_MEMORY_TTL=3600
AGENT_MAX_TOOLS_PER_REQUEST=5
AGENT_PLANNING_ENABLED=true
AGENT_PROACTIVE_SUGGESTIONS=true
AGENT_DEFAULT_IMPORTANCE_THRESHOLD=7

# Tool Configuration
TOOL_TIMEOUT_MS=10000
TOOL_RATE_LIMIT_PER_MINUTE=60
TOOL_MAX_CONCURRENT_EXECUTIONS=3

# Memory Configuration
MEMORY_CONSOLIDATION_INTERVAL=3600
MEMORY_IMPORTANCE_THRESHOLD=7
MEMORY_MAX_SHORT_TERM=20
MEMORY_MAX_WORKING=10

# Agent Features
AGENT_TOOL_CALLING_ENABLED=true
AGENT_MEMORY_ENABLED=true
AGENT_STATE_PERSISTENCE_ENABLED=true
