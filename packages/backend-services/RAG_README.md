# RAG (Retrieval-Augmented Generation) Implementation

This document describes the RAG system implementation for the Closezly project, providing enhanced AI capabilities through document knowledge retrieval.

## Overview

The RAG system enables the AI assistant to access and utilize information from uploaded documents, providing more accurate and contextual responses based on user's knowledge base.

## Architecture

### Core Components

1. **DocumentProcessingService** - Handles document upload, text extraction, chunking, and embedding generation
2. **VectorSearchService** - Performs semantic search using OpenAI embeddings and pgvector
3. **Knowledge API** - RESTful endpoints for document management and search
4. **Enhanced LLM Orchestration** - Integrates RAG with existing Gemini LLM service

### Technology Stack

- **Embeddings**: OpenAI text-embedding-3-large (3072 dimensions)
- **Vector Database**: PostgreSQL with pgvector extension
- **Caching**: Redis for search result caching
- **Queue Processing**: Bull queues for document processing
- **File Processing**: Support for PDF, DOCX, TXT, HTML

## Setup Instructions

### 1. Environment Configuration

Copy the environment template and configure your settings:

```bash
cp .env.example .env
```

Required environment variables:
```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_EMBEDDING_MODEL=text-embedding-3-large
OPENAI_EMBEDDING_DIMENSIONS=3072

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

### 2. Database Setup

Run the database migration to create the required tables:

```sql
-- Execute the migration file
\i src/database/migrations/001_create_rag_schema.sql
```

### 3. Install Dependencies

The required dependencies are already installed:
- openai
- pdf-parse
- mammoth
- multer
- bull
- redis
- ioredis

### 4. Start Services

Ensure Redis is running:
```bash
redis-server
```

Start the backend service:
```bash
npm run dev
```

## API Endpoints

### Document Management

#### Upload Document
```http
POST /api/v1/knowledge/documents
Content-Type: multipart/form-data

{
  "document": <file>,
  "metadata": "{\"category\": \"manual\"}" // optional
}
```

#### Get Document Status
```http
GET /api/v1/knowledge/documents/:documentId/status
```

#### List Documents
```http
GET /api/v1/knowledge/documents?page=1&limit=20&status=completed
```

#### Delete Document
```http
DELETE /api/v1/knowledge/documents/:documentId
```

### Search and Retrieval

#### Search Documents
```http
POST /api/v1/knowledge/search
Content-Type: application/json

{
  "query": "How to configure the system?",
  "limit": 10,
  "threshold": 0.7,
  "documentIds": ["uuid1", "uuid2"] // optional
}
```

### Analytics and Health

#### Get Analytics
```http
GET /api/v1/knowledge/analytics
```

#### Health Check
```http
GET /api/v1/knowledge/health
```

## Usage Examples

### Basic Document Upload and Search

```typescript
// Upload a document
const formData = new FormData();
formData.append('document', file);

const uploadResponse = await fetch('/api/v1/knowledge/documents', {
  method: 'POST',
  headers: {
    'X-User-ID': 'user123'
  },
  body: formData
});

// Search for relevant content
const searchResponse = await fetch('/api/v1/knowledge/search', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-User-ID': 'user123'
  },
  body: JSON.stringify({
    query: 'How to setup authentication?',
    limit: 5,
    threshold: 0.75
  })
});
```

### RAG-Enhanced Text Generation

```typescript
import { LLMOrchestrationService } from './services/llmOrchestrationService';

// Generate response with RAG context
const response = await LLMOrchestrationService.generateTextWithRAG({
  prompt: 'How do I configure the database?',
  useRAG: true,
  userId: 'user123',
  contextLimit: 5,
  similarityThreshold: 0.75,
  temperature: 0.7,
  maxOutputTokens: 500
});

if (response.success) {
  console.log('Response:', response.text);
  console.log('Sources:', response.sources);
  console.log('Context used:', response.contextUsed);
}
```

## Configuration Options

### Processing Configuration

```typescript
// Document processing settings
const processingConfig = {
  maxFileSizeMB: 50,
  chunkSize: 1000,
  chunkOverlap: 200,
  defaultSimilarityThreshold: 0.7,
  maxSearchResults: 50
};
```

### Rate Limiting

```typescript
// API rate limits
const rateLimits = {
  upload: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10 // 10 uploads per window
  },
  search: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 60 // 60 searches per minute
  }
};
```

## Monitoring and Analytics

### Search Analytics

The system tracks search queries and performance metrics:

- Query patterns and frequency
- Search result relevance
- Processing times
- Cache hit rates

### Health Monitoring

Health check endpoint provides status for:

- Database connectivity
- Redis connectivity
- OpenAI API connectivity
- Vector search functionality

## Performance Optimization

### Caching Strategy

- Search results cached in Redis (1 hour TTL)
- Cache keys based on query and search options
- Automatic cache invalidation on document updates

### Vector Search Optimization

- HNSW index for fast similarity search
- Configurable similarity thresholds
- Batch embedding generation
- Optimized chunk sizes

## Security Considerations

### Authentication

- User-based document isolation
- API key protection
- Rate limiting per user

### Data Privacy

- User documents are isolated by user_id
- Secure file upload validation
- Automatic cleanup of failed processing

## Troubleshooting

### Common Issues

1. **Document processing fails**
   - Check file format support
   - Verify OpenAI API key
   - Check file size limits

2. **Search returns no results**
   - Verify document processing completed
   - Adjust similarity threshold
   - Check user permissions

3. **Performance issues**
   - Monitor Redis cache hit rates
   - Check database index usage
   - Optimize chunk sizes

### Logs and Debugging

Enable debug logging:
```env
LOG_LEVEL=debug
```

Check processing queue status:
```typescript
const jobStatus = await documentService.getJobStatus(jobId);
```

## Testing

Run the test suite:
```bash
npm test -- rag.test.ts
```

The tests cover:
- Document processing pipeline
- Vector search functionality
- RAG-enhanced text generation
- Error handling scenarios

## Future Enhancements

- Support for additional file formats
- Advanced chunking strategies
- Multi-modal document processing
- Real-time collaborative features
- Advanced analytics dashboard
