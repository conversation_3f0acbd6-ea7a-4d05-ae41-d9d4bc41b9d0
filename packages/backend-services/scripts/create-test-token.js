/**
 * Create Test Token Script
 * 
 * Creates a valid JWT token for testing API endpoints
 */

// Load environment variables FIRST
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

async function createTestToken() {
  console.log('🔑 Creating test token for API testing...\n');

  try {
    // Initialize Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Test user credentials
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';

    console.log(`📧 Using test user: ${testEmail}`);

    // Try to sign in with the test user
    const { data, error } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });

    if (error) {
      console.log('❌ Sign in failed, trying to create user...');
      
      // Try to create the user if sign in fails
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
        options: {
          emailRedirectTo: undefined // Skip email confirmation for testing
        }
      });

      if (signUpError) {
        console.error('❌ Failed to create test user:', signUpError.message);
        return null;
      }

      console.log('✅ Test user created successfully');
      
      // Try to sign in again
      const { data: retryData, error: retryError } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword
      });

      if (retryError) {
        console.error('❌ Failed to sign in after creation:', retryError.message);
        return null;
      }

      console.log('✅ Test user signed in successfully');
      return retryData.session?.access_token;
    }

    console.log('✅ Test user signed in successfully');
    console.log(`👤 User ID: ${data.user?.id}`);
    console.log(`🎫 Token: ${data.session?.access_token?.substring(0, 50)}...`);
    
    return data.session?.access_token;

  } catch (error) {
    console.error('❌ Error creating test token:', error.message);
    return null;
  }
}

// Run the script
if (require.main === module) {
  createTestToken()
    .then(token => {
      if (token) {
        console.log('\n🎉 Test token created successfully!');
        console.log('\n📋 Use this token for API testing:');
        console.log(`Bearer ${token}`);
        console.log('\n💡 Example usage:');
        console.log(`curl -H "Authorization: Bearer ${token}" http://localhost:4000/api/v1/assist/health`);
      } else {
        console.log('\n❌ Failed to create test token');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Script error:', error);
      process.exit(1);
    });
}

module.exports = { createTestToken };
