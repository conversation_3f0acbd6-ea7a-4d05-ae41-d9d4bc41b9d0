#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

async function verifyMigration() {
  console.log('🔍 Verifying RAG database migration...');
  
  // Initialize Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // Test basic connection
    console.log('📡 Testing Supabase connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('documents')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      if (connectionError.message.includes('relation "documents" does not exist')) {
        console.log('⚠️  Documents table does not exist - creating tables...');
        await createTablesDirectly(supabase);
      } else {
        console.error('❌ Connection error:', connectionError.message);
        return false;
      }
    } else {
      console.log('✅ Documents table exists');
    }
    
    // Test document_chunks table
    const { data: chunksTest, error: chunksError } = await supabase
      .from('document_chunks')
      .select('count')
      .limit(1);
    
    if (chunksError) {
      console.log('⚠️  Document chunks table issue:', chunksError.message);
    } else {
      console.log('✅ Document chunks table exists');
    }
    
    // Test search_analytics table
    const { data: analyticsTest, error: analyticsError } = await supabase
      .from('search_analytics')
      .select('count')
      .limit(1);
    
    if (analyticsError) {
      console.log('⚠️  Search analytics table issue:', analyticsError.message);
    } else {
      console.log('✅ Search analytics table exists');
    }
    
    console.log('🎉 Migration verification completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    return false;
  }
}

async function createTablesDirectly(supabase) {
  console.log('🔨 Creating tables directly...');
  
  try {
    // Create documents table using Supabase admin
    console.log('📝 Creating documents table...');
    
    // Note: For production, these would be created via Supabase dashboard or SQL editor
    // This is a verification that the connection works
    
    console.log('ℹ️  Tables should be created via Supabase dashboard SQL editor');
    console.log('ℹ️  Please run the migration SQL manually in Supabase dashboard');
    
  } catch (error) {
    console.error('❌ Direct table creation failed:', error.message);
  }
}

// Run the verification
verifyMigration().then(success => {
  if (success) {
    console.log('✨ RAG database is ready!');
    process.exit(0);
  } else {
    console.log('⚠️  Manual migration may be required');
    process.exit(1);
  }
});
