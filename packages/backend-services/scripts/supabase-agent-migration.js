#!/usr/bin/env node

/**
 * Supabase Agent System Migration Script
 * 
 * This script applies the agent system migration using Supabase CLI
 * and validates the setup for local Supabase development environment.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

async function runSupabaseMigration() {
  console.log('🚀 Supabase Agent System Migration\n');
  console.log('This script will apply the agent system migration to your local Supabase instance.\n');

  try {
    // Step 1: Check if Supabase CLI is available
    console.log('📋 Step 1: Checking Supabase CLI...');
    try {
      const supabaseVersion = execSync('supabase --version', { encoding: 'utf8' });
      console.log(`✅ Supabase CLI found: ${supabaseVersion.trim()}`);
    } catch (error) {
      console.error('❌ Supabase CLI not found. Please install it first:');
      console.error('   npm install -g supabase');
      console.error('   or visit: https://supabase.com/docs/guides/cli');
      process.exit(1);
    }

    // Step 2: Check if we're in the right directory
    console.log('\n📋 Step 2: Checking project structure...');
    const projectRoot = path.resolve(__dirname, '../../../');
    const supabaseConfigPath = path.join(projectRoot, 'supabase', 'config.toml');
    
    if (!fs.existsSync(supabaseConfigPath)) {
      console.error('❌ Supabase config not found. Please run this from the project root.');
      console.error(`   Expected: ${supabaseConfigPath}`);
      process.exit(1);
    }
    console.log('✅ Supabase project structure found');

    // Step 3: Check Supabase status
    console.log('\n📋 Step 3: Checking Supabase status...');
    try {
      const statusOutput = execSync('supabase status', { 
        encoding: 'utf8', 
        cwd: projectRoot 
      });
      console.log('✅ Supabase is running:');
      console.log(statusOutput);
    } catch (error) {
      console.log('⚠️  Supabase not running. Starting Supabase...');
      try {
        execSync('supabase start', { 
          encoding: 'utf8', 
          cwd: projectRoot,
          stdio: 'inherit'
        });
        console.log('✅ Supabase started successfully');
      } catch (startError) {
        console.error('❌ Failed to start Supabase:', startError.message);
        process.exit(1);
      }
    }

    // Step 4: Check if agent migration exists
    console.log('\n📋 Step 4: Checking agent migration file...');
    const migrationPath = path.join(projectRoot, 'supabase', 'migrations', '20250630_create_agent_schema.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Agent migration file not found:', migrationPath);
      console.error('   Please ensure the migration file exists in supabase/migrations/');
      process.exit(1);
    }
    console.log('✅ Agent migration file found');

    // Step 5: Apply migrations
    console.log('\n📋 Step 5: Applying database migrations...');
    try {
      const migrateOutput = execSync('supabase db reset', { 
        encoding: 'utf8', 
        cwd: projectRoot,
        stdio: 'inherit'
      });
      console.log('✅ Database migrations applied successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      console.error('   Trying alternative approach...');
      
      // Alternative: Apply specific migration
      try {
        const pushOutput = execSync('supabase db push', { 
          encoding: 'utf8', 
          cwd: projectRoot,
          stdio: 'inherit'
        });
        console.log('✅ Database schema pushed successfully');
      } catch (pushError) {
        console.error('❌ Alternative migration also failed:', pushError.message);
        process.exit(1);
      }
    }

    // Step 6: Validate migration
    console.log('\n📋 Step 6: Validating agent system setup...');
    await validateAgentSetup(projectRoot);

    // Step 7: Test vector functionality
    console.log('\n📋 Step 7: Testing vector functionality...');
    await testVectorFunctionality(projectRoot);

    console.log('\n🎉 Agent System Migration Completed Successfully!');
    console.log('\nNext steps:');
    console.log('1. Update your .env file with the correct Supabase credentials');
    console.log('2. Run the agent system tests: npm test');
    console.log('3. Start your backend service: npm run dev');
    console.log('\nYour AI agent system is ready to use! 🚀');

  } catch (error) {
    console.error('\n💥 Migration failed with error:');
    console.error(error.message);
    console.error('\nPlease check the error above and try again.');
    process.exit(1);
  }
}

/**
 * Validate that the agent system tables and functions are properly created
 */
async function validateAgentSetup(projectRoot) {
  try {
    // Get Supabase connection details
    const statusOutput = execSync('supabase status --output json', { 
      encoding: 'utf8', 
      cwd: projectRoot 
    });
    const status = JSON.parse(statusOutput);
    
    const supabaseUrl = status.API_URL || `http://localhost:54321`;
    const supabaseKey = status.ANON_KEY || status.anon_key;

    console.log(`   Connecting to: ${supabaseUrl}`);

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Check required tables
    const requiredTables = ['agent_states', 'agent_memories', 'tool_executions', 'planning_sessions'];
    
    for (const tableName of requiredTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('count')
          .limit(1);
        
        if (error) {
          console.error(`   ❌ Table ${tableName}: ${error.message}`);
        } else {
          console.log(`   ✅ Table ${tableName}: exists and accessible`);
        }
      } catch (error) {
        console.error(`   ❌ Table ${tableName}: ${error.message}`);
      }
    }

    // Check stored procedures
    console.log('\n   Checking stored procedures...');
    try {
      const { data, error } = await supabase.rpc('search_agent_memories', {
        query_embedding: new Array(1536).fill(0),
        conversation_id_filter: 'test',
        similarity_threshold: 0.7,
        match_count: 1
      });
      
      if (error && !error.message.includes('no rows')) {
        console.error(`   ❌ Stored procedures: ${error.message}`);
      } else {
        console.log('   ✅ Stored procedures: working correctly');
      }
    } catch (error) {
      console.error(`   ❌ Stored procedures: ${error.message}`);
    }

    console.log('✅ Agent system validation completed');

  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    throw error;
  }
}

/**
 * Test vector functionality with pgvector
 */
async function testVectorFunctionality(projectRoot) {
  try {
    // Get Supabase connection details
    const statusOutput = execSync('supabase status --output json', { 
      encoding: 'utf8', 
      cwd: projectRoot 
    });
    const status = JSON.parse(statusOutput);
    
    const supabaseUrl = status.API_URL || `http://localhost:54321`;
    const supabaseKey = status.ANON_KEY || status.anon_key;

    // Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Test vector extension
    console.log('   Testing pgvector extension...');
    try {
      const { data, error } = await supabase.rpc('exec', {
        sql: "SELECT extname FROM pg_extension WHERE extname = 'vector';"
      });
      
      if (error) {
        // Try alternative check
        const { data: vectorData, error: vectorError } = await supabase
          .from('agent_memories')
          .select('embedding')
          .limit(1);
        
        if (vectorError && !vectorError.message.includes('no rows')) {
          console.error(`   ❌ pgvector test failed: ${vectorError.message}`);
        } else {
          console.log('   ✅ pgvector extension: working correctly');
        }
      } else {
        console.log('   ✅ pgvector extension: installed and active');
      }
    } catch (error) {
      console.log('   ⚠️  pgvector test skipped (limited permissions)');
    }

    // Test vector operations
    console.log('   Testing vector operations...');
    try {
      // Insert a test memory with vector
      const testEmbedding = new Array(1536).fill(0.1);
      const { data, error } = await supabase
        .from('agent_memories')
        .insert({
          id: 'test-memory-vector',
          conversation_id: 'test-vector-conv',
          content: 'Test memory for vector functionality',
          embedding: testEmbedding,
          memory_type: 'conversation',
          importance: 5
        });

      if (error) {
        console.error(`   ❌ Vector insert test: ${error.message}`);
      } else {
        console.log('   ✅ Vector insert: working correctly');
        
        // Test vector similarity search
        const { data: searchData, error: searchError } = await supabase.rpc('search_agent_memories', {
          query_embedding: testEmbedding,
          conversation_id_filter: 'test-vector-conv',
          similarity_threshold: 0.5,
          match_count: 1
        });

        if (searchError) {
          console.error(`   ❌ Vector search test: ${searchError.message}`);
        } else {
          console.log('   ✅ Vector similarity search: working correctly');
        }

        // Cleanup test data
        await supabase
          .from('agent_memories')
          .delete()
          .eq('id', 'test-memory-vector');
      }
    } catch (error) {
      console.error(`   ❌ Vector operations test: ${error.message}`);
    }

    console.log('✅ Vector functionality testing completed');

  } catch (error) {
    console.error('❌ Vector testing failed:', error.message);
    throw error;
  }
}

/**
 * Get Supabase connection info for environment setup
 */
function getSupabaseConnectionInfo(projectRoot) {
  try {
    const statusOutput = execSync('supabase status --output json', { 
      encoding: 'utf8', 
      cwd: projectRoot 
    });
    const status = JSON.parse(statusOutput);
    
    console.log('\n📋 Supabase Connection Information:');
    console.log('=====================================');
    console.log(`API URL: http://localhost:${status.api.port}`);
    console.log(`Anon Key: ${status.anon_key}`);
    console.log(`Service Role Key: ${status.service_role_key}`);
    console.log(`Database URL: postgresql://postgres:${status.db.password}@localhost:${status.db.port}/postgres`);
    console.log('\nAdd these to your .env file:');
    console.log(`SUPABASE_URL=http://localhost:${status.api.port}`);
    console.log(`SUPABASE_ANON_KEY=${status.anon_key}`);
    console.log(`SUPABASE_SERVICE_ROLE_KEY=${status.service_role_key}`);
    console.log('=====================================\n');

  } catch (error) {
    console.error('❌ Could not get Supabase connection info:', error.message);
  }
}

// Run the migration if called directly
if (require.main === module) {
  runSupabaseMigration()
    .then(() => {
      console.log('\n✨ Migration script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { runSupabaseMigration, validateAgentSetup, testVectorFunctionality };
