#!/usr/bin/env node

// Load environment variables
require('dotenv').config();

async function validateEnvironment() {
  console.log('🔍 Validating RAG system environment...\n');
  
  // Check environment variables
  console.log('📋 Environment Variables:');
  const envVars = [
    'OPENAI_API_KEY',
    'SUPABASE_URL', 
    'SUPABASE_SERVICE_ROLE_KEY',
    'REDIS_HOST',
    'REDIS_PORT',
    'REDIS_PASSWORD'
  ];
  
  envVars.forEach(envVar => {
    const value = process.env[envVar];
    if (value) {
      console.log(`✅ ${envVar}: ${value.substring(0, 20)}...`);
    } else {
      console.log(`❌ ${envVar}: Not set`);
    }
  });
  
  // Check packages
  console.log('\n📦 Required Packages:');
  const packages = [
    'openai', 'pdf-parse', 'mammoth', 'multer', 
    'bull', 'redis', 'ioredis', '@supabase/supabase-js'
  ];
  
  packages.forEach(pkg => {
    try {
      require(pkg);
      console.log(`✅ ${pkg}: Available`);
    } catch (error) {
      console.log(`❌ ${pkg}: Missing`);
    }
  });
  
  console.log('\n✨ Basic validation completed!');
  console.log('ℹ️  For full validation, test individual services manually');
}

validateEnvironment();
