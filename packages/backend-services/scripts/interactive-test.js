#!/usr/bin/env node

/**
 * Interactive Real-World Testing Script
 * 
 * This script provides an interactive way to test the agent system
 * with real-world sales scenarios.
 */

require('dotenv').config();
const axios = require('axios');
const readline = require('readline');

const BASE_URL = 'http://localhost:4000/api/v1';

// Test token - you can replace this with a real token
const TEST_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Wk4YJxE0MjI30xkWQACSD2r947VRUqAnX-Xx-x215i8';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Predefined test scenarios
const scenarios = {
  '1': {
    name: 'Price Objection',
    context: {
      prospectName: '<PERSON>',
      companyName: 'TechCorp Inc',
      dealStage: 'negotiation',
      currentTranscriptSegment: 'I like your solution, but $50,000 seems really expensive for our budget.'
    },
    assistanceType: 'price_objection',
    query: 'They think our price is too high'
  },
  '2': {
    name: 'Discovery Questions',
    context: {
      prospectName: '<PERSON>',
      companyName: 'StartupXYZ',
      dealStage: 'discovery',
      currentTranscriptSegment: 'Hi, thanks for taking the time to speak with me today. I understand you might be interested in our solution.'
    },
    assistanceType: 'discovery',
    query: 'What questions should I ask to understand their needs?'
  },
  '3': {
    name: 'Competitive Positioning',
    context: {
      prospectName: 'Lisa Rodriguez',
      companyName: 'Enterprise Corp',
      dealStage: 'evaluation',
      currentTranscriptSegment: 'We are also looking at Salesforce and HubSpot. How does your solution compare?'
    },
    assistanceType: 'competitive_positioning',
    query: 'How do we position against Salesforce and HubSpot?'
  },
  '4': {
    name: 'Closing Strategy',
    context: {
      prospectName: 'David Wilson',
      companyName: 'Growth Industries',
      dealStage: 'closing',
      currentTranscriptSegment: 'This looks good. I think we are ready to move forward, but I need to discuss with my team first.'
    },
    assistanceType: 'closing',
    query: 'How should I close this deal?'
  },
  '5': {
    name: 'General Objection',
    context: {
      prospectName: 'Jennifer Adams',
      companyName: 'Cautious Corp',
      dealStage: 'objection',
      currentTranscriptSegment: 'I am not sure if this is the right time for us to make this kind of investment.'
    },
    assistanceType: 'objection',
    query: 'They are hesitant about timing'
  }
};

function displayMenu() {
  console.log('\n🤖 Closezly Agent System - Interactive Testing');
  console.log('='.repeat(50));
  console.log('Choose a test scenario:');
  console.log('');
  
  Object.entries(scenarios).forEach(([key, scenario]) => {
    console.log(`${key}. ${scenario.name}`);
  });
  
  console.log('');
  console.log('6. Custom scenario');
  console.log('7. Test agent memory (multi-turn conversation)');
  console.log('8. Check system health');
  console.log('9. View available tools');
  console.log('0. Exit');
  console.log('');
}

async function testScenario(scenario, useAgent = false, conversationId = null) {
  try {
    console.log(`\n🎯 Testing: ${scenario.name}`);
    console.log(`📞 Prospect: ${scenario.context.prospectName} from ${scenario.context.companyName}`);
    console.log(`💬 They said: "${scenario.context.currentTranscriptSegment}"`);
    console.log(`🤔 Your question: "${scenario.query}"`);
    console.log('\n⏳ Getting AI assistance...\n');

    const requestData = {
      context: scenario.context,
      assistanceType: scenario.assistanceType,
      query: scenario.query,
      useAgent,
      enableToolCalling: useAgent
    };

    if (conversationId) {
      requestData.conversationId = conversationId;
    }

    const response = await axios.post(`${BASE_URL}/assist/realtime`, requestData, {
      headers: {
        'Authorization': TEST_TOKEN,
        'Content-Type': 'application/json'
      }
    });

    console.log('🎉 AI Suggestion:');
    console.log('─'.repeat(50));
    console.log(response.data.rawResponse);
    console.log('─'.repeat(50));
    
    if (response.data.suggestions && response.data.suggestions.length > 0) {
      console.log('\n💡 Additional Suggestions:');
      response.data.suggestions.forEach((suggestion, index) => {
        console.log(`${index + 1}. ${suggestion}`);
      });
    }

    if (response.data.usage) {
      console.log(`\n📊 Usage: ${response.data.usage.totalTokens} tokens`);
    }

    if (response.data.performance) {
      console.log(`⚡ Response time: ${response.data.performance.totalDuration}ms`);
    }

    return response.data;

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.error || error.message);
    return null;
  }
}

async function customScenario() {
  console.log('\n📝 Create your custom scenario:');
  
  const prospectName = await askQuestion('Prospect name: ');
  const companyName = await askQuestion('Company name: ');
  const dealStage = await askQuestion('Deal stage (discovery/negotiation/closing/etc): ');
  const transcript = await askQuestion('What did the prospect say? ');
  const assistanceType = await askQuestion('Assistance type (objection/discovery/product_info/etc): ');
  const query = await askQuestion('Your specific question: ');

  const customScenario = {
    name: 'Custom Scenario',
    context: {
      prospectName,
      companyName,
      dealStage,
      currentTranscriptSegment: transcript
    },
    assistanceType,
    query
  };

  await testScenario(customScenario);
}

async function testAgentMemory() {
  const conversationId = `memory-test-${Date.now()}`;
  console.log(`\n🧠 Testing Agent Memory (Conversation ID: ${conversationId})`);
  
  // First interaction
  console.log('\n--- Turn 1: Initial Discovery ---');
  await testScenario({
    name: 'Memory Test - Turn 1',
    context: {
      prospectName: 'Memory Test User',
      companyName: 'Test Corp',
      dealStage: 'discovery',
      currentTranscriptSegment: 'We are a 200-person company looking for a CRM solution. We currently use spreadsheets.'
    },
    assistanceType: 'discovery',
    query: 'What should I ask next?'
  }, true, conversationId);

  await askQuestion('\nPress Enter to continue to Turn 2...');

  // Second interaction
  console.log('\n--- Turn 2: Follow-up Questions ---');
  await testScenario({
    name: 'Memory Test - Turn 2',
    context: {
      prospectName: 'Memory Test User',
      companyName: 'Test Corp',
      dealStage: 'discovery',
      currentTranscriptSegment: 'We have about 50 sales reps and are struggling with lead tracking and follow-ups.'
    },
    assistanceType: 'product_info',
    query: 'How can our solution help with their lead tracking issues?'
  }, true, conversationId);

  // Check agent state
  console.log('\n--- Checking Agent Memory ---');
  try {
    const stateResponse = await axios.get(`${BASE_URL}/assist/agent/${conversationId}/state`, {
      headers: { 'Authorization': TEST_TOKEN }
    });
    
    console.log('🧠 Agent State:');
    console.log(JSON.stringify(stateResponse.data, null, 2));
  } catch (error) {
    console.error('❌ Error checking agent state:', error.response?.data?.error || error.message);
  }
}

async function checkSystemHealth() {
  console.log('\n🏥 Checking System Health...');
  
  try {
    const healthResponse = await axios.get(`${BASE_URL}/assist/health`, {
      headers: { 'Authorization': TEST_TOKEN }
    });
    
    console.log('✅ System Status:');
    console.log(JSON.stringify(healthResponse.data, null, 2));
  } catch (error) {
    console.error('❌ Health check failed:', error.response?.data?.error || error.message);
  }
}

async function viewAvailableTools() {
  console.log('\n🔧 Available Tools...');
  
  try {
    const toolsResponse = await axios.get(`${BASE_URL}/assist/agent/tools`, {
      headers: { 'Authorization': TEST_TOKEN }
    });
    
    console.log('🛠️ Available Tools:');
    toolsResponse.data.tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });
  } catch (error) {
    console.error('❌ Error fetching tools:', error.response?.data?.error || error.message);
  }
}

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log('🚀 Starting Interactive Agent System Testing...');
  
  // Check if server is running
  try {
    await axios.get('http://localhost:4000/health');
    console.log('✅ Server is running on http://localhost:4000');
  } catch (error) {
    console.error('❌ Server is not running. Please start it with: npm run dev');
    process.exit(1);
  }

  while (true) {
    displayMenu();
    const choice = await askQuestion('Enter your choice (0-9): ');

    switch (choice) {
      case '1':
      case '2':
      case '3':
      case '4':
      case '5':
        await testScenario(scenarios[choice]);
        break;
      case '6':
        await customScenario();
        break;
      case '7':
        await testAgentMemory();
        break;
      case '8':
        await checkSystemHealth();
        break;
      case '9':
        await viewAvailableTools();
        break;
      case '0':
        console.log('\n👋 Thanks for testing! Goodbye!');
        rl.close();
        return;
      default:
        console.log('❌ Invalid choice. Please try again.');
    }

    await askQuestion('\nPress Enter to continue...');
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Goodbye!');
  rl.close();
  process.exit(0);
});

if (require.main === module) {
  main().catch(console.error);
}
