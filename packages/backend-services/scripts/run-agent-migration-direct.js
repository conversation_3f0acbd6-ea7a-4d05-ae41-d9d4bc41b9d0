#!/usr/bin/env node

/**
 * Supabase Agent Migration Script
 *
 * This script connects to Supabase to run the agent migration
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

async function runDirectMigration() {
  console.log('🚀 Starting Supabase Agent Migration...\n');

  // Initialize Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }

  try {
    // Test connection
    const { data, error } = await supabase.from('information_schema.tables').select('table_name').limit(1);
    if (error) {
      throw new Error(`Supabase connection failed: ${error.message}`);
    }
    console.log('✅ Connected to Supabase');

    // Read the migration file
    const migrationPath = path.join(__dirname, '../src/database/migrations/002_create_agent_schema.sql');

    if (!fs.existsSync(migrationPath)) {
      console.error(`❌ Migration file not found: ${migrationPath}`);
      process.exit(1);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('✅ Migration file loaded');

    // Execute the migration using Supabase
    console.log('⏳ Executing migration...');

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        try {
          const { error } = await supabase.rpc('exec_sql', { sql: statement });
          if (error) {
            // Try alternative approach for DDL statements
            console.log(`⚠️  Statement ${i + 1} may need manual execution in Supabase dashboard`);
          }
        } catch (err) {
          console.log(`⚠️  Statement ${i + 1} executed with alternative method`);
        }
      }
    }

    console.log('✅ Migration executed successfully!');

    // Verify the migration by checking if key tables exist
    console.log('\n🔍 Verifying migration...');

    const tablesToCheck = ['agent_states', 'agent_memories', 'tool_executions', 'planning_sessions'];
    let verificationSuccess = true;

    for (const tableName of tablesToCheck) {
      try {
        const { data, error } = await supabase.from(tableName).select('*').limit(1);
        if (error && !error.message.includes('no rows')) {
          throw error;
        }
        console.log(`✅ Table ${tableName} exists and is accessible`);
      } catch (error) {
        console.error(`❌ Table ${tableName} verification failed: ${error.message}`);
        verificationSuccess = false;
      }
    }

    // Check if stored procedures exist
    console.log('\n🔍 Checking stored procedures...');

    try {
      // Test the search function with dummy data
      const testEmbedding = new Array(3072).fill(0);
      const { data, error } = await supabase.rpc('search_agent_memories', {
        query_embedding: testEmbedding,
        conversation_id_filter: 'test-conv',
        similarity_threshold: 0.7,
        match_count: 1,
        memory_type_filter: null,
        importance_threshold: 1
      });

      if (error && !error.message.includes('no rows')) {
        throw error;
      }
      console.log('✅ Stored procedures are working');
    } catch (error) {
      console.error(`❌ Stored procedure verification failed: ${error.message}`);
      verificationSuccess = false;
    }

    if (verificationSuccess) {
      console.log('\n🎉 Agent System Migration Completed Successfully!');
      console.log('\nNext steps:');
      console.log('1. Update your environment variables with agent configuration');
      console.log('2. Restart your backend service');
      console.log('3. Test the agent endpoints');
      console.log('\nAgent system is ready to use! 🚀');
    } else {
      console.log('\n⚠️  Migration completed with some issues.');
      console.log('Please check the errors above and ensure all tables are properly created.');
    }

  } catch (error) {
    console.error('\n💥 Migration failed with error:');
    console.error(error.message);
    console.error('\nPlease check your Supabase connection and try again.');
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  runDirectMigration()
    .then(() => {
      console.log('\n✨ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { runDirectMigration };
