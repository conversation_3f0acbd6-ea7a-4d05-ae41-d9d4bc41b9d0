#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const OpenAI = require('openai');
const Redis = require('ioredis');
const Bull = require('bull');

// Load environment variables
require('dotenv').config();

async function validateDependencies() {
  console.log('🔍 Validating RAG system dependencies...\n');
  
  let allValid = true;
  const results = {};

  // 1. Validate Environment Variables
  console.log('📋 Checking environment variables...');
  const requiredEnvVars = [
    'OPENAI_API_KEY',
    'SUPABASE_URL', 
    'SUPABASE_SERVICE_ROLE_KEY',
    'REDIS_HOST',
    'REDIS_PORT',
    'REDIS_PASSWORD'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      console.log(`✅ ${envVar}: Configured`);
      results[envVar] = true;
    } else {
      console.log(`❌ ${envVar}: Missing`);
      results[envVar] = false;
      allValid = false;
    }
  }
  
  // 2. Validate OpenAI API
  console.log('\n🤖 Testing OpenAI API connection...');
  try {
    const openai = new OpenAI({ 
      apiKey: process.env.OPENAI_API_KEY 
    });
    
    // Test with a simple embedding request
    const response = await openai.embeddings.create({
      model: 'text-embedding-3-large',
      input: 'test',
      dimensions: 3072
    });
    
    if (response.data && response.data[0] && response.data[0].embedding) {
      console.log('✅ OpenAI API: Connected and working');
      console.log(`✅ Embedding dimensions: ${response.data[0].embedding.length}`);
      results.openai = true;
    } else {
      console.log('❌ OpenAI API: Invalid response');
      results.openai = false;
      allValid = false;
    }
  } catch (error) {
    console.log(`❌ OpenAI API: ${error.message}`);
    results.openai = false;
    allValid = false;
  }
  
  // 3. Validate Redis Connection
  console.log('\n🔴 Testing Redis connection...');
  try {
    const redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      lazyConnect: true
    });
    
    await redis.connect();
    await redis.ping();
    console.log('✅ Redis: Connected and responding');
    
    // Test set/get
    await redis.set('test_key', 'test_value');
    const value = await redis.get('test_key');
    if (value === 'test_value') {
      console.log('✅ Redis: Read/write operations working');
      results.redis = true;
    } else {
      console.log('❌ Redis: Read/write operations failed');
      results.redis = false;
      allValid = false;
    }
    
    await redis.del('test_key');
    await redis.disconnect();
    
  } catch (error) {
    console.log(`❌ Redis: ${error.message}`);
    results.redis = false;
    allValid = false;
  }
  
  // 4. Validate Bull Queue
  console.log('\n📋 Testing Bull queue setup...');
  try {
    const testQueue = new Bull('test queue', {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
      }
    });
    
    // Test adding a job
    const job = await testQueue.add('test job', { test: 'data' });
    if (job.id) {
      console.log('✅ Bull Queue: Job creation working');
      results.bull = true;
    } else {
      console.log('❌ Bull Queue: Job creation failed');
      results.bull = false;
      allValid = false;
    }
    
    await testQueue.close();
    
  } catch (error) {
    console.log(`❌ Bull Queue: ${error.message}`);
    results.bull = false;
    allValid = false;
  }
  
  // 5. Validate Supabase Connection
  console.log('\n🗄️  Testing Supabase connection...');
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    // Test basic connection - try to query a system table
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .limit(1);
    
    if (error) {
      console.log(`❌ Supabase: ${error.message}`);
      results.supabase = false;
      allValid = false;
    } else {
      console.log('✅ Supabase: Connected successfully');
      results.supabase = true;
    }
    
  } catch (error) {
    console.log(`❌ Supabase: ${error.message}`);
    results.supabase = false;
    allValid = false;
  }
  
  // 6. Validate Required Packages
  console.log('\n📦 Checking required packages...');
  const requiredPackages = [
    'openai', 'pdf-parse', 'mammoth', 'multer', 
    'bull', 'redis', 'ioredis', '@supabase/supabase-js'
  ];
  
  for (const pkg of requiredPackages) {
    try {
      require(pkg);
      console.log(`✅ ${pkg}: Available`);
      results[`package_${pkg}`] = true;
    } catch (error) {
      console.log(`❌ ${pkg}: Missing or broken`);
      results[`package_${pkg}`] = false;
      allValid = false;
    }
  }
  
  // Summary
  console.log('\n📊 Validation Summary:');
  console.log('='.repeat(50));
  
  if (allValid) {
    console.log('🎉 All dependencies are properly configured!');
    console.log('✨ RAG system is ready for testing');
  } else {
    console.log('⚠️  Some dependencies need attention:');
    Object.entries(results).forEach(([key, value]) => {
      if (!value) {
        console.log(`   ❌ ${key}`);
      }
    });
  }
  
  return allValid;
}

// Run validation
validateDependencies().then(success => {
  process.exit(success ? 0 : 1);
});
