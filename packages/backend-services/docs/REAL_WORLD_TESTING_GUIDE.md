# Real-World Testing Guide

This guide shows you how to test the AI Agent System in real-world scenarios using actual sales conversations and use cases.

## 🚀 Quick Start

### 1. Get Your Authentication Token

First, create a test user and get an authentication token:

```bash
cd packages/backend-services
node scripts/create-test-token.js
```

Copy the Bearer token from the output. You'll use this for all API calls.

### 2. Verify System Health

```bash
# Check basic health
curl http://localhost:4000/health

# Check agent system health (requires auth)
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:4000/api/v1/assist/health
```

## 🎯 Real-World Test Scenarios

### Scenario 1: Handling Price Objections

**Context**: Prospect says your solution is too expensive.

```bash
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "context": {
      "prospectName": "<PERSON>",
      "companyName": "TechCorp Inc",
      "dealStage": "negotiation",
      "currentTranscriptSegment": "I like your solution, but $50,000 seems really expensive for our budget."
    },
    "assistanceType": "price_objection",
    "query": "They think our price is too high"
  }'
```

### Scenario 2: Discovery Questions for New Prospect

**Context**: First call with a new prospect.

```bash
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "context": {
      "prospectName": "Mike Chen",
      "companyName": "StartupXYZ",
      "dealStage": "discovery",
      "currentTranscriptSegment": "Hi, thanks for taking the time to speak with me today. I understand you might be interested in our solution."
    },
    "assistanceType": "discovery",
    "query": "What questions should I ask to understand their needs?"
  }'
```

### Scenario 3: Competitive Positioning

**Context**: Prospect mentions a competitor.

```bash
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "context": {
      "prospectName": "Lisa Rodriguez",
      "companyName": "Enterprise Corp",
      "dealStage": "evaluation",
      "currentTranscriptSegment": "We are also looking at Salesforce and HubSpot. How does your solution compare?"
    },
    "assistanceType": "competitive_positioning",
    "query": "How do we position against Salesforce and HubSpot?"
  }'
```

## 🤖 Testing Agent-Enhanced Features

### Test Agent Memory and State

**Step 1**: Start a conversation with agent capabilities:

```bash
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "context": {
      "prospectName": "John Smith",
      "companyName": "BigCorp Ltd",
      "dealStage": "discovery",
      "currentTranscriptSegment": "We are a 500-person company looking for a CRM solution."
    },
    "assistanceType": "discovery",
    "useAgent": true,
    "conversationId": "test-conversation-123",
    "enableToolCalling": true
  }'
```

**Step 2**: Check agent state:

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/v1/assist/agent/test-conversation-123/state
```

**Step 3**: Continue the conversation:

```bash
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "context": {
      "prospectName": "John Smith",
      "companyName": "BigCorp Ltd",
      "dealStage": "discovery",
      "currentTranscriptSegment": "We currently use spreadsheets and are having issues with data consistency."
    },
    "assistanceType": "product_info",
    "useAgent": true,
    "conversationId": "test-conversation-123",
    "query": "How can our solution help with their data consistency issues?"
  }'
```

### Test Available Tools

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:4000/api/v1/assist/agent/tools
```

## 📊 Performance Testing

### Load Testing with Multiple Conversations

Create a simple load test script:

```bash
# Create multiple concurrent requests
for i in {1..5}; do
  curl -X POST http://localhost:4000/api/v1/assist/realtime \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer YOUR_TOKEN" \
    -d "{
      \"context\": {
        \"prospectName\": \"Test User $i\",
        \"companyName\": \"Test Corp $i\",
        \"dealStage\": \"discovery\",
        \"currentTranscriptSegment\": \"This is test conversation number $i\"
      },
      \"assistanceType\": \"general_assistance\",
      \"useAgent\": true,
      \"conversationId\": \"load-test-$i\"
    }" &
done
wait
```

## 🔍 Monitoring and Debugging

### Check System Health During Testing

```bash
# Monitor agent system health
watch -n 5 'curl -s -H "Authorization: Bearer YOUR_TOKEN" http://localhost:4000/api/v1/assist/health | jq'

# Check Redis connection
redis-cli ping

# Check Supabase connection
curl -s http://127.0.0.1:54321/health
```

### View Real-Time Logs

In your terminal where the server is running, you'll see real-time logs showing:
- Agent state updates
- Memory operations
- Tool executions
- Performance metrics

## 🎭 Role-Playing Test Scenarios

### Scenario A: Difficult Prospect
Test how the agent handles challenging situations:

```bash
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "context": {
      "prospectName": "Difficult Dan",
      "companyName": "Skeptical Systems",
      "dealStage": "objection",
      "currentTranscriptSegment": "I have tried solutions like this before and they never work. Why should I believe yours is any different?"
    },
    "assistanceType": "objection",
    "useAgent": true,
    "conversationId": "difficult-prospect-test"
  }'
```

### Scenario B: Technical Deep-Dive
Test product knowledge capabilities:

```bash
curl -X POST http://localhost:4000/api/v1/assist/realtime \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "context": {
      "prospectName": "Technical Tom",
      "companyName": "DevOps Dynamics",
      "dealStage": "technical_evaluation",
      "currentTranscriptSegment": "Can you explain the technical architecture? How does it integrate with our existing API infrastructure?"
    },
    "assistanceType": "product_info",
    "useAgent": true,
    "conversationId": "technical-deep-dive"
  }'
```

## 📈 Success Metrics to Track

While testing, monitor these metrics:

1. **Response Time**: Should be < 3 seconds for most requests
2. **Agent Memory**: Check if context is maintained across conversations
3. **Tool Usage**: Verify tools are being called appropriately
4. **Response Quality**: Evaluate if suggestions are relevant and helpful
5. **Error Rate**: Should be < 1% for normal operations

## 🚨 Common Issues and Solutions

### Issue: "Invalid or expired token"
**Solution**: Generate a new token with `node scripts/create-test-token.js`

### Issue: "Agent state not found"
**Solution**: Make sure you're using `useAgent: true` and providing a `conversationId`

### Issue: Slow responses
**Solution**: Check Redis and Supabase connections, monitor system resources

### Issue: Empty responses
**Solution**: Verify Gemini API key is valid and has sufficient quota

## 🎯 Next Steps

After testing these scenarios:

1. **Integrate with Frontend**: Connect these APIs to your desktop/web applications
2. **Add Custom Tools**: Extend the tool orchestrator with your specific CRM/tools
3. **Fine-tune Prompts**: Adjust prompts based on your specific use cases
4. **Scale Testing**: Test with larger volumes and longer conversations
5. **Monitor Production**: Set up proper monitoring and alerting

## 📞 Testing with Real Sales Calls

For the ultimate test, try using the system during actual sales calls:

1. Have the API ready during your next prospect call
2. Use the real-time assistance endpoints for live guidance
3. Test different assistance types based on call flow
4. Monitor agent memory to see context retention
5. Gather feedback on response quality and relevance

Remember: The agent system learns and improves with usage, so the more you test with real scenarios, the better it becomes!
