# Supabase AI Agent System Setup Guide

## Overview

This guide provides step-by-step instructions for setting up and testing the AI Agent System with your local Supabase development environment.

## Prerequisites

### 1. Supabase CLI Installation

```bash
# Install Supabase CLI globally
npm install -g supabase

# Verify installation
supabase --version
```

### 2. Docker Installation

Ensure Docker is installed and running on your system:
- **macOS**: [Docker Desktop for Mac](https://docs.docker.com/desktop/mac/install/)
- **Windows**: [Docker Desktop for Windows](https://docs.docker.com/desktop/windows/install/)
- **Linux**: [Docker Engine](https://docs.docker.com/engine/install/)

## Setup Instructions

### Step 1: Start Supabase Local Development

```bash
# Navigate to project root
cd /Users/<USER>/Desktop/closezly

# Start Supabase (this will download and start all required containers)
supabase start

# Check status
supabase status
```

**Expected Output:**
```
         API URL: http://localhost:54321
          DB URL: postgresql://postgres:postgres@localhost:54322/postgres
      Studio URL: http://localhost:54323
    Inbucket URL: http://localhost:54324
      JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
        anon key: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
service_role key: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### Step 2: Apply Agent System Migration

```bash
# Navigate to backend services
cd packages/backend-services

# Run the Supabase-specific migration script
node scripts/supabase-agent-migration.js
```

**What this script does:**
- ✅ Verifies Supabase CLI is available
- ✅ Checks project structure
- ✅ Ensures Supabase is running
- ✅ Applies database migrations
- ✅ Validates agent system setup
- ✅ Tests vector functionality
- ✅ Provides connection information

### Step 3: Configure Environment Variables

Create or update your `.env` file in `packages/backend-services/`:

```bash
# Copy example environment file
cp .env.example .env
```

Add the following variables (get values from `supabase status`):

```env
# Supabase Configuration
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your-anon-key-from-supabase-status
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-from-supabase-status

# Redis Configuration (for agent state caching)
REDIS_HOST=localhost
REDIS_PORT=6379

# LLM API Keys (for full functionality)
OPENAI_API_KEY=your-openai-api-key
GEMINI_API_KEY=your-gemini-api-key

# Agent Configuration
AGENT_MEMORY_TTL=3600
AGENT_MAX_TOOLS_PER_REQUEST=5
AGENT_PLANNING_ENABLED=true
TOOL_TIMEOUT_MS=30000
MEMORY_CONSOLIDATION_INTERVAL=300000
AGENT_TOOL_CALLING_ENABLED=true
```

### Step 4: Install Dependencies and Build

```bash
# Install dependencies
npm install

# Build the project
npm run build
```

### Step 5: Run Comprehensive Tests

```bash
# Test the agent system with Supabase
node scripts/test-supabase-agent-system.js
```

**Expected Test Results:**
```
🧪 Supabase Agent System Comprehensive Test

📋 Step 1: Verifying Supabase Status
   ✅ Supabase API: http://localhost:54321
   ✅ Database: localhost:54322
   ✅ Studio: http://localhost:54323

📋 Step 2: Setting up Environment Variables
   ✅ Environment variables configured for testing

📋 Step 3: Testing Database Connectivity
   ✅ Database connectivity: working
   ✅ Agent tables: accessible

📋 Step 4: Testing Agent State Service
   ✅ Agent state creation: working
   ✅ Agent state retrieval: working
   ✅ Agent state updates: working
   ✅ Agent state cleanup: working

📋 Step 5: Testing Memory Service
   ✅ Memory creation: working
   ✅ Memory retrieval: found X memories
   ✅ Memory context building: working
   ✅ Memory cleanup: working

📋 Step 6: Testing Planning Engine
   ✅ Planning session creation: working
   ✅ Planning cleanup: working

📋 Step 7: Testing Reasoning Engine
   ✅ Reasoning Engine structure: working
   ✅ Reasoning cleanup: working

📋 Step 8: Testing Proactive Suggestion Engine
   ✅ Suggestion generation: working
   ✅ Suggestion statistics: working
   ✅ Suggestions cleanup: working

📋 Step 9: Testing Advanced Tool Orchestrator
   ✅ Workflow templates: working
   ✅ Tool chain creation: working

📋 Step 10: Testing Enhanced LLM Service Integration
   ✅ Enhanced LLM service health check: working
   ✅ Workflow templates access: working

📋 Step 11: Testing Vector Search Functionality
   ✅ Vector insertion: working
   ✅ Vector similarity search: working
   ✅ Vector cleanup: working

📋 Step 12: Performance Testing
   ✅ Performance test completed

🎉 All Tests Passed Successfully!
```

## Database Schema Validation

### Verify Tables

Access Supabase Studio at `http://localhost:54323` and verify these tables exist:

1. **agent_states** - Stores agent conversation states
2. **agent_memories** - Stores agent memories with vector embeddings
3. **tool_executions** - Tracks tool usage and results
4. **planning_sessions** - Stores planning session data

### Verify Extensions

Check that required extensions are enabled:

```sql
-- In Supabase Studio SQL Editor
SELECT extname FROM pg_extension WHERE extname IN ('vector', 'uuid-ossp');
```

Expected result:
```
extname
-------
vector
uuid-ossp
```

### Test Vector Functionality

```sql
-- Test vector operations
SELECT 
  id, 
  content, 
  embedding <-> '[0.1,0.1,0.1,...]'::vector AS distance
FROM agent_memories 
WHERE conversation_id = 'test-conv'
ORDER BY embedding <-> '[0.1,0.1,0.1,...]'::vector
LIMIT 5;
```

## API Testing

### Start Development Server

```bash
# Start the backend service
npm run dev
```

### Test Agent Endpoints

#### Basic Agent Assistance

```bash
curl -X POST http://localhost:3000/api/v1/assist/agent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "context": {
      "prospectName": "John Doe",
      "companyName": "Acme Corp",
      "dealStage": "discovery"
    },
    "assistanceType": "general_assistance",
    "query": "Help me prepare for this call",
    "conversationId": "test-conv-123",
    "useAgent": true,
    "enableToolCalling": true
  }'
```

#### Advanced Agent with Planning

```bash
curl -X POST http://localhost:3000/api/v1/assist/agent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "context": {
      "prospectName": "Jane Smith",
      "companyName": "TechCorp",
      "dealStage": "discovery"
    },
    "assistanceType": "general_assistance",
    "query": "Create a comprehensive plan to qualify this lead",
    "conversationId": "test-planning-456",
    "enablePlanning": true,
    "enableReasoning": true,
    "reasoningPattern": "react",
    "enableProactiveSuggestions": true
  }'
```

#### Agent Management

```bash
# Get agent state
curl -X GET http://localhost:3000/api/v1/assist/agent/test-conv-123/state \
  -H "Authorization: Bearer your-token"

# Update agent goal
curl -X PUT http://localhost:3000/api/v1/assist/agent/test-conv-123/goal \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{"goal": "Close the deal with Acme Corp"}'

# Get available tools
curl -X GET http://localhost:3000/api/v1/assist/agent/tools \
  -H "Authorization: Bearer your-token"
```

## Troubleshooting

### Common Issues

#### 1. Supabase Not Starting

```bash
# Check Docker status
docker ps

# Restart Supabase
supabase stop
supabase start
```

#### 2. Migration Errors

```bash
# Reset database and reapply migrations
supabase db reset

# Or apply migrations manually
supabase db push
```

#### 3. Vector Extension Issues

```sql
-- Check if vector extension is installed
SELECT * FROM pg_extension WHERE extname = 'vector';

-- If not installed, install it
CREATE EXTENSION IF NOT EXISTS vector;
```

#### 4. Connection Issues

```bash
# Check Supabase status
supabase status

# Verify environment variables
echo $SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY
```

#### 5. Test Failures

```bash
# Run tests with verbose output
DEBUG=* node scripts/test-supabase-agent-system.js

# Check specific service
node -e "
const { AgentStateService } = require('./dist/services/agentStateService');
const service = new AgentStateService();
console.log('Service initialized successfully');
"
```

### Performance Optimization

#### Database Indexes

Ensure these indexes exist for optimal performance:

```sql
-- Vector similarity search index
CREATE INDEX IF NOT EXISTS agent_memories_embedding_idx 
ON agent_memories USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Conversation lookup index
CREATE INDEX IF NOT EXISTS agent_memories_conversation_idx 
ON agent_memories (conversation_id);

-- Agent state lookup index
CREATE INDEX IF NOT EXISTS agent_states_conversation_idx 
ON agent_states (conversation_id);

-- Tool execution lookup index
CREATE INDEX IF NOT EXISTS tool_executions_conversation_idx 
ON tool_executions (conversation_id);
```

#### Connection Pooling

For production, configure connection pooling in your Supabase project settings.

## Next Steps

1. **Development**: Your agent system is ready for development
2. **Testing**: Run the full test suite with `npm test`
3. **Integration**: Integrate with your frontend applications
4. **Monitoring**: Set up monitoring and logging
5. **Production**: Deploy to Supabase Cloud when ready

## Support

If you encounter issues:

1. Check the [Supabase Documentation](https://supabase.com/docs)
2. Review the test output for specific error messages
3. Verify all environment variables are set correctly
4. Ensure Docker and Supabase CLI are up to date

Your AI Agent System is now fully integrated with Supabase and ready for advanced AI-powered sales assistance! 🚀
