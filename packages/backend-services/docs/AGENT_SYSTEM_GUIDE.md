# AI Agent System Implementation Guide

## Overview

The AI Agent System transforms the existing reactive LLM orchestration service into a proactive, memory-enabled, tool-using AI agent while maintaining full backward compatibility. This implementation follows the specifications outlined in the agent implementation guide and research documentation.

## Implementation Status

### ✅ Completed Features
- **Agent State Service**: Redis caching + Supabase persistence for conversation state
- **Memory Service**: Short-term, working, and long-term memory with vector search
- **Tool Orchestrator**: 5 default tools (CRM, Knowledge, Email, Analysis, Scheduling)
- **Enhanced LLM Service**: Agent-aware wrapper with tool calling and memory integration
- **Service Container**: Singleton pattern for optimized resource management
- **API Integration**: Full authentication and health check endpoints
- **Test Coverage**: Comprehensive unit tests and integration tests

### ⚠️ Known Issues
- Vector search function signature compatibility (minor impact)
- Multiple service instantiation optimization (partially resolved)

### 🔄 In Progress
- Advanced planning and reasoning engines
- Proactive suggestion system enhancements
- Vector embedding dimension standardization

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                     Agent Orchestration Layer                   │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Memory    │  │  Planning   │  │ Tool Use    │  │ Reasoning│ │
│  │  Manager    │  │   Engine    │  │Orchestrator │  │  Engine │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    Enhanced LLM Service                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │           Existing LLM Orchestration Service               │ │
│  │        (Google Gemini + Prompt Templates)                  │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                      Tool Integration Layer                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ CRM Tools   │  │ RAG System  │  │Communication│  │Analysis │ │
│  │(SF, HubSpot)│  │ Integration │  │   Tools     │  │ Tools   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     Shared Infrastructure                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Redis     │  │  pgvector   │  │ Monitoring  │  │ Logging │ │
│  │  (State)    │  │ (Memory)    │  │(Prometheus) │  │(Winston)│ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Agent State Service (`agentStateService.ts`)

Manages persistent conversation state with Redis caching and Supabase persistence.

**Key Features:**
- Redis-first caching for active conversations
- Supabase persistence for long-term storage
- Automatic state serialization/deserialization
- Health monitoring and error recovery

**Usage:**
```typescript
const agentStateService = new AgentStateService();

// Create new agent state
const state = await agentStateService.createNewAgentState(
  conversationId, 
  userId, 
  context
);

// Retrieve existing state
const state = await agentStateService.getAgentState(conversationId);

// Update state
await agentStateService.updateAgentState(state);
```

### 2. Memory Service (`memoryService.ts`)

Manages multi-layered memory system with semantic search capabilities.

**Memory Types:**
- **Short-term Memory**: Recent conversation context (last 20 messages)
- **Working Memory**: Active task context and tool results (last 10 items)
- **Long-term Memory**: Important insights stored with vector embeddings

**Key Features:**
- Automatic memory consolidation based on importance scores
- Semantic memory search using OpenAI embeddings
- User preference tracking
- Memory importance scoring (1-10 scale)

**Usage:**
```typescript
const memoryService = new MemoryService();

// Add memory
await memoryService.addMemory(
  agentState, 
  'Customer budget is $50k annually', 
  'insight', 
  8 // importance score
);

// Retrieve relevant memories
const memories = await memoryService.retrieveRelevantMemories(
  'budget information', 
  conversationId
);

// Build memory context for prompts
const context = await memoryService.getMemoryContext(agentState, query);
```

### 3. Tool Orchestrator (`toolOrchestrator.ts`)

Plugin-based tool execution framework with validation and logging.

**Built-in Tools:**
- **search_knowledge**: RAG-powered knowledge base search
- **search_crm**: CRM system integration (placeholder)
- **send_email**: Email communication (placeholder)
- **analyze_conversation**: Conversation analysis
- **schedule_meeting**: Calendar integration (placeholder)

**Key Features:**
- Parameter validation with type checking
- Permission-based access control
- Execution timeout handling
- Comprehensive logging and analytics

**Usage:**
```typescript
const toolOrchestrator = new ToolOrchestrator();

// Execute tool
const result = await toolOrchestrator.executeTool(
  'search_knowledge',
  { query: 'pricing information', limit: 5 },
  { agentState, userId, conversationId }
);

// Register custom tool
toolOrchestrator.registerTool({
  name: 'custom_tool',
  description: 'Custom tool description',
  category: 'utility',
  parameters: [/* parameter definitions */],
  execute: async (params, context) => {
    // Tool implementation
    return { success: true, data: result };
  }
});
```

### 4. Enhanced LLM Orchestration Service (`enhancedLLMOrchestrationService.ts`)

Agent-aware wrapper around the existing LLM service.

**Key Features:**
- Agent context building with memory integration
- Tool calling with pattern matching `[TOOL:name(params)]`
- Graceful fallback to existing LLM service
- Automatic memory storage of interactions

**Usage:**
```typescript
const enhancedLLMService = new EnhancedLLMOrchestrationService();

// Generate with agent capabilities
const response = await enhancedLLMService.generateWithAgent({
  prompt: 'Help me prepare for the call with Acme Corp',
  conversationId: 'conv-123',
  userId: 'user-456',
  context: callContext,
  useAgent: true,
  enableToolCalling: true,
  useRAG: true
});
```

## Database Schema

### Tables Created

1. **agent_states**: Persistent conversation state
2. **agent_memories**: Vector-based semantic memory storage
3. **tool_executions**: Tool usage tracking and analytics
4. **planning_sessions**: Future planning capabilities (foundation)

### Key Stored Procedures

- `search_agent_memories()`: Semantic memory search
- `get_agent_state_with_context()`: State retrieval with recent memories
- `cleanup_old_agent_states()`: Maintenance function

## API Endpoints

### Enhanced Existing Endpoints

#### POST `/api/v1/assist/realtime`
Enhanced with optional agent capabilities:

```json
{
  "context": { /* CallContext */ },
  "assistanceType": "general_assistance",
  "query": "Help me close this deal",
  "useAgent": true,
  "conversationId": "conv-123",
  "enableToolCalling": true,
  "useRAG": true
}
```

### New Agent-Specific Endpoints

#### POST `/api/v1/assist/agent`
Full agent-enhanced assistance with all capabilities enabled.

#### GET `/api/v1/assist/agent/:conversationId/state`
Retrieve agent state for a conversation.

#### PUT `/api/v1/assist/agent/:conversationId/goal`
Update agent goal for a conversation.

#### GET `/api/v1/assist/agent/tools`
Get available tools for the current user.

#### DELETE `/api/v1/assist/agent/:conversationId/memory`
Clear agent memory for a conversation.

## Configuration

### Environment Variables

```env
# Agent Configuration
AGENT_MEMORY_TTL=3600
AGENT_MAX_TOOLS_PER_REQUEST=5
AGENT_PLANNING_ENABLED=true
AGENT_PROACTIVE_SUGGESTIONS=true
AGENT_DEFAULT_IMPORTANCE_THRESHOLD=7

# Tool Configuration
TOOL_TIMEOUT_MS=10000
TOOL_RATE_LIMIT_PER_MINUTE=60
TOOL_MAX_CONCURRENT_EXECUTIONS=3

# Memory Configuration
MEMORY_CONSOLIDATION_INTERVAL=3600
MEMORY_IMPORTANCE_THRESHOLD=7
MEMORY_MAX_SHORT_TERM=20
MEMORY_MAX_WORKING=10

# Agent Features
AGENT_TOOL_CALLING_ENABLED=true
AGENT_MEMORY_ENABLED=true
AGENT_STATE_PERSISTENCE_ENABLED=true
```

## Setup Instructions

### 1. Database Migration

Run the agent system database migration:

```bash
node scripts/run-agent-migration.js
```

### 2. Environment Configuration

Update your `.env` file with agent-specific configuration:

```bash
cp .env.example .env
# Edit .env with your agent configuration
```

### 3. Install Dependencies

All required dependencies are already included in the existing setup:
- Redis (ioredis)
- Supabase client
- OpenAI client
- pgvector support

### 4. Test the System

Run the comprehensive test suite:

```bash
node scripts/test-agent-system.js
```

### 5. Start the Service

```bash
npm run dev
```

## Usage Examples

### Basic Agent Interaction

```javascript
// Using the enhanced assist endpoint
const response = await fetch('/api/v1/assist/realtime', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify({
    context: {
      prospectName: 'John Doe',
      companyName: 'Acme Corp',
      dealStage: 'discovery'
    },
    assistanceType: 'general_assistance',
    query: 'What should I focus on in this call?',
    useAgent: true,
    conversationId: 'conv-123',
    enableToolCalling: true,
    useRAG: true
  })
});
```

### Tool Calling in Prompts

The agent can automatically call tools when it detects the need:

```
User: "What do we know about Acme Corp's budget?"
Agent: "Let me search our CRM for information about Acme Corp.

[TOOL:search_crm({"query": "Acme Corp", "type": "account"})]

Based on the CRM data, Acme Corp has an approved budget of $50k annually for software solutions..."
```

### Memory Integration

The agent automatically:
- Stores important conversation insights
- Retrieves relevant memories for context
- Builds user preference profiles
- Consolidates memories based on importance

## Monitoring and Analytics

### Health Checks

```javascript
// Check agent system health
const health = await fetch('/api/v1/assist/health');
// Returns: { status: 'healthy', agent: {...}, llm: {...} }
```

### Tool Usage Analytics

The system tracks:
- Tool execution frequency
- Success/failure rates
- Average execution times
- User-specific tool preferences

### Memory Analytics

Monitor:
- Memory consolidation patterns
- Importance score distributions
- Semantic search effectiveness
- User preference evolution

## Backward Compatibility

The agent system maintains 100% backward compatibility:

- All existing API endpoints work unchanged
- Agent features are opt-in via request parameters
- Graceful fallback to existing LLM service
- No breaking changes to existing functionality

## Next Steps

### Phase 2: Advanced Capabilities (Weeks 5-8)
- Planning engine implementation
- Reasoning patterns (ReAct, Chain-of-Thought)
- Long-term memory optimization
- Proactive suggestion system

### Phase 3: Multi-Agent System (Weeks 9-12)
- LangGraph integration
- Specialized agent roles
- Agent coordination patterns
- Human-in-the-loop workflows

### Phase 4: Production Optimization (Weeks 13-16)
- Performance tuning
- Advanced monitoring
- Security hardening
- Comprehensive documentation

## Quick Setup Guide

### Prerequisites
- Node.js v23.11+
- Docker and Docker Compose
- Supabase CLI
- Redis server

### 1. Environment Setup

```bash
# Clone and navigate to backend services
cd packages/backend-services

# Install dependencies
npm install

# Copy environment template
cp .env.example .env
```

### 2. Configure Environment Variables

Update `.env` with the following required variables:

```bash
# Server Configuration
PORT=4000
NODE_ENV=development

# Supabase Configuration (Local Instance)
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# OpenAI Configuration (for embeddings)
OPENAI_API_KEY=your_openai_api_key
OPENAI_EMBEDDING_MODEL=text-embedding-3-large
OPENAI_EMBEDDING_DIMENSIONS=3072

# Google Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key
```

### 3. Start Infrastructure Services

```bash
# Start Supabase (includes PostgreSQL with pgvector)
supabase start

# Start Redis (if not using Docker)
redis-server

# Or use Docker Compose for all services
docker-compose up -d
```

### 4. Database Setup

```bash
# Apply database migrations
npm run db:migrate

# Seed test data (optional)
npm run db:seed
```

### 5. Build and Start the Service

```bash
# Build TypeScript
npm run build

# Start in development mode
npm run dev

# Or start in production mode
npm start
```

### 6. Verify Installation

```bash
# Run health checks
curl http://localhost:4000/health
curl http://localhost:4000/api/v1/health

# Run agent system tests
npm run test:agent

# Run API integration tests
node scripts/test-api-integration.js
```

### 7. Create Test User (Optional)

```bash
# Generate test authentication token
node scripts/create-test-token.js
```

## API Endpoints

### Health and Status
- `GET /health` - Basic service health
- `GET /api/v1/health` - API version health
- `GET /api/v1/assist/health` - Agent system health (requires auth)

### Agent Assistance
- `POST /api/v1/assist/realtime` - Standard assistance
- `POST /api/v1/assist/agent` - Agent-enhanced assistance
- `POST /api/v1/assist/stream` - Streaming assistance
- `POST /api/v1/assist/multimodal` - Multimodal assistance

### Agent Management
- `GET /api/v1/assist/agent/{conversationId}/state` - Get agent state
- `PUT /api/v1/assist/agent/{conversationId}/goal` - Update agent goal
- `DELETE /api/v1/assist/agent/{conversationId}/memory` - Clear agent memory
- `GET /api/v1/assist/agent/tools` - Get available tools

## Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
# Test agent system components
npm run test:agent

# Test API endpoints
node scripts/test-api-integration.js

# Test vector search functionality
npm run test:vector
```

### Demo Scripts
```bash
# Run agent system demo
node scripts/demo-agent-system.js

# Test RAG system
node scripts/test-rag-system.js
```

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   - Verify Redis is running
   - Check REDIS_HOST and REDIS_PORT configuration

2. **Database Migration Failures**
   - Ensure pgvector extension is available
   - Check Supabase permissions
   - Run migration manually if needed

3. **Memory Search Issues**
   - Verify OpenAI API key is configured
   - Check embedding model configuration
   - Ensure vector indexes are created

4. **Tool Execution Timeouts**
   - Adjust TOOL_TIMEOUT_MS setting
   - Check tool implementation for performance issues
   - Monitor tool execution logs

### Support

For issues and questions:
1. Check the test suite output for specific errors
2. Review the health check endpoints
3. Examine the application logs for detailed error messages
4. Refer to the implementation guide for architectural details
