# AI Agent System Testing Guide

## Overview

This guide provides comprehensive instructions for testing the AI Agent System, including setup, execution, and validation of all advanced capabilities.

## Prerequisites

### Environment Setup

1. **Node.js and Dependencies**
   ```bash
   cd packages/backend-services
   npm install
   ```

2. **Supabase Local Development**
   ```bash
   # From project root
   supabase start
   ```

3. **Environment Configuration**
   ```bash
   # Copy and configure environment variables
   cp .env.example .env
   
   # Required variables for testing:
   SUPABASE_URL=http://127.0.0.1:54321
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   REDIS_HOST=localhost
   REDIS_PORT=6379
   OPENAI_API_KEY=your-openai-api-key
   ```

4. **Database Migration**
   ```bash
   # Run the agent system migration
   node scripts/run-agent-migration-direct.js
   ```

## Testing Levels

### 1. Unit Tests

Test individual components in isolation:

```bash
# Run all unit tests
npm test

# Run specific test files
npm test -- agentSystem.test.ts
npm test -- advancedAgentSystem.test.ts

# Run tests with coverage
npm test -- --coverage
```

**Key Unit Test Areas:**
- Agent State Management
- Memory Operations
- Tool Orchestration
- Planning Engine
- Reasoning Engine
- Proactive Suggestions

### 2. Integration Tests

Test service interactions and data flow:

```bash
# Run integration tests
npm test -- --testNamePattern="Integration"

# Run specific integration scenarios
npm test -- --testNamePattern="maintain consistency"
```

**Integration Test Coverage:**
- Agent state persistence across services
- Memory consolidation and retrieval
- Tool execution with state updates
- Planning session management
- Reasoning chain execution

### 3. End-to-End Tests

Test complete agent workflows:

```bash
# Run E2E test script
node scripts/test-agent-system.js

# Run with verbose output
DEBUG=* node scripts/test-agent-system.js
```

## Manual Testing Procedures

### 1. Basic Agent Functionality

**Test Agent State Management:**
```bash
# Start the development server
npm run dev

# Test agent state creation and retrieval
curl -X POST http://localhost:3000/api/v1/assist/agent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "context": {
      "prospectName": "John Doe",
      "companyName": "Acme Corp",
      "dealStage": "discovery"
    },
    "assistanceType": "general_assistance",
    "query": "Help me prepare for this call",
    "conversationId": "test-conv-123",
    "enableToolCalling": true,
    "useRAG": true
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "response": "Based on your conversation context...",
  "agentState": {
    "conversationId": "test-conv-123",
    "currentGoal": null,
    "memoryItems": 1
  },
  "toolsUsed": [],
  "memoryContext": "Available",
  "metadata": {
    "agentEnabled": true,
    "toolCallsExecuted": 0,
    "memoryItemsRetrieved": 1,
    "processingTime": 1234
  }
}
```

### 2. Advanced Agent Features

**Test Planning Capabilities:**
```bash
curl -X POST http://localhost:3000/api/v1/assist/agent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "context": {
      "prospectName": "Jane Smith",
      "companyName": "TechCorp",
      "dealStage": "discovery"
    },
    "assistanceType": "general_assistance",
    "query": "Create a comprehensive plan to qualify this lead",
    "conversationId": "test-planning-456",
    "enablePlanning": true,
    "enableToolCalling": true
  }'
```

**Test Reasoning Patterns:**
```bash
curl -X POST http://localhost:3000/api/v1/assist/agent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "context": {
      "prospectName": "Bob Johnson",
      "companyName": "StartupXYZ",
      "dealStage": "presentation"
    },
    "assistanceType": "general_assistance",
    "query": "What is the best strategy to handle their pricing objections?",
    "conversationId": "test-reasoning-789",
    "enableReasoning": true,
    "reasoningPattern": "react"
  }'
```

**Test Proactive Suggestions:**
```bash
curl -X POST http://localhost:3000/api/v1/assist/agent \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "context": {
      "prospectName": "Alice Brown",
      "companyName": "Enterprise Inc",
      "dealStage": "closing"
    },
    "assistanceType": "general_assistance",
    "query": "The prospect seems hesitant about moving forward",
    "conversationId": "test-suggestions-101",
    "enableProactiveSuggestions": true
  }'
```

### 3. Agent Management Endpoints

**Get Agent State:**
```bash
curl -X GET http://localhost:3000/api/v1/assist/agent/test-conv-123/state \
  -H "Authorization: Bearer your-token"
```

**Update Agent Goal:**
```bash
curl -X PUT http://localhost:3000/api/v1/assist/agent/test-conv-123/goal \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{"goal": "Close the deal with Acme Corp by end of quarter"}'
```

**Get Available Tools:**
```bash
curl -X GET http://localhost:3000/api/v1/assist/agent/tools \
  -H "Authorization: Bearer your-token"
```

**Clear Agent Memory:**
```bash
curl -X DELETE http://localhost:3000/api/v1/assist/agent/test-conv-123/memory \
  -H "Authorization: Bearer your-token"
```

## Performance Testing

### 1. Load Testing

**Test Concurrent Requests:**
```bash
# Install artillery for load testing
npm install -g artillery

# Create load test configuration
cat > load-test.yml << EOF
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Agent Assistance"
    requests:
      - post:
          url: "/api/v1/assist/agent"
          headers:
            Authorization: "Bearer your-token"
            Content-Type: "application/json"
          json:
            context:
              prospectName: "Load Test User"
              companyName: "Test Corp"
              dealStage: "discovery"
            assistanceType: "general_assistance"
            query: "Help with load testing"
            conversationId: "load-test-{{ \$randomString() }}"
EOF

# Run load test
artillery run load-test.yml
```

### 2. Memory Performance Testing

**Test Memory Operations:**
```bash
# Run memory performance test
node -e "
const { MemoryService } = require('./dist/services/memoryService');
const { AgentStateService } = require('./dist/services/agentStateService');

async function testMemoryPerformance() {
  const memoryService = new MemoryService();
  const agentStateService = new AgentStateService();
  
  const conversationId = 'perf-test-' + Date.now();
  const agentState = await agentStateService.createNewAgentState(
    conversationId, 
    'perf-user', 
    { prospectName: 'Perf Test' }
  );
  
  console.time('Memory Operations');
  
  // Add 100 memories
  for (let i = 0; i < 100; i++) {
    await memoryService.addMemory(
      agentState,
      \`Performance test memory \${i}\`,
      'conversation',
      Math.floor(Math.random() * 10) + 1
    );
  }
  
  // Retrieve memories
  const memories = await memoryService.retrieveRelevantMemories(
    'performance test',
    conversationId,
    { limit: 10 }
  );
  
  console.timeEnd('Memory Operations');
  console.log(\`Retrieved \${memories.length} memories\`);
  
  // Cleanup
  await memoryService.clearMemories(conversationId);
  await agentStateService.deleteAgentState(conversationId);
}

testMemoryPerformance().catch(console.error);
"
```

## Validation Procedures

### 1. Feature Validation

**Validate Planning Engine:**
```bash
# Test planning session creation and execution
node -e "
const { PlanningEngine } = require('./dist/services/planningEngine');
const { AgentStateService } = require('./dist/services/agentStateService');

async function validatePlanning() {
  const planningEngine = new PlanningEngine();
  const agentStateService = new AgentStateService();
  
  const agentState = await agentStateService.createNewAgentState(
    'planning-validation',
    'test-user',
    { prospectName: 'Validation Test' }
  );
  
  const context = {
    agentState,
    availableTools: ['search_knowledge', 'search_crm'],
    timeConstraints: { maxDuration: 30 },
    resources: { maxToolCalls: 5, maxSteps: 10 }
  };
  
  const session = await planningEngine.createPlanningSession(
    'Validate planning capabilities',
    context
  );
  
  console.log('Planning Session Created:');
  console.log('- ID:', session.id);
  console.log('- Goal:', session.goal.description);
  console.log('- Steps:', session.steps.length);
  console.log('- Status:', session.status);
  
  // Execute first step
  const result = await planningEngine.executeNextStep(session.id, context);
  console.log('First Step Executed:');
  console.log('- Step ID:', result.step?.id);
  console.log('- Success:', result.result?.success);
  console.log('- Completed:', result.completed);
  
  // Cleanup
  await agentStateService.deleteAgentState('planning-validation');
}

validatePlanning().catch(console.error);
"
```

**Validate Reasoning Engine:**
```bash
# Test reasoning patterns
node -e "
const { ReasoningEngine } = require('./dist/services/reasoningEngine');
const { AgentStateService } = require('./dist/services/agentStateService');

async function validateReasoning() {
  const reasoningEngine = new ReasoningEngine();
  const agentStateService = new AgentStateService();
  
  const agentState = await agentStateService.createNewAgentState(
    'reasoning-validation',
    'test-user',
    { prospectName: 'Reasoning Test' }
  );
  
  // Test ReAct reasoning
  const reactChain = await reasoningEngine.executeReActReasoning(
    'How should I approach this sales conversation?',
    agentState,
    2
  );
  
  console.log('ReAct Reasoning:');
  console.log('- Chain ID:', reactChain.id);
  console.log('- Pattern:', reactChain.pattern.name);
  console.log('- Steps:', reactChain.steps.length);
  console.log('- Status:', reactChain.status);
  
  // Test Chain-of-Thought reasoning
  const cotChain = await reasoningEngine.executeChainOfThought(
    'What are the key factors for closing this deal?',
    agentState,
    3
  );
  
  console.log('Chain-of-Thought Reasoning:');
  console.log('- Chain ID:', cotChain.id);
  console.log('- Pattern:', cotChain.pattern.name);
  console.log('- Steps:', cotChain.steps.length);
  console.log('- Status:', cotChain.status);
  
  // Cleanup
  await agentStateService.deleteAgentState('reasoning-validation');
}

validateReasoning().catch(console.error);
"
```

### 2. Health Check Validation

**System Health Check:**
```bash
curl -X GET http://localhost:3000/api/v1/assist/health \
  -H "Authorization: Bearer your-token"
```

**Expected Healthy Response:**
```json
{
  "status": "healthy",
  "service": "assist",
  "llm": "connected",
  "agent": {
    "redis": true,
    "database": true
  },
  "overall": true,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check Supabase status
   supabase status
   
   # Restart if needed
   supabase stop
   supabase start
   ```

2. **Redis Connection Errors**
   ```bash
   # Check Redis status
   redis-cli ping
   
   # Start Redis if needed (macOS)
   brew services start redis
   ```

3. **Migration Failures**
   ```bash
   # Run migration manually
   node scripts/run-agent-migration-direct.js
   
   # Check migration status
   supabase db diff
   ```

4. **Test Failures**
   ```bash
   # Run tests with verbose output
   npm test -- --verbose
   
   # Run specific failing test
   npm test -- --testNamePattern="specific test name"
   ```

### Debug Mode

**Enable Debug Logging:**
```bash
# Set debug environment variable
export DEBUG=*

# Run with debug output
npm run dev

# Or for specific modules
export DEBUG=agent:*,memory:*,planning:*
```

**Check Logs:**
```bash
# View application logs
tail -f logs/application.log

# View error logs
tail -f logs/error.log
```

## Performance Benchmarks

### Expected Performance Metrics

- **Agent State Operations**: < 100ms
- **Memory Retrieval**: < 200ms
- **Tool Execution**: < 5s
- **Planning Session Creation**: < 2s
- **Reasoning Chain Execution**: < 10s
- **Suggestion Generation**: < 500ms

### Monitoring

**Key Metrics to Monitor:**
- Response times for all endpoints
- Memory usage and growth patterns
- Database query performance
- Redis cache hit rates
- Tool execution success rates
- Agent session lifecycle metrics

## Continuous Testing

### Automated Testing Pipeline

```bash
# Create test script for CI/CD
cat > test-pipeline.sh << 'EOF'
#!/bin/bash
set -e

echo "Starting Agent System Test Pipeline..."

# 1. Environment setup
npm install
supabase start

# 2. Database migration
node scripts/run-agent-migration-direct.js

# 3. Unit tests
npm test

# 4. Integration tests
npm test -- --testNamePattern="Integration"

# 5. E2E tests
node scripts/test-agent-system.js

# 6. Performance tests
npm run test:performance

echo "All tests passed successfully!"
EOF

chmod +x test-pipeline.sh
```

This comprehensive testing guide ensures your AI Agent System is thoroughly validated and ready for production deployment. The tests cover all aspects from basic functionality to advanced reasoning capabilities, providing confidence in the system's reliability and performance.
