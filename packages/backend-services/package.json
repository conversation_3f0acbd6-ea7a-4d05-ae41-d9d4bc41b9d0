{"name": "backend-services", "version": "0.1.0", "private": true, "main": "./src/index.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "NODE_ENV=production node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "devDependencies": {"@types/bull": "^3.15.9", "@types/jest": "^29.5.14", "@types/pdf-parse": "^1.1.5", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "typescript": "^5.0.0"}, "dependencies": {"@anthropic-ai/sdk": "^0.60.0", "@google/genai": "^1.0.1", "@supabase/supabase-js": "^2.49.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/multer": "^2.0.0", "bull": "^4.16.5", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "ioredis": "^5.6.1", "mammoth": "^1.9.1", "multer": "^2.0.1", "openai": "^5.8.2", "pdf-parse": "^1.1.1", "ts-node": "^10.9.2", "yn": "^3.1.1"}}