import { LLMOrchestrationService } from '../../services/llmOrchestrationService'
import { GoogleGenAI } from '@google/genai'

// Mock Google GenAI
jest.mock('@google/genai')
const mockGoogleGenAI = GoogleGenAI as jest.MockedClass<typeof GoogleGenAI>

describe('LLMOrchestrationService', () => {
  let mockGenAI: any
  let mockModel: any

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockModel = {
      generateContent: jest.fn(),
      generateContentStream: jest.fn()
    }

    mockGenAI = {
      models: mockModel
    }

    mockGoogleGenAI.mockImplementation(() => mockGenAI)
  })

  describe('generateText', () => {
    it('should generate text successfully', async () => {
      const mockResponse = {
        text: 'This is a generated response from the AI model.',
        usageMetadata: {
          promptTokenCount: 50,
          candidatesTokenCount: 25,
          totalTokenCount: 75
        }
      }

      mockModel.generateContent.mockResolvedValue(mockResponse)

      const result = await LLMOrchestrationService.generateText({
        prompt: 'Test prompt',
        temperature: 0.7,
        maxOutputTokens: 1000
      })

      expect(result.success).toBe(true)
      expect(result.text).toBe(mockResponse.text)
      expect(result.usage).toEqual({
        promptTokens: 50,
        candidatesTokens: 25,
        totalTokens: 75
      })
      expect(mockModel.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.0-flash-001',
        contents: [{ parts: [{ text: 'Test prompt' }] }],
        config: {
          temperature: 0.7,
          maxOutputTokens: 1000,
          topK: 40,
          topP: 0.95,
          candidateCount: 1
        }
      })
    })

    it('should handle empty response', async () => {
      mockModel.generateContent.mockResolvedValue({
        text: null
      })

      const result = await LLMOrchestrationService.generateText({
        prompt: 'Test prompt'
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('No text generated from the model.')
    })

    it('should handle API errors', async () => {
      mockModel.generateContent.mockRejectedValue(new Error('API rate limit exceeded'))

      const result = await LLMOrchestrationService.generateText({
        prompt: 'Test prompt'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('API rate limit exceeded')
    })

    it('should use default parameters when not provided', async () => {
      mockModel.generateContent.mockResolvedValue({
        text: 'Generated text'
      })

      await LLMOrchestrationService.generateText({
        prompt: 'Test prompt'
      })

      expect(mockModel.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.0-flash-001',
        contents: [{ parts: [{ text: 'Test prompt' }] }],
        config: {
          temperature: 0.7,
          maxOutputTokens: 1000,
          topK: 40,
          topP: 0.95,
          candidateCount: 1
        }
      })
    })

    it('should handle custom parameters', async () => {
      mockModel.generateContent.mockResolvedValue({
        text: 'Generated text'
      })

      await LLMOrchestrationService.generateText({
        prompt: 'Test prompt',
        temperature: 0.9,
        maxOutputTokens: 2000,
        topK: 20,
        topP: 0.8
      })

      expect(mockModel.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.0-flash-001',
        contents: [{ parts: [{ text: 'Test prompt' }] }],
        config: {
          temperature: 0.9,
          maxOutputTokens: 2000,
          topK: 20,
          topP: 0.8,
          candidateCount: 1
        }
      })
    })
  })

  describe('generateTextStream', () => {
    it('should generate streaming text successfully', async () => {
      const mockChunks = [
        { text: 'First chunk ' },
        { text: 'second chunk ' },
        { text: 'final chunk.' }
      ]

      // Mock async iterator
      const mockAsyncIterator = {
        [Symbol.asyncIterator]: async function* () {
          for (const chunk of mockChunks) {
            yield chunk
          }
        }
      }

      mockModel.generateContentStream.mockResolvedValue(mockAsyncIterator)

      const streamGenerator = await LLMOrchestrationService.generateTextStream({
        prompt: 'Test streaming prompt'
      })

      const chunks = []
      for await (const chunk of streamGenerator) {
        chunks.push(chunk)
      }

      expect(chunks).toHaveLength(4) // 3 text chunks + 1 done chunk
      expect(chunks[0]).toEqual({ text: 'First chunk ', done: false })
      expect(chunks[1]).toEqual({ text: 'second chunk ', done: false })
      expect(chunks[2]).toEqual({ text: 'final chunk.', done: false })
      expect(chunks[3]).toEqual({ text: '', done: true })
    })

    it('should handle streaming errors', async () => {
      mockModel.generateContentStream.mockRejectedValue(new Error('Streaming failed'))

      await expect(LLMOrchestrationService.generateTextStream({
        prompt: 'Test prompt'
      })).rejects.toThrow('Streaming generation failed: Streaming failed')
    })
  })

  describe('generateMultimodalContent', () => {
    it('should generate multimodal content with text and image', async () => {
      const mockResponse = {
        text: 'Based on the image and text, here is the response.',
        usageMetadata: {
          promptTokenCount: 100,
          candidatesTokenCount: 50,
          totalTokenCount: 150
        }
      }

      mockModel.generateContent.mockResolvedValue(mockResponse)

      const result = await LLMOrchestrationService.generateMultimodalContent({
        content: {
          text: 'Describe this image',
          imageData: 'base64-image-data',
          imageMimeType: 'image/png'
        }
      })

      expect(result.success).toBe(true)
      expect(result.text).toBe(mockResponse.text)
      expect(mockModel.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.0-flash-001',
        contents: [{
          parts: [
            { text: 'Describe this image' },
            {
              inlineData: {
                mimeType: 'image/png',
                data: 'base64-image-data'
              }
            }
          ]
        }],
        config: {
          temperature: 0.7,
          maxOutputTokens: 1000,
          topK: 40,
          topP: 0.95,
          candidateCount: 1
        }
      })
    })

    it('should generate multimodal content with audio', async () => {
      const mockResponse = {
        text: 'Based on the audio, here is the response.'
      }

      mockModel.generateContent.mockResolvedValue(mockResponse)

      const result = await LLMOrchestrationService.generateMultimodalContent({
        content: {
          text: 'Analyze this audio',
          audioData: 'base64-audio-data',
          audioMimeType: 'audio/wav'
        }
      })

      expect(result.success).toBe(true)
      expect(mockModel.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.0-flash-001',
        contents: [{
          parts: [
            { text: 'Analyze this audio' },
            {
              inlineData: {
                mimeType: 'audio/wav',
                data: 'base64-audio-data'
              }
            }
          ]
        }],
        config: {
          temperature: 0.7,
          maxOutputTokens: 1000,
          topK: 40,
          topP: 0.95,
          candidateCount: 1
        }
      })
    })

    it('should handle missing content', async () => {
      const result = await LLMOrchestrationService.generateMultimodalContent({
        content: {}
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('At least one content type (text, audio, or image) must be provided.')
    })

    it('should strip data URL prefix from image data', async () => {
      mockModel.generateContent.mockResolvedValue({
        text: 'Response'
      })

      await LLMOrchestrationService.generateMultimodalContent({
        content: {
          text: 'Test',
          imageData: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
          imageMimeType: 'image/png'
        }
      })

      const call = mockModel.generateContent.mock.calls[0][0]
      const imageData = call.contents[0].parts[1].inlineData.data
      expect(imageData).toBe('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==')
    })
  })

  describe('validateConnection', () => {
    it('should validate successful connection', async () => {
      mockModel.generateContent.mockResolvedValue({
        text: 'Connection test successful'
      })

      const result = await LLMOrchestrationService.validateConnection()

      expect(result.success).toBe(true)
      expect(mockModel.generateContent).toHaveBeenCalledWith({
        model: 'gemini-2.0-flash-001',
        contents: [{ parts: [{ text: 'Test connection' }] }],
        config: {
          temperature: 0,
          maxOutputTokens: 10,
          topK: 1,
          topP: 1,
          candidateCount: 1
        }
      })
    })

    it('should handle connection failure', async () => {
      mockModel.generateContent.mockRejectedValue(new Error('Invalid API key'))

      const result = await LLMOrchestrationService.validateConnection()

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid API key')
    })
  })
})
