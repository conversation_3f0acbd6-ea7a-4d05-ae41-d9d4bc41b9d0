import { authMiddleware } from '../authMiddleware'
import { supabase } from '../supabaseClient'
import { Request, Response, NextFunction } from 'express'

// Mock Supabase client
jest.mock('../supabaseClient')
const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('Auth Middleware', () => {
  let mockReq: Partial<Request>
  let mockRes: Partial<Response>
  let mockNext: NextFunction

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockReq = {
      headers: {}
    }
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    }
    mockNext = jest.fn()
  })

  it('should authenticate user with valid token', async () => {
    mockReq.headers = {
      authorization: 'Bearer valid-token-123'
    }

    mockSupabase.auth.getUser.mockResolvedValue({
      data: {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          user_metadata: {
            username: 'testuser'
          }
        }
      },
      error: null
    } as any)

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('valid-token-123')
    expect((mockReq as any).user).toEqual({
      id: 'user-123',
      email: '<EMAIL>',
      user_metadata: {
        username: 'testuser'
      }
    })
    expect(mockNext).toHaveBeenCalled()
    expect(mockRes.status).not.toHaveBeenCalled()
  })

  it('should return 401 for missing authorization header', async () => {
    mockReq.headers = {}

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Authorization header required'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should return 401 for invalid authorization header format', async () => {
    mockReq.headers = {
      authorization: 'InvalidFormat token-123'
    }

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Invalid authorization header format'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should return 401 for authorization header without Bearer prefix', async () => {
    mockReq.headers = {
      authorization: 'token-123'
    }

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Invalid authorization header format'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should return 401 for expired token', async () => {
    mockReq.headers = {
      authorization: 'Bearer expired-token'
    }

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: { message: 'JWT expired' }
    } as any)

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('expired-token')
    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Invalid or expired token'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should return 401 for invalid token', async () => {
    mockReq.headers = {
      authorization: 'Bearer invalid-token'
    }

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: { message: 'Invalid JWT' }
    } as any)

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('invalid-token')
    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Invalid or expired token'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should return 401 when user is null even without error', async () => {
    mockReq.headers = {
      authorization: 'Bearer some-token'
    }

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: null
    } as any)

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Invalid or expired token'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should handle Supabase service errors', async () => {
    mockReq.headers = {
      authorization: 'Bearer valid-token'
    }

    mockSupabase.auth.getUser.mockRejectedValue(new Error('Supabase service unavailable'))

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockRes.status).toHaveBeenCalledWith(500)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Authentication service error'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should handle case-insensitive authorization header', async () => {
    mockReq.headers = {
      Authorization: 'Bearer valid-token-123' // Capital A
    }

    mockSupabase.auth.getUser.mockResolvedValue({
      data: {
        user: {
          id: 'user-123',
          email: '<EMAIL>'
        }
      },
      error: null
    } as any)

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('valid-token-123')
    expect(mockNext).toHaveBeenCalled()
  })

  it('should trim whitespace from token', async () => {
    mockReq.headers = {
      authorization: 'Bearer  valid-token-123  ' // Extra spaces
    }

    mockSupabase.auth.getUser.mockResolvedValue({
      data: {
        user: {
          id: 'user-123',
          email: '<EMAIL>'
        }
      },
      error: null
    } as any)

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockSupabase.auth.getUser).toHaveBeenCalledWith('valid-token-123')
    expect(mockNext).toHaveBeenCalled()
  })

  it('should handle empty token after Bearer', async () => {
    mockReq.headers = {
      authorization: 'Bearer '
    }

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Invalid authorization header format'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })

  it('should handle Bearer with only whitespace', async () => {
    mockReq.headers = {
      authorization: 'Bearer    '
    }

    await authMiddleware(mockReq as Request, mockRes as Response, mockNext)

    expect(mockRes.status).toHaveBeenCalledWith(401)
    expect(mockRes.json).toHaveBeenCalledWith({
      error: 'Invalid authorization header format'
    })
    expect(mockNext).not.toHaveBeenCalled()
  })
})
