import {
  ValidationError,
  LLMError,
  ExternalServiceError,
  handleValidationError,
  handleLLMError,
  createHealthCheckError,
  globalErrorHandler,
  requestIdMiddleware,
  notFoundHandler,
  asyncHandler
} from '../../utils/errorHandler'
import { Request, Response, NextFunction } from 'express'

describe('Error Handler Utils', () => {
  let mockReq: Partial<Request>
  let mockRes: Partial<Response>
  let mockNext: NextFunction

  beforeEach(() => {
    mockReq = {
      method: 'POST',
      url: '/api/test',
      headers: {}
    }
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      setHeader: jest.fn()
    }
    mockNext = jest.fn()
  })

  describe('Custom Error Classes', () => {
    it('should create ValidationError correctly', () => {
      const error = new ValidationError('Invalid input data')
      expect(error.name).toBe('ValidationError')
      expect(error.message).toBe('Invalid input data')
      expect(error.statusCode).toBe(400)
    })

    it('should create LLMError correctly', () => {
      const error = new LLMError('LLM service unavailable')
      expect(error.name).toBe('LLMError')
      expect(error.message).toBe('LLM service unavailable')
      expect(error.statusCode).toBe(500)
    })

    it('should create ExternalServiceError correctly', () => {
      const error = new ExternalServiceError('Database', 'Connection failed')
      expect(error.name).toBe('ExternalServiceError')
      expect(error.message).toContain('Database')
      expect(error.message).toContain('Connection failed')
      expect(error.statusCode).toBe(503)
    })
  })

  describe('handleValidationError', () => {
    it('should throw ValidationError for invalid validation result', () => {
      const validationResult = {
        isValid: false,
        errors: ['Field is required', 'Invalid format']
      }

      expect(() => {
        handleValidationError(validationResult, 'Test validation')
      }).toThrow(ValidationError)

      try {
        handleValidationError(validationResult, 'Test validation')
      } catch (error: any) {
        expect(error.message).toContain('Test validation')
        expect(error.details.errors).toContain('Field is required')
        expect(error.details.errors).toContain('Invalid format')
      }
    })

    it('should not throw for valid validation result', () => {
      const validationResult = {
        isValid: true,
        errors: []
      }

      expect(() => {
        handleValidationError(validationResult, 'Test validation')
      }).not.toThrow()
    })
  })

  describe('handleLLMError', () => {
    it('should throw LLMError for failed LLM response', () => {
      const llmResponse = {
        success: false,
        error: 'API rate limit exceeded'
      }

      expect(() => {
        handleLLMError(llmResponse, 'Text generation')
      }).toThrow(LLMError)

      try {
        handleLLMError(llmResponse, 'Text generation')
      } catch (error: any) {
        expect(error.message).toContain('Text generation')
        expect(error.message).toContain('API rate limit exceeded')
      }
    })

    it('should not throw for successful LLM response', () => {
      const llmResponse = {
        success: true,
        text: 'Generated text'
      }

      expect(() => {
        handleLLMError(llmResponse, 'Text generation')
      }).not.toThrow()
    })
  })

  describe('createHealthCheckError', () => {
    it('should create ExternalServiceError with service name', () => {
      const error = createHealthCheckError('Redis', 'Connection timeout')

      expect(error).toBeInstanceOf(ExternalServiceError)
      expect(error.message).toContain('Redis')
      expect(error.details.originalError).toContain('Connection timeout')
    })
  })

  describe('requestIdMiddleware', () => {
    it('should add request ID to headers', () => {
      requestIdMiddleware(mockReq as Request, mockRes as Response, mockNext)

      expect(mockReq.headers!['x-request-id']).toBeDefined()
      expect(typeof mockReq.headers!['x-request-id']).toBe('string')
      expect(mockNext).toHaveBeenCalled()
    })

    it('should preserve existing request ID', () => {
      mockReq.headers = { 'x-request-id': 'existing-id' }

      requestIdMiddleware(mockReq as Request, mockRes as Response, mockNext)

      expect(mockReq.headers['x-request-id']).toBe('existing-id')
      expect(mockNext).toHaveBeenCalled()
    })
  })

  describe('notFoundHandler', () => {
    it('should throw NotFoundError for unknown routes', () => {
      expect(() => {
        notFoundHandler(mockReq as Request, mockRes as Response, mockNext)
      }).toThrow('Route POST /api/test not found')
    })
  })

  describe('globalErrorHandler', () => {
    it('should handle ValidationError', () => {
      const error = new ValidationError('Invalid input')

      globalErrorHandler(error, mockReq as Request, mockRes as Response, mockNext)

      expect(mockRes.status).toHaveBeenCalledWith(400)
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Invalid input',
          code: 'VALIDATION_ERROR'
        })
      )
    })

    it('should handle LLMError', () => {
      const error = new LLMError('LLM service failed')

      globalErrorHandler(error, mockReq as Request, mockRes as Response, mockNext)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'LLM service failed',
          code: 'LLM_ERROR'
        })
      )
    })

    it('should handle ExternalServiceError', () => {
      const error = new ExternalServiceError('TestService', 'Service unavailable')

      globalErrorHandler(error, mockReq as Request, mockRes as Response, mockNext)

      expect(mockRes.status).toHaveBeenCalledWith(503)
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.stringContaining('Service unavailable'),
          code: 'EXTERNAL_SERVICE_ERROR'
        })
      )
    })

    it('should handle generic errors', () => {
      const error = new Error('Unexpected error')

      globalErrorHandler(error, mockReq as Request, mockRes as Response, mockNext)

      expect(mockRes.status).toHaveBeenCalledWith(500)
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Internal server error',
          code: 'INTERNAL_ERROR'
        })
      )
    })

    it('should include request ID when available', () => {
      mockReq.headers = { 'x-request-id': 'test-request-123' }
      const error = new ValidationError('Test error')

      globalErrorHandler(error, mockReq as Request, mockRes as Response, mockNext)

      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Test error',
          code: 'VALIDATION_ERROR',
          requestId: 'test-request-123'
        })
      )
    })

    it('should log errors in development', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      const error = new Error('Test error')

      globalErrorHandler(error, mockReq as Request, mockRes as Response, mockNext)

      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })
  })

  describe('asyncHandler', () => {
    it('should handle successful async operations', async () => {
      const asyncFn = jest.fn().mockResolvedValue('success')
      const wrappedFn = asyncHandler(asyncFn)

      await wrappedFn(mockReq as Request, mockRes as Response, mockNext)

      expect(asyncFn).toHaveBeenCalledWith(mockReq, mockRes, mockNext)
      expect(mockNext).not.toHaveBeenCalled()
    })

    it('should catch and forward async errors', async () => {
      const error = new Error('Async error')
      const asyncFn = jest.fn().mockRejectedValue(error)
      const wrappedFn = asyncHandler(asyncFn)

      await wrappedFn(mockReq as Request, mockRes as Response, mockNext)

      expect(asyncFn).toHaveBeenCalledWith(mockReq, mockRes, mockNext)
      expect(mockNext).toHaveBeenCalledWith(error)
    })
  })
})
