import request from 'supertest';
import express from 'express';
import { createClient } from '@supabase/supabase-js';
import { getDocumentService } from '../services/documentProcessingService';
import { getHealthCheckService } from '../services/healthCheckService';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:54321';
process.env.SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-key';
process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY || 'test-openai-key';

describe('RAG System End-to-End Tests', () => {
  let app: express.Application;
  let supabase: any;
  let testUserId: string;
  let testDocumentId: string;

  beforeAll(async () => {
    // Initialize test app
    app = express();
    app.use(express.json());
    
    // Initialize Supabase client
    supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Create test user
    testUserId = 'test-user-' + Date.now();
    
    // Clean up any existing test data
    await cleanupTestData();
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  async function cleanupTestData() {
    try {
      // Delete test documents and chunks
      await supabase
        .from('document_chunks')
        .delete()
        .like('user_id', 'test-user-%');
        
      await supabase
        .from('documents')
        .delete()
        .like('user_id', 'test-user-%');
    } catch (error) {
      console.warn('Cleanup warning:', error);
    }
  }

  describe('Health Checks', () => {
    test('should perform comprehensive health check', async () => {
      const healthService = getHealthCheckService();
      const healthStatus = await healthService.performHealthCheck();

      expect(healthStatus).toHaveProperty('overall');
      expect(healthStatus).toHaveProperty('services');
      expect(healthStatus).toHaveProperty('summary');
      expect(healthStatus.services).toBeInstanceOf(Array);
      expect(healthStatus.services.length).toBeGreaterThan(0);

      // Check that all required services are present
      const serviceNames = healthStatus.services.map(s => s.service);
      expect(serviceNames).toContain('database');
      expect(serviceNames).toContain('openai');
      expect(serviceNames).toContain('vector_search');
    });

    test('should validate individual service health', async () => {
      const healthService = getHealthCheckService();
      const healthStatus = await healthService.performHealthCheck();

      for (const service of healthStatus.services) {
        expect(service).toHaveProperty('service');
        expect(service).toHaveProperty('status');
        expect(service).toHaveProperty('responseTime');
        expect(['healthy', 'degraded', 'unhealthy']).toContain(service.status);
        expect(service.responseTime).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('Document Processing Pipeline', () => {
    test('should validate document before processing', async () => {
      const documentService = getDocumentService();

      // Test with invalid file type
      const invalidFile = {
        originalname: 'test.xyz',
        mimetype: 'application/unknown',
        size: 1000,
        buffer: Buffer.from('test content')
      } as Express.Multer.File;

      await expect(
        documentService.processDocument(invalidFile, testUserId)
      ).rejects.toThrow('Unsupported file type');
    });

    test('should reject oversized documents', async () => {
      const documentService = getDocumentService();

      // Test with oversized file (simulate 100MB file)
      const oversizedFile = {
        originalname: 'large.txt',
        mimetype: 'text/plain',
        size: 100 * 1024 * 1024, // 100MB
        buffer: Buffer.from('test content')
      } as Express.Multer.File;

      await expect(
        documentService.processDocument(oversizedFile, testUserId)
      ).rejects.toThrow('exceeds maximum allowed size');
    });

    test('should reject empty files', async () => {
      const documentService = getDocumentService();

      const emptyFile = {
        originalname: 'empty.txt',
        mimetype: 'text/plain',
        size: 0,
        buffer: Buffer.from('')
      } as Express.Multer.File;

      await expect(
        documentService.processDocument(emptyFile, testUserId)
      ).rejects.toThrow('Cannot process empty file');
    });

    test('should process valid text document successfully', async () => {
      const documentService = getDocumentService();

      const validFile = {
        originalname: 'test-document.txt',
        mimetype: 'text/plain',
        size: 500,
        buffer: Buffer.from('This is a test document with some content for processing. It contains multiple sentences to test the chunking and embedding generation process.')
      } as Express.Multer.File;

      const result = await documentService.processDocument(validFile, testUserId);

      expect(result).toHaveProperty('jobId');
      expect(result).toHaveProperty('status');
      expect(result.status).toBe('queued');

      // Store document ID for later tests
      if (result.documentId) {
        testDocumentId = result.documentId;
      }
    }, 30000); // 30 second timeout for processing
  });

  describe('Vector Search Functionality', () => {
    test('should perform vector search with valid parameters', async () => {
      // Wait for document processing to complete
      await new Promise(resolve => setTimeout(resolve, 5000));

      const { data, error } = await supabase.rpc('search_document_chunks', {
        query_embedding: new Array(1536).fill(0.1),
        similarity_threshold: 0.1,
        match_count: 5,
        user_id_filter: testUserId,
        document_ids_filter: null
      });

      expect(error).toBeNull();
      expect(data).toBeInstanceOf(Array);
    });

    test('should respect user isolation in vector search', async () => {
      const otherUserId = 'other-test-user-' + Date.now();

      const { data, error } = await supabase.rpc('search_document_chunks', {
        query_embedding: new Array(1536).fill(0.1),
        similarity_threshold: 0.1,
        match_count: 5,
        user_id_filter: otherUserId,
        document_ids_filter: null
      });

      expect(error).toBeNull();
      expect(data).toBeInstanceOf(Array);
      expect(data.length).toBe(0); // Should return no results for different user
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle invalid embedding dimensions gracefully', async () => {
      const { data, error } = await supabase.rpc('search_document_chunks', {
        query_embedding: new Array(100).fill(0.1), // Wrong dimensions
        similarity_threshold: 0.1,
        match_count: 5,
        user_id_filter: testUserId,
        document_ids_filter: null
      });

      expect(error).not.toBeNull();
      expect(error.message).toContain('dimension');
    });

    test('should handle database connection errors gracefully', async () => {
      // This test would require mocking database failures
      // For now, we'll test that the health check can detect database issues
      const healthService = getHealthCheckService();
      const healthStatus = await healthService.performHealthCheck();
      
      const dbService = healthStatus.services.find(s => s.service === 'database');
      expect(dbService).toBeDefined();
      expect(dbService!.responseTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Memory Management', () => {
    test('should handle memory usage efficiently during processing', async () => {
      const initialMemory = process.memoryUsage();
      
      // Process multiple small documents
      const documentService = getDocumentService();
      const promises = [];

      for (let i = 0; i < 5; i++) {
        const file = {
          originalname: `test-${i}.txt`,
          mimetype: 'text/plain',
          size: 200,
          buffer: Buffer.from(`Test document ${i} with some content for memory testing.`)
        } as Express.Multer.File;

        promises.push(documentService.processDocument(file, testUserId + '-memory'));
      }

      await Promise.all(promises);
      
      // Check memory usage hasn't grown excessively
      const finalMemory = process.memoryUsage();
      const memoryGrowth = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // Memory growth should be reasonable (less than 100MB for 5 small documents)
      expect(memoryGrowth).toBeLessThan(100 * 1024 * 1024);
    }, 60000); // 60 second timeout
  });

  describe('Performance Benchmarks', () => {
    test('should process documents within acceptable time limits', async () => {
      const documentService = getDocumentService();
      
      const file = {
        originalname: 'performance-test.txt',
        mimetype: 'text/plain',
        size: 1000,
        buffer: Buffer.from('Performance test document. '.repeat(50))
      } as Express.Multer.File;

      const startTime = Date.now();
      const result = await documentService.processDocument(file, testUserId + '-perf');
      const processingTime = Date.now() - startTime;

      expect(result.status).toBe('queued');
      expect(processingTime).toBeLessThan(5000); // Should queue within 5 seconds
    });

    test('should perform vector search within acceptable time limits', async () => {
      const startTime = Date.now();
      
      const { data, error } = await supabase.rpc('search_document_chunks', {
        query_embedding: new Array(1536).fill(0.1),
        similarity_threshold: 0.1,
        match_count: 10,
        user_id_filter: testUserId,
        document_ids_filter: null
      });

      const searchTime = Date.now() - startTime;

      expect(error).toBeNull();
      expect(searchTime).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });
});
