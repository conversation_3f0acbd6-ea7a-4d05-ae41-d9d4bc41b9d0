import request from 'supertest'
import express from 'express'
import assistRouter from '../../api/v1/assist'
import { LLMOrchestrationService } from '../../services/llmOrchestrationService'
import { authMiddleware } from '../../authMiddleware'

// Create test app
const app = express()
app.use(express.json())

// Mock auth middleware to always pass
jest.mock('../../authMiddleware', () => ({
  authMiddleware: (req: any, res: any, next: any) => {
    req.user = { id: 'test-user-123' }
    next()
  }
}))

app.use('/api/v1/assist', assistRouter)

// Mock LLM service
jest.mock('../../services/llmOrchestrationService')
const mockLLMService = LLMOrchestrationService as jest.Mocked<typeof LLMOrchestrationService>

describe('Assist API', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST /api/v1/assist/realtime', () => {
    const validRequest = {
      context: {
        currentTranscriptSegment: 'Customer: Your price seems too high',
        prospectName: '<PERSON>',
        companyName: 'Acme Corp',
        dealStage: 'negotiation'
      },
      assistanceType: 'objection',
      query: 'They think our price is too high'
    }

    it('should return AI assistance successfully', async () => {
      mockLLMService.generateText.mockResolvedValue({
        success: true,
        text: 'Here are some strategies to handle price objections:\n1. Focus on value\n2. Break down costs',
        usage: { promptTokens: 100, candidatesTokens: 50, totalTokens: 150 }
      })

      const response = await request(app)
        .post('/api/v1/assist/realtime')
        .set('x-request-id', 'test-request-123')
        .send(validRequest)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.assistanceType).toBe('objection')
      expect(response.body.suggestions).toBeInstanceOf(Array)
      expect(response.body.suggestions.length).toBeGreaterThan(0)
      expect(response.body).toHaveProperty('usage')
      expect(response.body).toHaveProperty('performance')
    })

    it('should return 400 for missing context', async () => {
      const invalidRequest = {
        assistanceType: 'objection',
        query: 'Test query'
      }

      const response = await request(app)
        .post('/api/v1/assist/realtime')
        .send(invalidRequest)

      expect(response.status).toBe(400)
      expect(response.body.error).toContain('Context is required')
    })

    it('should return 400 for missing assistance type', async () => {
      const invalidRequest = {
        context: validRequest.context,
        query: 'Test query'
      }

      const response = await request(app)
        .post('/api/v1/assist/realtime')
        .send(invalidRequest)

      expect(response.status).toBe(400)
      expect(response.body.error).toContain('Assistance type is required')
    })

    it('should return 400 for invalid assistance type', async () => {
      const invalidRequest = {
        context: validRequest.context,
        assistanceType: 'invalid_type',
        query: 'Test query'
      }

      const response = await request(app)
        .post('/api/v1/assist/realtime')
        .send(invalidRequest)

      expect(response.status).toBe(400)
      expect(response.body.error).toContain('Invalid assistance type')
    })

    it('should return 400 for missing query when required', async () => {
      const invalidRequest = {
        context: validRequest.context,
        assistanceType: 'objection'
        // Missing query
      }

      const response = await request(app)
        .post('/api/v1/assist/realtime')
        .send(invalidRequest)

      expect(response.status).toBe(400)
      expect(response.body.error).toContain('Query is required for assistance type: objection')
    })

    it('should handle LLM service errors', async () => {
      mockLLMService.generateText.mockResolvedValue({
        success: false,
        error: 'LLM service unavailable'
      })

      const response = await request(app)
        .post('/api/v1/assist/realtime')
        .send(validRequest)

      expect(response.status).toBe(500)
      expect(response.body.error).toContain('LLM service unavailable')
    })

    it('should work with general assistance without query', async () => {
      const generalRequest = {
        context: validRequest.context,
        assistanceType: 'general_assistance'
        // No query required for general assistance
      }

      mockLLMService.generateText.mockResolvedValue({
        success: true,
        text: 'Based on the conversation context, here are some suggestions...',
        usage: { promptTokens: 80, candidatesTokens: 40, totalTokens: 120 }
      })

      const response = await request(app)
        .post('/api/v1/assist/realtime')
        .send(generalRequest)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.assistanceType).toBe('general_assistance')
    })
  })

  describe('POST /api/v1/assist/stream', () => {
    const validStreamRequest = {
      context: {
        currentTranscriptSegment: 'Customer: Tell me about your product',
        prospectName: 'Jane Smith',
        companyName: 'Tech Corp'
      },
      assistanceType: 'product_info',
      query: 'They want to know about our main features',
      stream: true
    }

    it('should return streaming response', async () => {
      // Mock streaming generator
      async function* mockStreamGenerator() {
        yield { text: 'Our product offers ', done: false }
        yield { text: 'advanced features ', done: false }
        yield { text: 'for sales teams.', done: false }
        yield { text: '', done: true }
      }

      mockLLMService.generateTextStream.mockResolvedValue(mockStreamGenerator())

      const response = await request(app)
        .post('/api/v1/assist/stream')
        .send(validStreamRequest)

      expect(response.status).toBe(200)
      expect(response.headers['content-type']).toContain('text/plain')
      expect(response.headers['transfer-encoding']).toBe('chunked')
    })

    it('should return 400 for missing required fields', async () => {
      const invalidRequest = {
        assistanceType: 'product_info',
        stream: true
        // Missing context
      }

      const response = await request(app)
        .post('/api/v1/assist/stream')
        .send(invalidRequest)

      expect(response.status).toBe(400)
      expect(response.body.error).toContain('Missing required fields')
    })
  })

  describe('POST /api/v1/assist/multimodal', () => {
    const validMultimodalRequest = {
      context: {
        currentTranscriptSegment: 'Customer: Can you show me how this works?',
        prospectName: 'Bob Johnson'
      },
      assistanceType: 'product_info',
      query: 'They want a demo',
      imageData: 'base64-encoded-image-data',
      imageMimeType: 'image/png'
    }

    it('should handle multimodal input successfully', async () => {
      mockLLMService.generateMultimodalContent.mockResolvedValue({
        success: true,
        text: 'Based on the screen content, I can see you are showing the dashboard. Here are key points to highlight...',
        usage: { promptTokens: 150, candidatesTokens: 75, totalTokens: 225 }
      })

      const response = await request(app)
        .post('/api/v1/assist/multimodal')
        .set('x-request-id', 'test-multimodal-123')
        .send(validMultimodalRequest)

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.inputTypes.hasImage).toBe(true)
      expect(response.body.inputTypes.hasText).toBe(true)
      expect(response.body.inputTypes.hasAudio).toBe(false)
    })

    it('should handle audio and image together', async () => {
      const audioImageRequest = {
        ...validMultimodalRequest,
        audioData: 'base64-encoded-audio-data',
        audioMimeType: 'audio/wav'
      }

      mockLLMService.generateMultimodalContent.mockResolvedValue({
        success: true,
        text: 'Based on the audio and visual context...',
        usage: { promptTokens: 200, candidatesTokens: 100, totalTokens: 300 }
      })

      const response = await request(app)
        .post('/api/v1/assist/multimodal')
        .send(audioImageRequest)

      expect(response.status).toBe(200)
      expect(response.body.inputTypes.hasImage).toBe(true)
      expect(response.body.inputTypes.hasAudio).toBe(true)
    })

    it('should return 400 for missing context', async () => {
      const invalidRequest = {
        assistanceType: 'product_info',
        imageData: 'base64-data'
      }

      const response = await request(app)
        .post('/api/v1/assist/multimodal')
        .send(invalidRequest)

      expect(response.status).toBe(400)
      expect(response.body.error).toContain('Missing required fields')
    })
  })

  describe('GET /api/v1/assist/health', () => {
    it('should return healthy status when LLM is connected', async () => {
      mockLLMService.validateConnection.mockResolvedValue({
        success: true
      })

      const response = await request(app)
        .get('/api/v1/assist/health')

      expect(response.status).toBe(200)
      expect(response.body.status).toBe('healthy')
      expect(response.body.service).toBe('assist')
      expect(response.body.llm).toBe('connected')
      expect(response.body).toHaveProperty('timestamp')
    })

    it('should return error when LLM is not connected', async () => {
      mockLLMService.validateConnection.mockResolvedValue({
        success: false,
        error: 'API key invalid'
      })

      const response = await request(app)
        .get('/api/v1/assist/health')

      expect(response.status).toBe(500)
      expect(response.body.error).toContain('API key invalid')
    })
  })
})
