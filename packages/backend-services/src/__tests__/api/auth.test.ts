import request from 'supertest'
import express from 'express'
import authRouter from '../../api/v1/auth'
import { supabase } from '../../supabaseClient'

// Create test app
const app = express()
app.use(express.json())
app.use('/api/v1/auth', authRouter)

// Mock Supabase responses
const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('Authentication API', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('POST /api/v1/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        username: 'testuser'
      }

      mockSupabase.auth.signUp.mockResolvedValue({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            user_metadata: { username: 'testuser' }
          },
          session: {
            access_token: 'access-token',
            refresh_token: 'refresh-token'
          }
        },
        error: null
      } as any)

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockResolvedValue({
            data: [{ id: 'user-123', email: '<EMAIL>', username: 'testuser' }],
            error: null
          })
        })
      } as any)

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty('access_token')
      expect(response.body).toHaveProperty('refresh_token')
      expect(response.body).toHaveProperty('user')
      expect(response.body.user.email).toBe(userData.email)
    })

    it('should return 400 for missing email', async () => {
      const userData = {
        password: 'password123'
      }

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)

      expect(response.status).toBe(400)
      expect(response.body.error).toBe('Email and password required')
    })

    it('should return 400 for invalid email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'password123'
      }

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)

      expect(response.status).toBe(400)
      expect(response.body.error).toBe('Invalid email format')
    })

    it('should return 400 for weak password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123'
      }

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)

      expect(response.status).toBe(400)
      expect(response.body.error).toBe('Password must be at least 8 characters')
    })

    it('should handle Supabase registration errors', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'User already registered' }
      } as any)

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)

      expect(response.status).toBe(400)
      expect(response.body.error).toBe('User already registered')
    })
  })

  describe('POST /api/v1/auth/login', () => {
    it('should login user successfully', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          },
          session: {
            access_token: 'access-token',
            refresh_token: 'refresh-token'
          }
        },
        error: null
      } as any)

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('access_token')
      expect(response.body).toHaveProperty('refresh_token')
    })

    it('should return 400 for missing credentials', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({})

      expect(response.status).toBe(400)
      expect(response.body.error).toBe('Email and password required')
    })

    it('should return 401 for invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid login credentials' }
      } as any)

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Invalid login credentials')
    })
  })

  describe('POST /api/v1/auth/refresh-token', () => {
    it('should refresh token successfully', async () => {
      const refreshData = {
        refresh_token: 'valid-refresh-token'
      }

      mockSupabase.auth.refreshSession.mockResolvedValue({
        data: {
          session: {
            access_token: 'new-access-token',
            refresh_token: 'new-refresh-token'
          }
        },
        error: null
      } as any)

      const response = await request(app)
        .post('/api/v1/auth/refresh-token')
        .send(refreshData)

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('access_token')
      expect(response.body).toHaveProperty('refresh_token')
    })

    it('should return 400 for missing refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh-token')
        .send({})

      expect(response.status).toBe(400)
      expect(response.body.error).toBe('Refresh token required')
    })

    it('should return 401 for invalid refresh token', async () => {
      const refreshData = {
        refresh_token: 'invalid-refresh-token'
      }

      mockSupabase.auth.refreshSession.mockResolvedValue({
        data: { session: null },
        error: { message: 'Invalid refresh token' }
      } as any)

      const response = await request(app)
        .post('/api/v1/auth/refresh-token')
        .send(refreshData)

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Invalid refresh token')
    })
  })

  describe('GET /api/v1/auth/me', () => {
    it('should return user profile with valid token', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: {
          user: {
            id: 'user-123',
            email: '<EMAIL>'
          }
        },
        error: null
      } as any)

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: {
                username: 'testuser',
                full_name: 'Test User',
                profile_picture_url: 'https://example.com/avatar.jpg',
                subscription_status: 'free'
              },
              error: null
            })
          })
        })
      } as any)

      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer valid-token')

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('user')
      expect(response.body.user.email).toBe('<EMAIL>')
      expect(response.body.user.username).toBe('testuser')
    })

    it('should return 401 for missing authorization header', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Missing or invalid Authorization header')
    })

    it('should return 401 for invalid token format', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'InvalidToken')

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Missing or invalid Authorization header')
    })

    it('should return 401 for expired token', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'JWT expired' }
      } as any)

      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer expired-token')

      expect(response.status).toBe(401)
      expect(response.body.error).toBe('Invalid or expired token')
    })
  })
})
