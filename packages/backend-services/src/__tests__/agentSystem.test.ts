import { AgentStateService } from '../services/agentStateService';
import { MemoryService } from '../services/memoryService';
import { ToolOrchestrator } from '../services/toolOrchestrator';
import { EnhancedLLMOrchestrationService } from '../services/enhancedLLMOrchestrationService';

// Mock environment variables
process.env.SUPABASE_URL = 'http://localhost:54321';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-key';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.OPENAI_API_KEY = 'test-key';

describe('Agent System Integration Tests', () => {
  let agentStateService: AgentStateService;
  let memoryService: MemoryService;
  let toolOrchestrator: ToolOrchestrator;
  let enhancedLLMService: EnhancedLLMOrchestrationService;

  const testConversationId = 'test-conversation-123';
  const testUserId = 'test-user-456';
  const testContext = {
    prospectName: '<PERSON>',
    companyName: 'Acme Corp',
    dealStage: 'discovery'
  };

  beforeAll(async () => {
    // Initialize services
    agentStateService = new AgentStateService();
    memoryService = new MemoryService();
    toolOrchestrator = new ToolOrchestrator();
    enhancedLLMService = new EnhancedLLMOrchestrationService();
  });

  afterAll(async () => {
    // Cleanup test data
    try {
      await agentStateService.deleteAgentState(testConversationId);
      await memoryService.clearMemories(testConversationId);
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('AgentStateService', () => {
    test('should create new agent state', async () => {
      const state = await agentStateService.createNewAgentState(
        testConversationId,
        testUserId,
        testContext
      );

      expect(state).toBeDefined();
      expect(state.conversationId).toBe(testConversationId);
      expect(state.userId).toBe(testUserId);
      expect(state.context).toEqual(testContext);
      expect(state.memory.shortTerm).toEqual([]);
      expect(state.memory.workingMemory).toEqual([]);
    });

    test('should retrieve existing agent state', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      
      expect(state).toBeDefined();
      expect(state!.conversationId).toBe(testConversationId);
      expect(state!.userId).toBe(testUserId);
    });

    test('should update agent state', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      state!.currentGoal = 'Test goal';
      await agentStateService.updateAgentState(state!);

      const updatedState = await agentStateService.getAgentState(testConversationId);
      expect(updatedState!.currentGoal).toBe('Test goal');
    });

    test('should perform health check', async () => {
      const health = await agentStateService.healthCheck();
      
      expect(health).toBeDefined();
      expect(typeof health.redis).toBe('boolean');
      expect(typeof health.database).toBe('boolean');
    });
  });

  describe('MemoryService', () => {
    test('should add memory to agent state', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      await memoryService.addMemory(
        state!,
        'Test memory content',
        'conversation',
        5
      );

      expect(state!.memory.shortTerm.length).toBeGreaterThan(0);
      const lastMemory = state!.memory.shortTerm[state!.memory.shortTerm.length - 1];
      expect(lastMemory.content).toContain('Test memory content');
    });

    test('should add to working memory', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      await memoryService.addToWorkingMemory(state!, { test: 'data' });

      expect(state!.memory.workingMemory.length).toBeGreaterThan(0);
      const workingItem = state!.memory.workingMemory[state!.memory.workingMemory.length - 1];
      expect(workingItem.content).toContain('test');
    });

    test('should build memory context', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      const context = await memoryService.getMemoryContext(state!, 'test query');
      
      expect(typeof context).toBe('string');
      // Context should include recent memories if any exist
      if (state!.memory.shortTerm.length > 0) {
        expect(context.length).toBeGreaterThan(0);
      }
    });

    test('should update user preferences', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      const preferences = { communicationStyle: 'direct', industryFocus: 'tech' };
      await memoryService.updateUserPreferences(state!, preferences);

      expect(state!.memory.userPreferences).toEqual(preferences);
    });
  });

  describe('ToolOrchestrator', () => {
    test('should get available tools', () => {
      const tools = toolOrchestrator.getAvailableTools();
      
      expect(Array.isArray(tools)).toBe(true);
      expect(tools.length).toBeGreaterThan(0);
      
      // Check for default tools
      const toolNames = tools.map(t => t.name);
      expect(toolNames).toContain('search_knowledge');
      expect(toolNames).toContain('search_crm');
    });

    test('should get tools by category', () => {
      const knowledgeTools = toolOrchestrator.getAvailableTools('knowledge');
      
      expect(Array.isArray(knowledgeTools)).toBe(true);
      knowledgeTools.forEach(tool => {
        expect(tool.category).toBe('knowledge');
      });
    });

    test('should execute search_knowledge tool', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      const result = await toolOrchestrator.executeTool(
        'search_knowledge',
        { query: 'test query', limit: 3 },
        {
          agentState: state!,
          userId: testUserId,
          conversationId: testConversationId
        }
      );

      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
      expect(result.executionTime).toBeGreaterThan(0);
    });

    test('should validate tool parameters', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      // Test with missing required parameter
      const result = await toolOrchestrator.executeTool(
        'search_knowledge',
        { limit: 3 }, // missing required 'query' parameter
        {
          agentState: state!,
          userId: testUserId,
          conversationId: testConversationId
        }
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Missing required parameter');
    });

    test('should handle invalid tool name', async () => {
      const state = await agentStateService.getAgentState(testConversationId);
      expect(state).toBeDefined();

      const result = await toolOrchestrator.executeTool(
        'nonexistent_tool',
        {},
        {
          agentState: state!,
          userId: testUserId,
          conversationId: testConversationId
        }
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });
  });

  describe('EnhancedLLMOrchestrationService', () => {
    test('should perform health check', async () => {
      const health = await enhancedLLMService.healthCheck();
      
      expect(health).toBeDefined();
      expect(health.agent).toBeDefined();
      expect(health.llm).toBeDefined();
      expect(typeof health.overall).toBe('boolean');
    });

    test('should get agent state', async () => {
      const state = await enhancedLLMService.getAgentState(testConversationId);
      
      expect(state).toBeDefined();
      expect(state!.conversationId).toBe(testConversationId);
    });

    test('should update agent goal', async () => {
      const newGoal = 'Close the deal with Acme Corp';
      await enhancedLLMService.updateAgentGoal(testConversationId, newGoal);

      const state = await enhancedLLMService.getAgentState(testConversationId);
      expect(state!.currentGoal).toBe(newGoal);
    });

    test('should get available tools', () => {
      const tools = enhancedLLMService.getAvailableTools(testUserId);
      
      expect(Array.isArray(tools)).toBe(true);
      expect(tools.length).toBeGreaterThan(0);
    });

    // Note: Full generation tests would require actual API keys and connections
    // These would be integration tests rather than unit tests
    test('should generate without agent (fallback mode)', async () => {
      const options = {
        prompt: 'Hello, this is a test',
        conversationId: testConversationId,
        userId: testUserId,
        context: testContext,
        useAgent: false,
        temperature: 0.7,
        maxOutputTokens: 100
      };

      // This test would require mocking the LLM service or actual API keys
      // For now, we'll just test that the method exists and can be called
      expect(typeof enhancedLLMService.generateWithAgent).toBe('function');
    });
  });

  describe('Integration Tests', () => {
    test('should maintain state consistency across services', async () => {
      // Create initial state
      const initialState = await agentStateService.createNewAgentState(
        'integration-test-conv',
        testUserId,
        testContext
      );

      // Add memory
      await memoryService.addMemory(
        initialState,
        'Integration test memory',
        'conversation',
        7
      );

      // Update state
      await agentStateService.updateAgentState(initialState);

      // Retrieve state and verify consistency
      const retrievedState = await agentStateService.getAgentState('integration-test-conv');
      
      expect(retrievedState).toBeDefined();
      expect(retrievedState!.memory.shortTerm.length).toBeGreaterThan(0);
      expect(retrievedState!.memory.shortTerm.some(m => 
        m.content.includes('Integration test memory')
      )).toBe(true);

      // Cleanup
      await agentStateService.deleteAgentState('integration-test-conv');
    });

    test('should handle concurrent operations safely', async () => {
      const conversationId = 'concurrent-test-conv';
      
      // Create state
      const state = await agentStateService.createNewAgentState(
        conversationId,
        testUserId,
        testContext
      );

      // Perform concurrent memory operations
      const memoryPromises = [
        memoryService.addMemory(state, 'Memory 1', 'conversation', 5),
        memoryService.addMemory(state, 'Memory 2', 'conversation', 6),
        memoryService.addMemory(state, 'Memory 3', 'conversation', 7)
      ];

      await Promise.all(memoryPromises);

      // Verify all memories were added
      expect(state.memory.shortTerm.length).toBeGreaterThanOrEqual(3);

      // Cleanup
      await agentStateService.deleteAgentState(conversationId);
    });
  });
});

// Helper function to create mock context
function createMockContext() {
  return {
    prospectName: 'Test Prospect',
    companyName: 'Test Company',
    dealStage: 'discovery',
    currentTranscriptSegment: 'Test conversation segment'
  };
}

// Helper function to wait for async operations
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
