import { supabase } from '../../supabaseClient'
import { createClient } from '@supabase/supabase-js'
import {
  setupTestDatabase,
  teardownTestDatabase,
  generateTestUser,
  generateTestCallTranscript,
  generateTestDocument,
  cleanupTestData,
  TEST_DB_CONFIG
} from './setup'

describe('Row Level Security (RLS) Tests', () => {
  let user1: any
  let user2: any
  let user1Client: any
  let user2Client: any

  beforeAll(async () => {
    await setupTestDatabase()

    // Create test users
    user1 = generateTestUser({ email: '<EMAIL>', username: 'rlsuser1' })
    user2 = generateTestUser({ email: '<EMAIL>', username: 'rlsuser2' })

    await supabase.from('users').insert([user1, user2])

    // Create authenticated clients for each user
    user1Client = createClient(TEST_DB_CONFIG.url, TEST_DB_CONFIG.anonKey)
    user2Client = createClient(TEST_DB_CONFIG.url, TEST_DB_CONFIG.anonKey)

    // Mock authentication for each client
    // Note: In real tests, you would use actual JWT tokens
    user1Client.auth.getUser = jest.fn().mockResolvedValue({
      data: { user: { id: user1.id } },
      error: null
    })

    user2Client.auth.getUser = jest.fn().mockResolvedValue({
      data: { user: { id: user2.id } },
      error: null
    })
  })

  afterAll(async () => {
    await teardownTestDatabase()
  })

  beforeEach(async () => {
    // Clean up test data but keep users
    await supabase.from('call_summaries').delete().neq('summary_id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('document_chunks').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('documents').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('call_transcripts').delete().neq('transcript_id', '00000000-0000-0000-0000-000000000000')
  })

  describe('Users Table RLS', () => {
    it('should allow users to view their own data only', async () => {
      // User 1 should see only their own data
      const { data: user1Data, error: user1Error } = await user1Client
        .from('users')
        .select('*')
        .eq('id', user1.id)

      expect(user1Error).toBeNull()
      expect(user1Data?.length).toBe(1)
      expect(user1Data?.[0]?.id).toBe(user1.id)

      // User 1 should not see user 2's data
      const { data: user2DataFromUser1, error: user2ErrorFromUser1 } = await user1Client
        .from('users')
        .select('*')
        .eq('id', user2.id)

      expect(user2ErrorFromUser1).toBeNull()
      expect(user2DataFromUser1?.length).toBe(0)
    })

    it('should allow users to update their own data only', async () => {
      // User 1 should be able to update their own data
      const { data, error } = await user1Client
        .from('users')
        .update({ full_name: 'Updated Name' })
        .eq('id', user1.id)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.full_name).toBe('Updated Name')

      // User 1 should not be able to update user 2's data
      const { data: updateData, error: updateError } = await user1Client
        .from('users')
        .update({ full_name: 'Hacked Name' })
        .eq('id', user2.id)
        .select()

      expect(updateError).not.toBeNull()
      expect(updateData?.length).toBe(0)
    })
  })

  describe('User Profiles Table RLS', () => {
    it('should allow users to view their own profile only', async () => {
      // User 1 should see their own profile
      const { data: profile1Data, error: profile1Error } = await user1Client
        .from('user_profiles')
        .select('*')
        .eq('user_id', user1.id)

      expect(profile1Error).toBeNull()
      expect(profile1Data?.length).toBe(1)
      expect(profile1Data?.[0]?.user_id).toBe(user1.id)

      // User 1 should not see user 2's profile
      const { data: profile2Data, error: profile2Error } = await user1Client
        .from('user_profiles')
        .select('*')
        .eq('user_id', user2.id)

      expect(profile2Error).toBeNull()
      expect(profile2Data?.length).toBe(0)
    })

    it('should allow users to update their own profile only', async () => {
      // User 1 should be able to update their own profile
      const { data, error } = await user1Client
        .from('user_profiles')
        .update({ job_title: 'Senior Developer' })
        .eq('user_id', user1.id)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.job_title).toBe('Senior Developer')

      // User 1 should not be able to update user 2's profile
      const { data: updateData, error: updateError } = await user1Client
        .from('user_profiles')
        .update({ job_title: 'Hacked Title' })
        .eq('user_id', user2.id)
        .select()

      expect(updateError).not.toBeNull()
      expect(updateData?.length).toBe(0)
    })
  })

  describe('Call Transcripts Table RLS', () => {
    beforeEach(async () => {
      // Create test transcripts for both users
      const transcript1 = generateTestCallTranscript(user1.id)
      const transcript2 = generateTestCallTranscript(user2.id)

      await supabase.from('call_transcripts').insert([transcript1, transcript2])
    })

    it('should allow users to view their own transcripts only', async () => {
      // User 1 should see only their own transcripts
      const { data: user1Transcripts, error: user1Error } = await user1Client
        .from('call_transcripts')
        .select('*')
        .eq('user_id', user1.id)

      expect(user1Error).toBeNull()
      expect(user1Transcripts?.length).toBe(1)
      expect(user1Transcripts?.[0]?.user_id).toBe(user1.id)

      // User 1 should not see user 2's transcripts
      const { data: user2Transcripts, error: user2Error } = await user1Client
        .from('call_transcripts')
        .select('*')
        .eq('user_id', user2.id)

      expect(user2Error).toBeNull()
      expect(user2Transcripts?.length).toBe(0)
    })

    it('should allow users to create transcripts for themselves only', async () => {
      const newTranscript = generateTestCallTranscript(user1.id)

      // User 1 should be able to create their own transcript
      const { data, error } = await user1Client
        .from('call_transcripts')
        .insert(newTranscript)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.user_id).toBe(user1.id)

      // User 1 should not be able to create transcript for user 2
      const maliciousTranscript = generateTestCallTranscript(user2.id)

      const { data: maliciousData, error: maliciousError } = await user1Client
        .from('call_transcripts')
        .insert(maliciousTranscript)
        .select()

      expect(maliciousError).not.toBeNull()
      expect(maliciousData?.length).toBe(0)
    })

    it('should allow users to update their own transcripts only', async () => {
      // Get user 1's transcript
      const { data: transcripts } = await supabase
        .from('call_transcripts')
        .select('*')
        .eq('user_id', user1.id)
        .limit(1)

      const transcriptId = transcripts?.[0]?.transcript_id

      // User 1 should be able to update their own transcript
      const { data, error } = await user1Client
        .from('call_transcripts')
        .update({ full_transcript: 'Updated transcript content' })
        .eq('transcript_id', transcriptId)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.full_transcript).toBe('Updated transcript content')

      // Get user 2's transcript
      const { data: user2Transcripts } = await supabase
        .from('call_transcripts')
        .select('*')
        .eq('user_id', user2.id)
        .limit(1)

      const user2TranscriptId = user2Transcripts?.[0]?.transcript_id

      // User 1 should not be able to update user 2's transcript
      const { data: updateData, error: updateError } = await user1Client
        .from('call_transcripts')
        .update({ full_transcript: 'Hacked content' })
        .eq('transcript_id', user2TranscriptId)
        .select()

      expect(updateError).not.toBeNull()
      expect(updateData?.length).toBe(0)
    })
  })

  describe('Documents Table RLS', () => {
    beforeEach(async () => {
      // Create test documents for both users
      const document1 = generateTestDocument(user1.id)
      const document2 = generateTestDocument(user2.id)

      await supabase.from('documents').insert([document1, document2])
    })

    it('should allow users to view their own documents only', async () => {
      // User 1 should see only their own documents
      const { data: user1Documents, error: user1Error } = await user1Client
        .from('documents')
        .select('*')
        .eq('user_id', user1.id)

      expect(user1Error).toBeNull()
      expect(user1Documents?.length).toBe(1)
      expect(user1Documents?.[0]?.user_id).toBe(user1.id)

      // User 1 should not see user 2's documents
      const { data: user2Documents, error: user2Error } = await user1Client
        .from('documents')
        .select('*')
        .eq('user_id', user2.id)

      expect(user2Error).toBeNull()
      expect(user2Documents?.length).toBe(0)
    })

    it('should allow users to create documents for themselves only', async () => {
      const newDocument = generateTestDocument(user1.id)

      // User 1 should be able to create their own document
      const { data, error } = await user1Client
        .from('documents')
        .insert(newDocument)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.user_id).toBe(user1.id)

      // User 1 should not be able to create document for user 2
      const maliciousDocument = generateTestDocument(user2.id)

      const { data: maliciousData, error: maliciousError } = await user1Client
        .from('documents')
        .insert(maliciousDocument)
        .select()

      expect(maliciousError).not.toBeNull()
      expect(maliciousData?.length).toBe(0)
    })
  })

  describe('Document Chunks Table RLS', () => {
    let user1DocumentId: string
    let user2DocumentId: string

    beforeEach(async () => {
      // Create documents for both users
      const document1 = generateTestDocument(user1.id)
      const document2 = generateTestDocument(user2.id)

      const { data: docData } = await supabase
        .from('documents')
        .insert([document1, document2])
        .select()

      user1DocumentId = docData?.find(doc => doc.user_id === user1.id)?.id
      user2DocumentId = docData?.find(doc => doc.user_id === user2.id)?.id
    })

    it('should allow users to view chunks from their own documents only', async () => {
      // Create chunks for both documents
      const chunk1 = {
        document_id: user1DocumentId,
        content: 'User 1 document chunk',
        metadata: { page: 1 },
        embedding: Array.from({ length: 1536 }, () => Math.random() - 0.5)
      }

      const chunk2 = {
        document_id: user2DocumentId,
        content: 'User 2 document chunk',
        metadata: { page: 1 },
        embedding: Array.from({ length: 1536 }, () => Math.random() - 0.5)
      }

      await supabase.from('document_chunks').insert([chunk1, chunk2])

      // User 1 should see only chunks from their own documents
      const { data: user1Chunks, error: user1Error } = await user1Client
        .from('document_chunks')
        .select('*')
        .eq('document_id', user1DocumentId)

      expect(user1Error).toBeNull()
      expect(user1Chunks?.length).toBe(1)
      expect(user1Chunks?.[0]?.content).toBe('User 1 document chunk')

      // User 1 should not see chunks from user 2's documents
      const { data: user2Chunks, error: user2Error } = await user1Client
        .from('document_chunks')
        .select('*')
        .eq('document_id', user2DocumentId)

      expect(user2Error).toBeNull()
      expect(user2Chunks?.length).toBe(0)
    })
  })

  describe('Plans Table RLS', () => {
    it('should allow all authenticated users to view plans', async () => {
      // Both users should be able to view plans
      const { data: user1Plans, error: user1Error } = await user1Client
        .from('plans')
        .select('*')

      const { data: user2Plans, error: user2Error } = await user2Client
        .from('plans')
        .select('*')

      expect(user1Error).toBeNull()
      expect(user2Error).toBeNull()
      expect(user1Plans?.length).toBeGreaterThan(0)
      expect(user2Plans?.length).toBeGreaterThan(0)
      expect(user1Plans?.length).toBe(user2Plans?.length)
    })

    it('should not allow users to modify plans', async () => {
      // Get a plan
      const { data: plans } = await supabase.from('plans').select('*').limit(1)
      const planId = plans?.[0]?.plan_id

      // Users should not be able to update plans
      const { data, error } = await user1Client
        .from('plans')
        .update({ name: 'Hacked Plan' })
        .eq('plan_id', planId)
        .select()

      expect(error).not.toBeNull()
      expect(data?.length).toBe(0)
    })
  })
})
