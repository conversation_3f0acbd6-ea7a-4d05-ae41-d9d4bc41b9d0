import { supabase } from '../../supabaseClient'
import {
  setupTestDatabase,
  teardownTestDatabase,
  generateTestUser,
  generateTestDocument,
  generateRandomVector,
  testVectorSimilarity,
  cleanupTestData
} from './setup'

describe('Vector Search Tests', () => {
  let testUser: any
  let testDocuments: any[]

  beforeAll(async () => {
    await setupTestDatabase()

    // Create test user
    testUser = generateTestUser()
    await supabase.from('users').insert(testUser)

    // Create test documents
    testDocuments = [
      generateTestDocument(testUser.id, { title: 'Sales Playbook', description: 'Comprehensive sales strategies' }),
      generateTestDocument(testUser.id, { title: 'Product Manual', description: 'Technical product documentation' }),
      generateTestDocument(testUser.id, { title: 'Customer Stories', description: 'Success stories and case studies' })
    ]

    const { data: docData } = await supabase.from('documents').insert(testDocuments).select()
    testDocuments = docData || []
  })

  afterAll(async () => {
    await teardownTestDatabase()
  })

  beforeEach(async () => {
    // Clean up document chunks before each test
    await supabase.from('document_chunks').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  })

  describe('Vector Embedding Storage', () => {
    it('should store vector embeddings correctly', async () => {
      const testChunk = {
        document_id: testDocuments[0].id,
        content: 'This is a test chunk about sales strategies and customer engagement.',
        metadata: { page: 1, section: 'introduction' },
        embedding: generateRandomVector(1536)
      }

      const { data, error } = await supabase
        .from('document_chunks')
        .insert(testChunk)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.embedding).toBeDefined()
      expect(Array.isArray(data?.[0]?.embedding)).toBe(true)
      expect(data?.[0]?.embedding.length).toBe(1536)
    })

    it('should handle different vector dimensions', async () => {
      // Test with smaller vector (should fail or be padded)
      const smallVector = generateRandomVector(512)
      
      const testChunk = {
        document_id: testDocuments[0].id,
        content: 'Test content with smaller vector',
        metadata: { page: 1 },
        embedding: smallVector
      }

      const { error } = await supabase
        .from('document_chunks')
        .insert(testChunk)

      // Should fail because vector dimension doesn't match
      expect(error).not.toBeNull()
    })

    it('should validate vector data types', async () => {
      const invalidChunk = {
        document_id: testDocuments[0].id,
        content: 'Test content with invalid vector',
        metadata: { page: 1 },
        embedding: 'invalid-vector-data'
      }

      const { error } = await supabase
        .from('document_chunks')
        .insert(invalidChunk)

      expect(error).not.toBeNull()
    })
  })

  describe('Vector Similarity Search', () => {
    beforeEach(async () => {
      // Create test chunks with known vectors for similarity testing
      const testChunks = [
        {
          document_id: testDocuments[0].id,
          content: 'Sales strategies for closing deals and handling objections.',
          metadata: { page: 1, topic: 'sales' },
          embedding: generateRandomVector(1536)
        },
        {
          document_id: testDocuments[1].id,
          content: 'Technical specifications and product features overview.',
          metadata: { page: 1, topic: 'technical' },
          embedding: generateRandomVector(1536)
        },
        {
          document_id: testDocuments[2].id,
          content: 'Customer success stories and testimonials from satisfied clients.',
          metadata: { page: 1, topic: 'customer-stories' },
          embedding: generateRandomVector(1536)
        }
      ]

      await supabase.from('document_chunks').insert(testChunks)
    })

    it('should perform vector similarity search using match_documents function', async () => {
      const queryVector = generateRandomVector(1536)

      const { data, error } = await testVectorSimilarity(queryVector, 0.0) // Low threshold to get results

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)
      expect(data?.length).toBeGreaterThan(0)

      // Check result structure
      if (data && data.length > 0) {
        expect(data[0]).toHaveProperty('id')
        expect(data[0]).toHaveProperty('content')
        expect(data[0]).toHaveProperty('metadata')
        expect(data[0]).toHaveProperty('similarity')
        expect(typeof data[0].similarity).toBe('number')
      }
    })

    it('should return results ordered by similarity', async () => {
      const queryVector = generateRandomVector(1536)

      const { data, error } = await testVectorSimilarity(queryVector, 0.0)

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)

      if (data && data.length > 1) {
        // Results should be ordered by similarity (highest first)
        for (let i = 1; i < data.length; i++) {
          expect(data[i-1].similarity).toBeGreaterThanOrEqual(data[i].similarity)
        }
      }
    })

    it('should respect similarity threshold', async () => {
      const queryVector = generateRandomVector(1536)

      // High threshold should return fewer or no results
      const { data: highThresholdData } = await testVectorSimilarity(queryVector, 0.9)
      
      // Low threshold should return more results
      const { data: lowThresholdData } = await testVectorSimilarity(queryVector, 0.1)

      expect(Array.isArray(highThresholdData)).toBe(true)
      expect(Array.isArray(lowThresholdData)).toBe(true)
      expect(lowThresholdData?.length || 0).toBeGreaterThanOrEqual(highThresholdData?.length || 0)
    })

    it('should respect match count limit', async () => {
      const queryVector = generateRandomVector(1536)

      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: queryVector,
        match_threshold: 0.0,
        match_count: 2
      })

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)
      expect(data?.length || 0).toBeLessThanOrEqual(2)
    })

    it('should handle empty results gracefully', async () => {
      const queryVector = generateRandomVector(1536)

      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: queryVector,
        match_threshold: 0.99, // Very high threshold
        match_count: 10
      })

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)
      expect(data?.length).toBe(0)
    })
  })

  describe('Vector Index Performance', () => {
    beforeEach(async () => {
      // Create a larger dataset for performance testing
      const chunks = []
      for (let i = 0; i < 50; i++) {
        chunks.push({
          document_id: testDocuments[i % testDocuments.length].id,
          content: `Test content chunk ${i} with various keywords and topics.`,
          metadata: { page: Math.floor(i / 10) + 1, chunk_index: i },
          embedding: generateRandomVector(1536)
        })
      }

      await supabase.from('document_chunks').insert(chunks)
    })

    it('should perform vector search efficiently with large dataset', async () => {
      const queryVector = generateRandomVector(1536)

      const startTime = performance.now()
      
      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: queryVector,
        match_threshold: 0.0,
        match_count: 10
      })

      const endTime = performance.now()
      const duration = endTime - startTime

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)
      expect(duration).toBeLessThan(1000) // Should complete within 1 second
    })

    it('should handle concurrent vector searches', async () => {
      const queryVector = generateRandomVector(1536)

      // Perform multiple concurrent searches
      const searchPromises = Array.from({ length: 5 }, () =>
        supabase.rpc('match_documents', {
          query_embedding: queryVector,
          match_threshold: 0.0,
          match_count: 5
        })
      )

      const results = await Promise.all(searchPromises)

      // All searches should succeed
      results.forEach(({ data, error }) => {
        expect(error).toBeNull()
        expect(Array.isArray(data)).toBe(true)
      })
    })
  })

  describe('Vector Search with Metadata Filtering', () => {
    beforeEach(async () => {
      const testChunks = [
        {
          document_id: testDocuments[0].id,
          content: 'Sales content from page 1',
          metadata: { page: 1, topic: 'sales', difficulty: 'beginner' },
          embedding: generateRandomVector(1536)
        },
        {
          document_id: testDocuments[0].id,
          content: 'Sales content from page 2',
          metadata: { page: 2, topic: 'sales', difficulty: 'advanced' },
          embedding: generateRandomVector(1536)
        },
        {
          document_id: testDocuments[1].id,
          content: 'Technical content from page 1',
          metadata: { page: 1, topic: 'technical', difficulty: 'intermediate' },
          embedding: generateRandomVector(1536)
        }
      ]

      await supabase.from('document_chunks').insert(testChunks)
    })

    it('should support filtering by metadata in combination with vector search', async () => {
      const queryVector = generateRandomVector(1536)

      // Search for chunks with specific metadata
      const { data, error } = await supabase
        .from('document_chunks')
        .select('*, similarity:embedding <=> $1::vector')
        .eq('metadata->>topic', 'sales')
        .order('similarity')
        .limit(10)

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)

      // All results should have 'sales' topic
      data?.forEach(chunk => {
        expect(chunk.metadata.topic).toBe('sales')
      })
    })

    it('should support complex metadata queries with vector search', async () => {
      const queryVector = generateRandomVector(1536)

      // Search for chunks with page 1 and beginner difficulty
      const { data, error } = await supabase
        .from('document_chunks')
        .select('*')
        .eq('metadata->>page', '1')
        .eq('metadata->>difficulty', 'beginner')

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)

      if (data && data.length > 0) {
        data.forEach(chunk => {
          expect(chunk.metadata.page).toBe(1)
          expect(chunk.metadata.difficulty).toBe('beginner')
        })
      }
    })
  })

  describe('Vector Search Error Handling', () => {
    it('should handle invalid vector dimensions in search', async () => {
      const invalidVector = generateRandomVector(512) // Wrong dimension

      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: invalidVector,
        match_threshold: 0.5,
        match_count: 5
      })

      expect(error).not.toBeNull()
      expect(data).toBeNull()
    })

    it('should handle null or undefined vectors', async () => {
      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: null,
        match_threshold: 0.5,
        match_count: 5
      })

      expect(error).not.toBeNull()
      expect(data).toBeNull()
    })

    it('should handle invalid threshold values', async () => {
      const queryVector = generateRandomVector(1536)

      // Test with negative threshold
      const { data: negativeData, error: negativeError } = await supabase.rpc('match_documents', {
        query_embedding: queryVector,
        match_threshold: -1,
        match_count: 5
      })

      // Test with threshold > 1
      const { data: highData, error: highError } = await supabase.rpc('match_documents', {
        query_embedding: queryVector,
        match_threshold: 2,
        match_count: 5
      })

      // Both should handle gracefully (may return empty results or error)
      expect(Array.isArray(negativeData) || negativeError !== null).toBe(true)
      expect(Array.isArray(highData) || highError !== null).toBe(true)
    })

    it('should handle invalid match count values', async () => {
      const queryVector = generateRandomVector(1536)

      // Test with negative match count
      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: queryVector,
        match_threshold: 0.5,
        match_count: -1
      })

      // Should handle gracefully
      expect(Array.isArray(data) || error !== null).toBe(true)
    })
  })
})
