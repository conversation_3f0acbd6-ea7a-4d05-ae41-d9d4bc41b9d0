import { supabase } from '../../supabaseClient'
import {
  setupTestDatabase,
  teardownTestDatabase,
  generateTestUser,
  generateTestCallTranscript,
  generateTestDocument,
  generateTestDocumentChunk,
  cleanupTestData
} from './setup'

describe('Database Operations Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await teardownTestDatabase()
  })

  beforeEach(async () => {
    // Clean up before each test to ensure isolation
    await cleanupTestData()
  })

  describe('User Operations', () => {
    it('should create a new user successfully', async () => {
      const testUser = generateTestUser()

      const { data, error } = await supabase
        .from('users')
        .insert(testUser)
        .select()

      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(data?.[0]?.email).toBe(testUser.email)
      expect(data?.[0]?.full_name).toBe(testUser.full_name)
      expect(data?.[0]?.username).toBe(testUser.username)
    })

    it('should automatically create user profile when user is created', async () => {
      const testUser = generateTestUser()

      // Insert user
      const { data: userData, error: userError } = await supabase
        .from('users')
        .insert(testUser)
        .select()

      expect(userError).toBeNull()

      // Check if user profile was created automatically
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', testUser.id)

      expect(profileError).toBeNull()
      expect(profileData).toBeDefined()
      expect(profileData?.length).toBe(1)
      expect(profileData?.[0]?.user_id).toBe(testUser.id)
    })

    it('should update user information', async () => {
      const testUser = generateTestUser()

      // Create user
      await supabase.from('users').insert(testUser)

      // Update user
      const updatedData = {
        full_name: 'Updated Full Name',
        company: 'Updated Company'
      }

      const { data, error } = await supabase
        .from('users')
        .update(updatedData)
        .eq('id', testUser.id)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.full_name).toBe(updatedData.full_name)
      expect(data?.[0]?.company).toBe(updatedData.company)
    })

    it('should delete user and cascade to related records', async () => {
      const testUser = generateTestUser()

      // Create user
      await supabase.from('users').insert(testUser)

      // Create related records
      const testTranscript = generateTestCallTranscript(testUser.id)
      await supabase.from('call_transcripts').insert(testTranscript)

      const testDocument = generateTestDocument(testUser.id)
      await supabase.from('documents').insert(testDocument)

      // Delete user
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', testUser.id)

      expect(error).toBeNull()

      // Check that related records were deleted
      const { data: transcriptData } = await supabase
        .from('call_transcripts')
        .select('*')
        .eq('user_id', testUser.id)

      const { data: documentData } = await supabase
        .from('documents')
        .select('*')
        .eq('user_id', testUser.id)

      const { data: profileData } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', testUser.id)

      expect(transcriptData?.length).toBe(0)
      expect(documentData?.length).toBe(0)
      expect(profileData?.length).toBe(0)
    })

    it('should handle duplicate email constraint', async () => {
      const testUser1 = generateTestUser({ email: '<EMAIL>' })
      const testUser2 = generateTestUser({ email: '<EMAIL>' })

      // Insert first user
      const { error: error1 } = await supabase.from('users').insert(testUser1)
      expect(error1).toBeNull()

      // Try to insert second user with same email
      const { error: error2 } = await supabase.from('users').insert(testUser2)
      expect(error2).not.toBeNull()
      expect(error2?.message).toContain('duplicate key value')
    })

    it('should handle duplicate username constraint', async () => {
      const testUser1 = generateTestUser({ username: 'duplicateuser' })
      const testUser2 = generateTestUser({ username: 'duplicateuser' })

      // Insert first user
      const { error: error1 } = await supabase.from('users').insert(testUser1)
      expect(error1).toBeNull()

      // Try to insert second user with same username
      const { error: error2 } = await supabase.from('users').insert(testUser2)
      expect(error2).not.toBeNull()
      expect(error2?.message).toContain('duplicate key value')
    })
  })

  describe('Call Transcript Operations', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = generateTestUser()
      await supabase.from('users').insert(testUser)
    })

    it('should create call transcript with JSONB segments', async () => {
      const testTranscript = generateTestCallTranscript(testUser.id)

      const { data, error } = await supabase
        .from('call_transcripts')
        .insert(testTranscript)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.user_id).toBe(testUser.id)
      expect(data?.[0]?.transcript_segments).toBeDefined()
      expect(Array.isArray(data?.[0]?.transcript_segments)).toBe(true)
      expect(data?.[0]?.transcript_segments.length).toBe(2)
    })

    it('should query transcripts by user', async () => {
      const transcript1 = generateTestCallTranscript(testUser.id)
      const transcript2 = generateTestCallTranscript(testUser.id)

      await supabase.from('call_transcripts').insert([transcript1, transcript2])

      const { data, error } = await supabase
        .from('call_transcripts')
        .select('*')
        .eq('user_id', testUser.id)
        .order('created_at', { ascending: false })

      expect(error).toBeNull()
      expect(data?.length).toBe(2)
      expect(data?.[0]?.user_id).toBe(testUser.id)
      expect(data?.[1]?.user_id).toBe(testUser.id)
    })

    it('should update transcript segments', async () => {
      const testTranscript = generateTestCallTranscript(testUser.id)

      const { data: insertData } = await supabase
        .from('call_transcripts')
        .insert(testTranscript)
        .select()

      const transcriptId = insertData?.[0]?.transcript_id

      const updatedSegments = [
        ...testTranscript.transcript_segments,
        {
          speaker: 'user',
          text: 'Thank you for your time.',
          timestamp: new Date().toISOString()
        }
      ]

      const { data, error } = await supabase
        .from('call_transcripts')
        .update({ transcript_segments: updatedSegments })
        .eq('transcript_id', transcriptId)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.transcript_segments.length).toBe(3)
    })

    it('should create call summary linked to transcript', async () => {
      const testTranscript = generateTestCallTranscript(testUser.id)

      const { data: transcriptData } = await supabase
        .from('call_transcripts')
        .insert(testTranscript)
        .select()

      const transcriptId = transcriptData?.[0]?.transcript_id

      const testSummary = {
        transcript_id: transcriptId,
        summary_text: 'This is a test call summary.',
        key_points: ['Point 1', 'Point 2', 'Point 3'],
        action_items: [
          { task: 'Follow up with customer', due_date: '2024-12-31' }
        ],
        sentiment_overview: { overall: 'positive', confidence: 0.85 }
      }

      const { data, error } = await supabase
        .from('call_summaries')
        .insert(testSummary)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.transcript_id).toBe(transcriptId)
      expect(data?.[0]?.key_points).toEqual(testSummary.key_points)
      expect(data?.[0]?.action_items).toEqual(testSummary.action_items)
    })
  })

  describe('Document Operations', () => {
    let testUser: any

    beforeEach(async () => {
      testUser = generateTestUser()
      await supabase.from('users').insert(testUser)
    })

    it('should create document with metadata', async () => {
      const testDocument = generateTestDocument(testUser.id)

      const { data, error } = await supabase
        .from('documents')
        .insert(testDocument)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.user_id).toBe(testUser.id)
      expect(data?.[0]?.title).toBe(testDocument.title)
      expect(data?.[0]?.status).toBe('processed')
    })

    it('should create document chunks with vector embeddings', async () => {
      const testDocument = generateTestDocument(testUser.id)

      const { data: docData } = await supabase
        .from('documents')
        .insert(testDocument)
        .select()

      const documentId = docData?.[0]?.id

      const testChunk = generateTestDocumentChunk(documentId)

      const { data, error } = await supabase
        .from('document_chunks')
        .insert(testChunk)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.document_id).toBe(documentId)
      expect(data?.[0]?.content).toBe(testChunk.content)
      expect(data?.[0]?.embedding).toBeDefined()
      expect(Array.isArray(data?.[0]?.embedding)).toBe(true)
      expect(data?.[0]?.embedding.length).toBe(1536)
    })

    it('should query documents by user with pagination', async () => {
      // Create multiple documents
      const documents = Array.from({ length: 5 }, () => generateTestDocument(testUser.id))
      await supabase.from('documents').insert(documents)

      // Query with pagination
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .eq('user_id', testUser.id)
        .order('created_at', { ascending: false })
        .range(0, 2) // First 3 documents

      expect(error).toBeNull()
      expect(data?.length).toBe(3)
      expect(data?.[0]?.user_id).toBe(testUser.id)
    })

    it('should update document status', async () => {
      const testDocument = generateTestDocument(testUser.id, { status: 'pending' })

      const { data: insertData } = await supabase
        .from('documents')
        .insert(testDocument)
        .select()

      const documentId = insertData?.[0]?.id

      const { data, error } = await supabase
        .from('documents')
        .update({ status: 'processed' })
        .eq('id', documentId)
        .select()

      expect(error).toBeNull()
      expect(data?.[0]?.status).toBe('processed')
    })
  })
})
