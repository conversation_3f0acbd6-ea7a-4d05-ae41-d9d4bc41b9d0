// Database test setup
import { supabase } from '../../supabaseClient'

// Test database configuration
export const TEST_DB_CONFIG = {
  url: process.env.SUPABASE_URL || 'http://localhost:54321',
  serviceKey: process.env.SUPABASE_SERVICE_KEY || 'test-service-key',
  anonKey: process.env.SUPABASE_ANON_KEY || 'test-anon-key'
}

// Test user data
export const TEST_USERS = {
  user1: {
    id: '00000000-0000-0000-0000-000000000001',
    email: '<EMAIL>',
    full_name: 'Test User 1',
    company: 'Test Company 1',
    username: 'testuser1'
  },
  user2: {
    id: '00000000-0000-0000-0000-000000000002',
    email: '<EMAIL>',
    full_name: 'Test User 2',
    company: 'Test Company 2',
    username: 'testuser2'
  }
}

// Test data generators
export const generateTestUser = (overrides: Partial<typeof TEST_USERS.user1> = {}) => ({
  ...TEST_USERS.user1,
  id: `test-user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  email: `test-${Date.now()}@example.com`,
  username: `testuser_${Date.now()}`,
  ...overrides
})

export const generateTestCallTranscript = (userId: string, overrides: any = {}) => ({
  user_id: userId,
  call_start_time: new Date().toISOString(),
  call_end_time: new Date(Date.now() + 3600000).toISOString(), // 1 hour later
  full_transcript: 'This is a test call transcript.',
  transcript_segments: [
    {
      speaker: 'user',
      text: 'Hello, how can I help you today?',
      timestamp: new Date().toISOString()
    },
    {
      speaker: 'customer',
      text: 'I am interested in your product.',
      timestamp: new Date(Date.now() + 5000).toISOString()
    }
  ],
  raw_audio_storage_path: '/test/audio/path.wav',
  ...overrides
})

export const generateTestDocument = (userId: string, overrides: any = {}) => ({
  user_id: userId,
  title: 'Test Document',
  description: 'This is a test document for testing purposes.',
  file_path: '/test/documents/test.pdf',
  file_type: 'application/pdf',
  status: 'processed',
  ...overrides
})

export const generateTestDocumentChunk = (documentId: string, overrides: any = {}) => ({
  document_id: documentId,
  content: 'This is a test document chunk content for vector search testing.',
  metadata: {
    page: 1,
    section: 'introduction',
    chunk_index: 0
  },
  embedding: Array.from({ length: 1536 }, () => Math.random() - 0.5), // Random vector
  ...overrides
})

// Database cleanup utilities
export const cleanupTestData = async () => {
  try {
    // Clean up in reverse order of dependencies
    await supabase.from('call_summaries').delete().neq('summary_id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('document_chunks').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('documents').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('call_transcripts').delete().neq('transcript_id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('subscriptions').delete().neq('subscription_id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('user_profiles').delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from('users').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  } catch (error) {
    console.warn('Cleanup warning:', error)
  }
}

// Database connection testing
export const testDatabaseConnection = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase.from('users').select('count').limit(1)
    return !error
  } catch (error) {
    return false
  }
}

// RLS testing utilities
export const createTestUserSession = async (userId: string) => {
  // Mock user session for RLS testing
  return {
    user: {
      id: userId,
      email: TEST_USERS.user1.email,
      aud: 'authenticated',
      role: 'authenticated'
    },
    access_token: 'mock-access-token',
    refresh_token: 'mock-refresh-token'
  }
}

// Migration testing utilities
export const checkTableExists = async (tableName: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('check_table_exists', { table_name: tableName })
    return !error && data
  } catch (error) {
    // Fallback method
    try {
      await supabase.from(tableName).select('*').limit(1)
      return true
    } catch (fallbackError) {
      return false
    }
  }
}

export const checkColumnExists = async (tableName: string, columnName: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('check_column_exists', { 
      table_name: tableName, 
      column_name: columnName 
    })
    return !error && data
  } catch (error) {
    return false
  }
}

export const checkIndexExists = async (indexName: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('check_index_exists', { index_name: indexName })
    return !error && data
  } catch (error) {
    return false
  }
}

// Vector search testing utilities
export const generateRandomVector = (dimensions: number = 1536): number[] => {
  return Array.from({ length: dimensions }, () => Math.random() - 0.5)
}

export const testVectorSimilarity = async (queryVector: number[], threshold: number = 0.5) => {
  try {
    const { data, error } = await supabase.rpc('match_documents', {
      query_embedding: queryVector,
      match_threshold: threshold,
      match_count: 10
    })
    return { data, error }
  } catch (error) {
    return { data: null, error }
  }
}

// Performance testing utilities
export const measureQueryTime = async (queryFn: () => Promise<any>): Promise<{ result: any, duration: number }> => {
  const startTime = performance.now()
  const result = await queryFn()
  const endTime = performance.now()
  return {
    result,
    duration: endTime - startTime
  }
}

// Data integrity testing utilities
export const checkForeignKeyConstraints = async (tableName: string, foreignKeyColumn: string, referencedTable: string) => {
  try {
    // Insert a record with invalid foreign key
    const invalidId = '99999999-9999-9999-9999-999999999999'
    const testData = { [foreignKeyColumn]: invalidId }
    
    const { error } = await supabase.from(tableName).insert(testData)
    
    // Should return an error due to foreign key constraint
    return error !== null
  } catch (error) {
    return true // Error expected
  }
}

// Setup and teardown for tests
export const setupTestDatabase = async () => {
  // Ensure test database is clean
  await cleanupTestData()
  
  // Check database connection
  const isConnected = await testDatabaseConnection()
  if (!isConnected) {
    throw new Error('Cannot connect to test database')
  }
  
  return true
}

export const teardownTestDatabase = async () => {
  await cleanupTestData()
}
