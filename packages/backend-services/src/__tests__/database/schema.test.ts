import { supabase } from '../../supabaseClient'
import {
  setupTestDatabase,
  teardownTestDatabase,
  checkTableExists,
  checkColumnExists,
  checkIndexExists,
  testDatabaseConnection
} from './setup'

describe('Database Schema Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await teardownTestDatabase()
  })

  describe('Database Connection', () => {
    it('should connect to the database successfully', async () => {
      const isConnected = await testDatabaseConnection()
      expect(isConnected).toBe(true)
    })

    it('should have access to Supabase client', () => {
      expect(supabase).toBeDefined()
      expect(typeof supabase.from).toBe('function')
    })
  })

  describe('Core Tables Existence', () => {
    const requiredTables = [
      'users',
      'user_profiles',
      'call_transcripts',
      'call_summaries',
      'documents',
      'document_chunks',
      'plans',
      'subscriptions'
    ]

    requiredTables.forEach(tableName => {
      it(`should have ${tableName} table`, async () => {
        const exists = await checkTableExists(tableName)
        expect(exists).toBe(true)
      })
    })
  })

  describe('Users Table Schema', () => {
    const requiredColumns = [
      'id',
      'email',
      'full_name',
      'company',
      'username',
      'created_at',
      'updated_at',
      'last_sign_in'
    ]

    requiredColumns.forEach(columnName => {
      it(`should have ${columnName} column in users table`, async () => {
        const exists = await checkColumnExists('users', columnName)
        expect(exists).toBe(true)
      })
    })

    it('should have unique constraint on email', async () => {
      const testUser1 = {
        id: '11111111-1111-1111-1111-111111111111',
        email: '<EMAIL>',
        username: 'user1'
      }
      
      const testUser2 = {
        id: '22222222-2222-2222-2222-222222222222',
        email: '<EMAIL>', // Same email
        username: 'user2'
      }

      // Insert first user
      const { error: error1 } = await supabase.from('users').insert(testUser1)
      expect(error1).toBeNull()

      // Try to insert second user with same email
      const { error: error2 } = await supabase.from('users').insert(testUser2)
      expect(error2).not.toBeNull()
      expect(error2?.message).toContain('duplicate key value')

      // Cleanup
      await supabase.from('users').delete().eq('id', testUser1.id)
    })

    it('should have unique constraint on username', async () => {
      const testUser1 = {
        id: '33333333-3333-3333-3333-333333333333',
        email: '<EMAIL>',
        username: 'duplicateusername'
      }
      
      const testUser2 = {
        id: '44444444-4444-4444-4444-444444444444',
        email: '<EMAIL>',
        username: 'duplicateusername' // Same username
      }

      // Insert first user
      const { error: error1 } = await supabase.from('users').insert(testUser1)
      expect(error1).toBeNull()

      // Try to insert second user with same username
      const { error: error2 } = await supabase.from('users').insert(testUser2)
      expect(error2).not.toBeNull()
      expect(error2?.message).toContain('duplicate key value')

      // Cleanup
      await supabase.from('users').delete().eq('id', testUser1.id)
    })
  })

  describe('User Profiles Table Schema', () => {
    const requiredColumns = [
      'id',
      'user_id',
      'job_title',
      'profile_picture_url',
      'preferences',
      'settings',
      'created_at',
      'updated_at'
    ]

    requiredColumns.forEach(columnName => {
      it(`should have ${columnName} column in user_profiles table`, async () => {
        const exists = await checkColumnExists('user_profiles', columnName)
        expect(exists).toBe(true)
      })
    })

    it('should have foreign key constraint to users table', async () => {
      const invalidProfile = {
        user_id: '99999999-9999-9999-9999-999999999999', // Non-existent user
        job_title: 'Test Job'
      }

      const { error } = await supabase.from('user_profiles').insert(invalidProfile)
      expect(error).not.toBeNull()
      expect(error?.message).toContain('foreign key constraint')
    })
  })

  describe('Call Transcripts Table Schema', () => {
    const requiredColumns = [
      'transcript_id',
      'user_id',
      'call_start_time',
      'call_end_time',
      'full_transcript',
      'transcript_segments',
      'raw_audio_storage_path',
      'created_at'
    ]

    requiredColumns.forEach(columnName => {
      it(`should have ${columnName} column in call_transcripts table`, async () => {
        const exists = await checkColumnExists('call_transcripts', columnName)
        expect(exists).toBe(true)
      })
    })

    it('should have JSONB type for transcript_segments', async () => {
      // Test by inserting valid JSONB data
      const testUser = {
        id: '55555555-5555-5555-5555-555555555555',
        email: '<EMAIL>',
        username: 'transcriptuser'
      }

      await supabase.from('users').insert(testUser)

      const testTranscript = {
        user_id: testUser.id,
        transcript_segments: [
          { speaker: 'user', text: 'Hello', timestamp: new Date().toISOString() },
          { speaker: 'customer', text: 'Hi there', timestamp: new Date().toISOString() }
        ]
      }

      const { error } = await supabase.from('call_transcripts').insert(testTranscript)
      expect(error).toBeNull()

      // Cleanup
      await supabase.from('call_transcripts').delete().eq('user_id', testUser.id)
      await supabase.from('users').delete().eq('id', testUser.id)
    })
  })

  describe('Documents Table Schema', () => {
    const requiredColumns = [
      'id',
      'user_id',
      'title',
      'description',
      'file_path',
      'file_type',
      'status',
      'created_at',
      'updated_at'
    ]

    requiredColumns.forEach(columnName => {
      it(`should have ${columnName} column in documents table`, async () => {
        const exists = await checkColumnExists('documents', columnName)
        expect(exists).toBe(true)
      })
    })

    it('should have default status value', async () => {
      const testUser = {
        id: '66666666-6666-6666-6666-666666666666',
        email: '<EMAIL>',
        username: 'documentuser'
      }

      await supabase.from('users').insert(testUser)

      const testDocument = {
        user_id: testUser.id,
        title: 'Test Document'
      }

      const { data, error } = await supabase.from('documents').insert(testDocument).select()
      expect(error).toBeNull()
      expect(data?.[0]?.status).toBe('pending')

      // Cleanup
      await supabase.from('documents').delete().eq('user_id', testUser.id)
      await supabase.from('users').delete().eq('id', testUser.id)
    })
  })

  describe('Document Chunks Table Schema', () => {
    const requiredColumns = [
      'id',
      'document_id',
      'content',
      'metadata',
      'embedding',
      'created_at'
    ]

    requiredColumns.forEach(columnName => {
      it(`should have ${columnName} column in document_chunks table`, async () => {
        const exists = await checkColumnExists('document_chunks', columnName)
        expect(exists).toBe(true)
      })
    })

    it('should support vector embeddings', async () => {
      const testUser = {
        id: '77777777-7777-7777-7777-777777777777',
        email: '<EMAIL>',
        username: 'vectoruser'
      }

      await supabase.from('users').insert(testUser)

      const testDocument = {
        user_id: testUser.id,
        title: 'Vector Test Document'
      }

      const { data: docData } = await supabase.from('documents').insert(testDocument).select()
      const documentId = docData?.[0]?.id

      const testChunk = {
        document_id: documentId,
        content: 'Test content for vector embedding',
        metadata: { page: 1 },
        embedding: Array.from({ length: 1536 }, () => Math.random() - 0.5)
      }

      const { error } = await supabase.from('document_chunks').insert(testChunk)
      expect(error).toBeNull()

      // Cleanup
      await supabase.from('document_chunks').delete().eq('document_id', documentId)
      await supabase.from('documents').delete().eq('id', documentId)
      await supabase.from('users').delete().eq('id', testUser.id)
    })
  })

  describe('Plans and Subscriptions Tables', () => {
    it('should have plans table with default plans', async () => {
      const { data, error } = await supabase.from('plans').select('*')
      expect(error).toBeNull()
      expect(data).toBeDefined()
      expect(data?.length).toBeGreaterThan(0)

      // Check for default plans
      const planNames = data?.map(plan => plan.name) || []
      expect(planNames).toContain('Free')
      expect(planNames).toContain('Pro')
      expect(planNames).toContain('Team')
    })

    it('should have subscriptions table with proper foreign keys', async () => {
      const requiredColumns = [
        'subscription_id',
        'user_id',
        'plan_id',
        'status',
        'current_period_start',
        'current_period_end',
        'created_at',
        'updated_at'
      ]

      for (const columnName of requiredColumns) {
        const exists = await checkColumnExists('subscriptions', columnName)
        expect(exists).toBe(true)
      }
    })
  })

  describe('Database Indexes', () => {
    it('should have vector similarity index on document_chunks', async () => {
      const exists = await checkIndexExists('document_chunks_embedding_idx')
      expect(exists).toBe(true)
    })

    it('should have indexes on foreign key columns for performance', async () => {
      // These indexes are typically created automatically by PostgreSQL for foreign keys
      // but we can test that queries on these columns are efficient
      const testUser = {
        id: '88888888-8888-8888-8888-888888888888',
        email: '<EMAIL>',
        username: 'indexuser'
      }

      await supabase.from('users').insert(testUser)

      // Test query performance on foreign key columns
      const startTime = performance.now()
      const { error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', testUser.id)
      const endTime = performance.now()

      expect(error).toBeNull()
      expect(endTime - startTime).toBeLessThan(100) // Should be fast

      // Cleanup
      await supabase.from('users').delete().eq('id', testUser.id)
    })
  })

  describe('Database Functions', () => {
    it('should have match_documents function for vector search', async () => {
      const queryVector = Array.from({ length: 1536 }, () => Math.random() - 0.5)
      
      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: queryVector,
        match_threshold: 0.5,
        match_count: 5
      })

      expect(error).toBeNull()
      expect(Array.isArray(data)).toBe(true)
    })
  })
})
