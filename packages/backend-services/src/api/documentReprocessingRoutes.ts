import express from 'express';
import { getDocumentService } from '../services/documentProcessingService';
import { logger } from '../utils/logger';

const router = express.Router();

// Import authentication middleware
import { authMiddleware } from '../authMiddleware';

/**
 * POST /api/v1/documents/reprocess/:documentId
 * Reprocess a failed document
 */
router.post('/reprocess/:documentId', authMiddleware, async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const { documentId } = req.params;
    const userId = (req as any).user?.id || (req as any).userId;

    if (!documentId) {
      res.status(400).json({
        success: false,
        error: 'Document ID is required'
      });
      return;
    }

    logger.info(`[DocumentReprocessing] Reprocessing document ${documentId} for user ${userId}`);

    // Get document details from database
    const documentService = getDocumentService();
    const supabase = (documentService as any).supabase;

    const { data: document, error: fetchError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', userId)
      .single();

    if (fetchError || !document) {
      res.status(404).json({
        success: false,
        error: 'Document not found or access denied'
      });
      return;
    }

    // Check if document is in a reprocessable state
    if (document.processing_status === 'processing') {
      res.status(400).json({
        success: false,
        error: 'Document is currently being processed'
      });
      return;
    }

    if (document.processing_status === 'completed') {
      res.status(400).json({
        success: false,
        error: 'Document has already been processed successfully'
      });
      return;
    }

    // Clean up any existing chunks for this document
    const { error: deleteError } = await supabase
      .from('document_chunks')
      .delete()
      .eq('document_id', documentId);

    if (deleteError) {
      logger.warn(`[DocumentReprocessing] Failed to clean up existing chunks: ${deleteError.message}`);
    }

    // Reset document status
    await supabase
      .from('documents')
      .update({
        processing_status: 'queued',
        error_message: null,
        chunk_count: 0,
        processed_at: null
      })
      .eq('id', documentId);

    // Create a mock file object for reprocessing
    const mockFile = {
      originalname: document.filename,
      mimetype: document.mimetype,
      size: document.size,
      buffer: Buffer.from('') // This will need to be handled differently in production
    } as Express.Multer.File;

    // Note: In a real implementation, you would need to store the original file
    // or have a way to retrieve it for reprocessing
    logger.warn(`[DocumentReprocessing] File buffer not available for reprocessing. This needs to be implemented based on your file storage strategy.`);

    res.status(200).json({
      success: true,
      message: 'Document queued for reprocessing',
      data: {
        documentId,
        status: 'queued'
      }
    });

  } catch (error: any) {
    logger.error(`[DocumentReprocessing] Error: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to reprocess document'
    });
  }
});

/**
 * GET /api/v1/documents/failed
 * Get list of failed documents for the user
 */
router.get('/failed', authMiddleware, async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id || (req as any).userId;
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
    const offset = (page - 1) * limit;

    logger.info(`[DocumentReprocessing] Getting failed documents for user ${userId}`);

    const documentService = getDocumentService();
    const supabase = (documentService as any).supabase;

    // Get failed documents
    const { data: documents, error, count } = await supabase
      .from('documents')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .eq('processing_status', 'failed')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    res.status(200).json({
      success: true,
      data: {
        documents: documents || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          pages: Math.ceil((count || 0) / limit)
        }
      }
    });

  } catch (error: any) {
    logger.error(`[DocumentReprocessing] Error getting failed documents: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve failed documents'
    });
  }
});

/**
 * GET /api/v1/documents/processing-stats
 * Get processing statistics for monitoring
 */
router.get('/processing-stats', authMiddleware, async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id || (req as any).userId;

    logger.info(`[DocumentReprocessing] Getting processing stats for user ${userId}`);

    const documentService = getDocumentService();
    const supabase = (documentService as any).supabase;

    // Get document counts by status
    const { data: stats, error } = await supabase
      .from('documents')
      .select('processing_status')
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    const statusCounts = (stats || []).reduce((acc: any, doc: any) => {
      acc[doc.processing_status] = (acc[doc.processing_status] || 0) + 1;
      return acc;
    }, {});

    // Get recent processing activity (last 24 hours)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const { data: recentDocs, error: recentError } = await supabase
      .from('documents')
      .select('processing_status, created_at')
      .eq('user_id', userId)
      .gte('created_at', yesterday);

    if (recentError) {
      logger.warn(`[DocumentReprocessing] Error getting recent docs: ${recentError.message}`);
    }

    const recentActivity = (recentDocs || []).length;

    res.status(200).json({
      success: true,
      data: {
        statusCounts,
        recentActivity,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error(`[DocumentReprocessing] Error getting processing stats: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve processing statistics'
    });
  }
});

export default router;
