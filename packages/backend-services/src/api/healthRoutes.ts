import express from 'express';
import { getHealthCheckService } from '../services/healthCheckService';
import { getMonitoringService } from '../services/monitoringService';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * GET /api/v1/health
 * Basic health check endpoint
 */
router.get('/', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const healthService = getHealthCheckService();
    const healthStatus = await healthService.performHealthCheck();

    const statusCode = healthStatus.overall === 'healthy' ? 200 : 
                      healthStatus.overall === 'degraded' ? 200 : 503;

    res.status(statusCode).json({
      success: healthStatus.overall !== 'unhealthy',
      status: healthStatus.overall,
      timestamp: healthStatus.timestamp,
      services: healthStatus.services,
      summary: healthStatus.summary
    });

  } catch (error: any) {
    logger.error(`[Health] Health check failed: ${error.message}`);
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check service unavailable'
    });
  }
});

/**
 * GET /api/v1/health/simple
 * Simple health check for load balancers
 */
router.get('/simple', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const healthService = getHealthCheckService();
    const healthStatus = await healthService.performHealthCheck();

    if (healthStatus.overall === 'unhealthy') {
      res.status(503).json({ status: 'unhealthy' });
    } else {
      res.status(200).json({ status: 'ok' });
    }

  } catch (error: any) {
    logger.error(`[Health] Simple health check failed: ${error.message}`);
    res.status(503).json({ status: 'error' });
  }
});

/**
 * GET /api/v1/health/detailed
 * Detailed health check with service-specific information
 */
router.get('/detailed', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const healthService = getHealthCheckService();
    const healthStatus = await healthService.performHealthCheck();

    // Add system information
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      environment: process.env.NODE_ENV || 'development'
    };

    res.status(200).json({
      success: healthStatus.overall !== 'unhealthy',
      status: healthStatus.overall,
      timestamp: healthStatus.timestamp,
      services: healthStatus.services,
      summary: healthStatus.summary,
      system: systemInfo
    });

  } catch (error: any) {
    logger.error(`[Health] Detailed health check failed: ${error.message}`);
    res.status(503).json({
      success: false,
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Detailed health check service unavailable'
    });
  }
});

/**
 * GET /api/v1/health/service/:serviceName
 * Check specific service health
 */
router.get('/service/:serviceName', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const { serviceName } = req.params;
    const healthService = getHealthCheckService();
    
    // Perform full health check and filter for specific service
    const healthStatus = await healthService.performHealthCheck();
    const serviceHealth = healthStatus.services.find(s => s.service === serviceName);

    if (!serviceHealth) {
      res.status(404).json({
        success: false,
        error: `Service '${serviceName}' not found`,
        availableServices: healthStatus.services.map(s => s.service)
      });
      return;
    }

    const statusCode = serviceHealth.status === 'healthy' ? 200 : 
                      serviceHealth.status === 'degraded' ? 200 : 503;

    res.status(statusCode).json({
      success: serviceHealth.status !== 'unhealthy',
      service: serviceHealth,
      timestamp: healthStatus.timestamp
    });

  } catch (error: any) {
    logger.error(`[Health] Service health check failed: ${error.message}`);
    res.status(503).json({
      success: false,
      error: 'Service health check unavailable'
    });
  }
});

/**
 * GET /api/v1/health/metrics
 * Get health metrics for monitoring
 */
router.get('/metrics', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const healthService = getHealthCheckService();
    const healthStatus = await healthService.performHealthCheck();

    // Calculate metrics
    const metrics = {
      timestamp: healthStatus.timestamp,
      overall_status: healthStatus.overall === 'healthy' ? 1 : 0,
      services_healthy: healthStatus.summary.healthy,
      services_degraded: healthStatus.summary.degraded,
      services_unhealthy: healthStatus.summary.unhealthy,
      services_total: healthStatus.summary.total,
      response_times: healthStatus.services.reduce((acc: any, service) => {
        acc[service.service] = service.responseTime;
        return acc;
      }, {}),
      memory_usage: process.memoryUsage(),
      uptime_seconds: process.uptime()
    };

    res.status(200).json({
      success: true,
      metrics
    });

  } catch (error: any) {
    logger.error(`[Health] Metrics collection failed: ${error.message}`);
    res.status(503).json({
      success: false,
      error: 'Metrics collection unavailable'
    });
  }
});

/**
 * POST /api/v1/health/test-embedding
 * Test embedding generation specifically
 */
router.post('/test-embedding', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const { text = 'test embedding generation' } = req.body;
    
    const { OpenAI } = require('openai');
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    const startTime = Date.now();
    
    const response = await openai.embeddings.create({
      model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-large',
      input: text,
      dimensions: parseInt(process.env.OPENAI_EMBEDDING_DIMENSIONS || '1536')
    });

    const responseTime = Date.now() - startTime;

    res.status(200).json({
      success: true,
      embedding: {
        model: process.env.OPENAI_EMBEDDING_MODEL,
        dimensions: response.data[0].embedding.length,
        responseTime,
        inputText: text,
        embeddingPreview: response.data[0].embedding.slice(0, 5)
      }
    });

  } catch (error: any) {
    logger.error(`[Health] Embedding test failed: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/v1/health/monitoring/alerts
 * Get current system alerts
 */
router.get('/monitoring/alerts', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const resolved = req.query.resolved === 'true';
    const monitoringService = getMonitoringService();
    const alerts = monitoringService.getAlerts(resolved);

    res.status(200).json({
      success: true,
      alerts,
      count: alerts.length
    });

  } catch (error: any) {
    logger.error(`[Health] Failed to get alerts: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve alerts'
    });
  }
});

/**
 * POST /api/v1/health/monitoring/alerts/:alertId/resolve
 * Resolve a specific alert
 */
router.post('/monitoring/alerts/:alertId/resolve', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const { alertId } = req.params;
    const monitoringService = getMonitoringService();
    const resolved = monitoringService.resolveAlert(alertId);

    if (resolved) {
      res.status(200).json({
        success: true,
        message: 'Alert resolved successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Alert not found'
      });
    }

  } catch (error: any) {
    logger.error(`[Health] Failed to resolve alert: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to resolve alert'
    });
  }
});

/**
 * GET /api/v1/health/monitoring/metrics
 * Get current system metrics
 */
router.get('/monitoring/metrics', async (req: express.Request, res: express.Response): Promise<void> => {
  try {
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 200);
    const monitoringService = getMonitoringService();

    const currentMetrics = monitoringService.getCurrentMetrics();
    const metricsHistory = monitoringService.getMetricsHistory(limit);
    const processingMetrics = await monitoringService.getProcessingMetrics();

    res.status(200).json({
      success: true,
      current: currentMetrics,
      history: metricsHistory,
      processing: processingMetrics
    });

  } catch (error: any) {
    logger.error(`[Health] Failed to get metrics: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve metrics'
    });
  }
});

export default router;
