import express from 'express';
import multer from 'multer';
import { DocumentProcessingService } from '../services/documentProcessingService';
import { VectorSearchService } from '../services/vectorSearchService';
import { logger } from '../utils/logger';
import { supabase } from '../supabaseClient';

const router = express.Router();

// Initialize services lazily
let documentService: DocumentProcessingService;
let vectorService: VectorSearchService;

function getDocumentService() {
  if (!documentService) {
    documentService = new DocumentProcessingService();
  }
  return documentService;
}

function getVectorService() {
  if (!vectorService) {
    vectorService = new VectorSearchService();
  }
  return vectorService;
}

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 1
  }
});

// Import the proper authentication middleware
import { authMiddleware } from '../authMiddleware';

// Use proper Supabase authentication with Bearer tokens only
const authenticate = async (req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> => {
  try {
    // Require Authorization Bearer token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Authentication required - provide Authorization Bearer token'
      });
      return;
    }

    const token = authHeader.replace('Bearer ', '');

    // Development test mode bypass (only for the specific test token)
    if (process.env.NODE_ENV === 'development' && token === 'test-token-for-api-testing') {
      console.log(`[Auth] Using development test mode`);
      (req as any).userId = '550e8400-e29b-41d4-a716-446655440000';
      next();
      return;
    }

    // Use proper Supabase authentication for all other tokens
    try {
      const { createClient } = require('@supabase/supabase-js');
      const supabaseAdmin = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );

      const { data, error } = await supabaseAdmin.auth.getUser(token);

      if (error || !data.user) {
        console.log(`[Auth] Invalid Supabase token: ${error?.message || 'User not found'}`);
        res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
        return;
      }

      console.log(`[Auth] Authenticated Supabase user: ${data.user.id} (${data.user.email})`);
      (req as any).userId = data.user.id;
      next();
      return;
    } catch (supabaseError: any) {
      console.log(`[Auth] Supabase validation error: ${supabaseError.message}`);
      res.status(401).json({
        success: false,
        error: 'Authentication validation failed'
      });
      return;
    }
  } catch (error) {
    console.error('[Auth] Authentication error:', error);
    res.status(401).json({
      success: false,
      error: 'Authentication failed'
    });
  }
};

/**
 * POST /api/knowledge/documents
 * Upload and process a document
 */
router.post('/documents',
  authenticate,
  upload.single('document'),
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
        return;
      }

      const userId = (req as any).userId;
      logger.info(`[KnowledgeAPI] Processing document for user: ${userId}, file: ${req.file.originalname}`);

      const result = await getDocumentService().processDocument(req.file, userId);

      res.status(202).json({
        success: true,
        message: 'Document queued for processing',
        data: result
      });
    } catch (error: any) {
      logger.error(`[KnowledgeAPI] Document upload failed: ${error.message}`);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

/**
 * GET /api/knowledge/documents
 * List user's documents
 */
router.get('/documents',
  authenticate,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const userId = (req as any).userId;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const offset = (page - 1) * limit;

      const { data: documents, error, count } = await supabase
        .from('documents')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('uploaded_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      const totalPages = Math.ceil((count || 0) / limit);

      res.json({
        success: true,
        data: {
          documents: documents || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            pages: totalPages
          }
        }
      });
    } catch (error: any) {
      logger.error(`[KnowledgeAPI] Document listing failed: ${error.message}`);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

/**
 * POST /api/knowledge/search
 * Search through documents
 */
router.post('/search',
  authenticate,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const userId = (req as any).userId;
      const { query, limit = 5, threshold = parseFloat(process.env.DEFAULT_SIMILARITY_THRESHOLD || '0.3') } = req.body;

      if (!query) {
        res.status(400).json({
          success: false,
          error: 'Query is required'
        });
        return;
      }

      const searchResponse = await getVectorService().search(query, {
        limit,
        threshold,
        userId
      });

      res.json({
        success: true,
        data: searchResponse
      });
    } catch (error: any) {
      logger.error(`[KnowledgeAPI] Search failed: ${error.message}`);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

/**
 * DELETE /api/knowledge/documents/:id
 * Delete a document and its associated chunks
 */
router.delete('/documents/:id',
  authenticate,
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const userId = (req as any).userId;
      const documentId = req.params.id;

      if (!documentId) {
        res.status(400).json({
          success: false,
          error: 'Document ID is required'
        });
        return;
      }

      logger.info(`[KnowledgeAPI] Deleting document ${documentId} for user: ${userId}`);

      // First, verify the document belongs to the user
      const { data: document, error: fetchError } = await supabase
        .from('documents')
        .select('id, user_id, filename')
        .eq('id', documentId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !document) {
        res.status(404).json({
          success: false,
          error: 'Document not found or access denied'
        });
        return;
      }

      // Delete associated document chunks first (due to foreign key constraint)
      const { error: chunksError } = await supabase
        .from('document_chunks')
        .delete()
        .eq('document_id', documentId)
        .eq('user_id', userId);

      if (chunksError) {
        logger.error(`[KnowledgeAPI] Failed to delete chunks for document ${documentId}: ${chunksError.message}`);
        res.status(500).json({
          success: false,
          error: 'Failed to delete document chunks'
        });
        return;
      }

      // Delete the document record
      const { error: documentError } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentId)
        .eq('user_id', userId);

      if (documentError) {
        logger.error(`[KnowledgeAPI] Failed to delete document ${documentId}: ${documentError.message}`);
        res.status(500).json({
          success: false,
          error: 'Failed to delete document'
        });
        return;
      }

      logger.info(`[KnowledgeAPI] Successfully deleted document ${documentId} (${document.filename})`);

      res.json({
        success: true,
        message: 'Document deleted successfully',
        data: {
          documentId,
          filename: document.filename
        }
      });
    } catch (error: any) {
      logger.error(`[KnowledgeAPI] Delete failed: ${error.message}`);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
);

/**
 * GET /api/knowledge/health
 * Health check endpoint
 */
router.get('/health',
  async (req: express.Request, res: express.Response): Promise<void> => {
    try {
      const health = await getVectorService().healthCheck();
      res.status(health.status === 'healthy' ? 200 : 503).json(health);
    } catch (error: any) {
      res.status(503).json({
        status: 'unhealthy',
        error: error.message
      });
    }
  }
);

export default router;
