import express, { Request, Response, NextFunction } from 'express'
import { LLMOrchestrationService } from '../../services/llmOrchestrationService'
import { SalesPromptTemplates, CallContext } from '../../prompts/salesPromptTemplates'
import { authMiddleware } from '../../authMiddleware'
import {
  validateAssistanceRequest,
  sanitizeAssistanceRequest,
  validateContentSafety
} from '../../utils/validation'
import {
  asyncHandler,
  handleValidationError,
  handleLLMError,
  ValidationError,
  LLMError,
  createHealthCheckError
} from '../../utils/errorHandler'
import { llmLogger, createPerformanceMonitor } from '../../utils/logger'

const router = express.Router()

// GET /api/v1/assist/health (no auth required for health check)
router.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'healthy',
    service: 'assist',
    timestamp: new Date().toISOString()
  })
})

// Define types for request bodies
interface AssistRequestBody {
  context: CallContext
  assistanceType: 'objection' | 'product_info' | 'general_assistance' | 'competitive_positioning' | 'closing' | 'discovery' | 'price_objection'
  query?: string // Required for specific assistance types
  useRAG?: boolean // Enable RAG-enhanced responses
}

interface StreamAssistRequestBody extends AssistRequestBody {
  stream: true
}

interface MultimodalAssistRequestBody {
  context: CallContext
  assistanceType: 'objection' | 'product_info' | 'general_assistance' | 'competitive_positioning' | 'closing' | 'discovery' | 'price_objection'
  query?: string
  audioData?: string // Base64 encoded audio
  imageData?: string // Base64 encoded image
  audioMimeType?: string
  imageMimeType?: string
  useRAG?: boolean // Enable RAG-enhanced responses
}

// Use proper Supabase authentication with Bearer tokens only
const authenticate = async (req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> => {
  try {
    // Require Authorization Bearer token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Authentication required - provide Authorization Bearer token'
      });
      return;
    }

    const token = authHeader.replace('Bearer ', '');

    // Development test mode bypass (only for the specific test token)
    if (process.env.NODE_ENV === 'development' && token === 'test-token-for-api-testing') {
      console.log(`[Auth] Using development test mode`);
      (req as any).user = { id: '550e8400-e29b-41d4-a716-446655440000' };
      next();
      return;
    }

    // Use proper Supabase authentication for all other tokens
    try {
      const { createClient } = require('@supabase/supabase-js');
      const supabaseAdmin = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );

      const { data, error } = await supabaseAdmin.auth.getUser(token);

      if (error || !data.user) {
        console.log(`[Auth] Invalid Supabase token: ${error?.message || 'User not found'}`);
        res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
        return;
      }

      console.log(`[Auth] Authenticated Supabase user: ${data.user.id} (${data.user.email})`);
      (req as any).user = data.user;
      next();
      return;
    } catch (supabaseError: any) {
      console.log(`[Auth] Supabase validation error: ${supabaseError.message}`);
      res.status(401).json({
        success: false,
        error: 'Authentication validation failed'
      });
      return;
    }
  } catch (error) {
    console.error('[Auth] Authentication error:', error);
    res.status(401).json({
      success: false,
      error: 'Authentication failed'
    });
  }
};

// Apply auth middleware to protect these routes
router.use(authenticate)

/**
 * POST /api/v1/assist/realtime
 * Main endpoint for real-time sales assistance
 */
router.post('/realtime', asyncHandler(async (req: Request, res: Response) => {
  const requestId = req.headers['x-request-id'] as string
  const userId = (req as any).user?.id
  const monitor = createPerformanceMonitor(requestId)

  // Validate request structure
  const validationResult = validateAssistanceRequest(req.body)
  handleValidationError(validationResult, 'Invalid assistance request')

  // Sanitize the request
  const sanitizedRequest = sanitizeAssistanceRequest(req.body)
  const { context, assistanceType, query, useRAG = false } = sanitizedRequest

  // Content safety validation
  if (query) {
    const safetyResult = validateContentSafety(query)
    if (!safetyResult.isValid) {
      throw new ValidationError('Content safety validation failed', safetyResult.errors)
    }
  }

  // Generate appropriate prompt based on assistance type
  let prompt: string
  switch (assistanceType) {
    case 'objection':
      prompt = SalesPromptTemplates.getObjectionHandlingPrompt(query!, context)
      break
    case 'product_info':
      prompt = SalesPromptTemplates.getProductInfoPrompt(query!, context)
      break
    case 'competitive_positioning':
      prompt = SalesPromptTemplates.getCompetitivePositioningPrompt(query!, context)
      break
    case 'price_objection':
      prompt = SalesPromptTemplates.getPriceObjectionPrompt(query!, context)
      break
    case 'closing':
      prompt = SalesPromptTemplates.getClosingPrompt(context)
      break
    case 'discovery':
      prompt = SalesPromptTemplates.getDiscoveryPrompt(context)
      break
    case 'general_assistance':
      prompt = SalesPromptTemplates.getRealtimeAssistancePrompt(context)
      break
    default:
      throw new ValidationError('Invalid assistance type', {
        validTypes: ['objection', 'product_info', 'general_assistance', 'competitive_positioning', 'closing', 'discovery', 'price_objection']
      })
  }

  // Log the request
  llmLogger.llmRequest(prompt, requestId, userId)
  monitor.checkpoint('prompt_generated')

  // Generate response using LLM service (with RAG if requested)
  const llmResponse = useRAG
    ? await LLMOrchestrationService.generateTextWithRAG({
        prompt,
        useRAG: true,
        userId,
        temperature: 0.7,
        maxOutputTokens: 500,
        contextLimit: 5,
        similarityThreshold: 0.3
      })
    : await LLMOrchestrationService.generateText({
        prompt,
        temperature: 0.7,
        maxOutputTokens: 500
      })

  // Log the response
  llmLogger.llmResponse(llmResponse, requestId, userId)
  monitor.checkpoint('llm_response_received')

  // Handle LLM errors
  handleLLMError(llmResponse, 'Sales assistance generation')

  // Parse the response into structured suggestions
  const suggestions = llmResponse.text!
    .split('\n')
    .map(s => s.trim())
    .filter(s => s.length > 0)

  const totalDuration = monitor.end('realtime_assistance_complete')

  res.status(200).json({
    success: true,
    assistanceType,
    suggestions,
    rawResponse: llmResponse.text,
    usage: llmResponse.usage,
    performance: {
      totalDuration,
      requestId
    },
    timestamp: new Date().toISOString(),
    // Include RAG metadata at top level for desktop app compatibility
    contextUsed: useRAG ? ((llmResponse as any).contextUsed || false) : false,
    sources: useRAG ? ((llmResponse as any).sources || []) : [],
    rag: useRAG ? {
      enabled: true,
      contextUsed: (llmResponse as any).contextUsed || false,
      sources: (llmResponse as any).sources || []
    } : { enabled: false }
  })
}))

/**
 * POST /api/v1/assist/stream
 * Streaming endpoint for real-time sales assistance
 */
router.post('/stream', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { context, assistanceType, query } = req.body as StreamAssistRequestBody

    // Same validation as realtime endpoint
    if (!context || !assistanceType) {
      return res.status(400).json({
        error: 'Missing required fields: context and assistanceType are required',
        code: 'MISSING_REQUIRED_FIELDS'
      })
    }

    const requiresQuery = ['objection', 'product_info', 'competitive_positioning', 'price_objection']
    if (requiresQuery.includes(assistanceType) && !query) {
      return res.status(400).json({
        error: `Query is required for assistance type: ${assistanceType}`,
        code: 'MISSING_QUERY'
      })
    }

    // Generate appropriate prompt (same logic as realtime)
    let prompt: string
    switch (assistanceType) {
      case 'objection':
        if (!query) return res.status(400).json({ error: 'Query is required for objection handling' })
        prompt = SalesPromptTemplates.getObjectionHandlingPrompt(query, context)
        break
      case 'product_info':
        if (!query) return res.status(400).json({ error: 'Query is required for product info' })
        prompt = SalesPromptTemplates.getProductInfoPrompt(query, context)
        break
      case 'competitive_positioning':
        if (!query) return res.status(400).json({ error: 'Competitor name is required for competitive positioning' })
        prompt = SalesPromptTemplates.getCompetitivePositioningPrompt(query, context)
        break
      case 'price_objection':
        if (!query) return res.status(400).json({ error: 'Price objection text is required' })
        prompt = SalesPromptTemplates.getPriceObjectionPrompt(query, context)
        break
      case 'closing':
        prompt = SalesPromptTemplates.getClosingPrompt(context)
        break
      case 'discovery':
        prompt = SalesPromptTemplates.getDiscoveryPrompt(context)
        break
      case 'general_assistance':
        prompt = SalesPromptTemplates.getRealtimeAssistancePrompt(context)
        break
      default:
        return res.status(400).json({
          error: 'Invalid assistance type',
          code: 'INVALID_ASSISTANCE_TYPE'
        })
    }

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    })

    try {
      const streamGenerator = await LLMOrchestrationService.generateTextStream({
        prompt,
        temperature: 0.7,
        maxOutputTokens: 500
      })

      for await (const chunk of streamGenerator) {
        if (chunk.text) {
          res.write(`data: ${JSON.stringify({ text: chunk.text, done: chunk.done })}\n\n`)
        }
        if (chunk.done) {
          res.write(`data: ${JSON.stringify({ done: true })}\n\n`)
          break
        }
      }
    } catch (streamError: any) {
      res.write(`data: ${JSON.stringify({ error: streamError.message })}\n\n`)
    }

    res.end()
  } catch (error: any) {
    console.error('[AssistAPI] Error in stream endpoint:', error)
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    })
  }
}))

/**
 * POST /api/v1/assist/multimodal
 * Multimodal endpoint for real-time sales assistance with audio and image inputs
 */
router.post('/multimodal', asyncHandler(async (req: Request, res: Response) => {
  const requestId = req.headers['x-request-id'] as string
  const userId = (req as any).user?.id
  const monitor = createPerformanceMonitor(requestId)

  const { context, assistanceType, query, audioData, imageData, audioMimeType, imageMimeType, useRAG = false } = req.body as MultimodalAssistRequestBody

  // Validate required fields
  if (!context || !assistanceType) {
    throw new ValidationError('Missing required fields: context and assistanceType are required')
  }

  // Validate that at least one media input is provided
  if (!audioData && !imageData && !query) {
    throw new ValidationError('At least one input (query, audioData, or imageData) must be provided')
  }

  // Content safety validation for text query if provided
  if (query) {
    const safetyResult = validateContentSafety(query)
    if (!safetyResult.isValid) {
      throw new ValidationError('Content safety validation failed', safetyResult.errors)
    }
  }

  // Generate sales-specific prompt for multimodal context
  let basePrompt: string
  switch (assistanceType) {
    case 'objection':
      basePrompt = SalesPromptTemplates.getMultimodalObjectionPrompt(context)
      break
    case 'product_info':
      basePrompt = SalesPromptTemplates.getMultimodalProductInfoPrompt(context)
      break
    case 'competitive_positioning':
      basePrompt = SalesPromptTemplates.getMultimodalCompetitivePrompt(context)
      break
    case 'price_objection':
      basePrompt = SalesPromptTemplates.getMultimodalPriceObjectionPrompt(context)
      break
    case 'closing':
      basePrompt = SalesPromptTemplates.getMultimodalClosingPrompt(context)
      break
    case 'discovery':
      basePrompt = SalesPromptTemplates.getMultimodalDiscoveryPrompt(context)
      break
    case 'general_assistance':
      basePrompt = SalesPromptTemplates.getMultimodalRealtimeAssistancePrompt(context)
      break
    default:
      throw new ValidationError('Invalid assistance type', {
        validTypes: ['objection', 'product_info', 'general_assistance', 'competitive_positioning', 'closing', 'discovery', 'price_objection']
      })
  }

  // Add specific query context if provided
  let fullPrompt = query ? `${basePrompt}\n\nSpecific question/context: ${query}` : basePrompt

  // Generate response using multimodal LLM service (with RAG if requested)
  let llmResponse
  let ragSources: any[] = []
  let contextUsed = false

  if (useRAG && query) {
    // Use LLM orchestration service's RAG functionality
    llmResponse = await LLMOrchestrationService.generateTextWithRAG({
      prompt: fullPrompt,
      useRAG: true,
      userId,
      temperature: 0.7,
      maxOutputTokens: 500,
      contextLimit: 3,
      similarityThreshold: 0.3
    })

    // Extract RAG information from response
    if ((llmResponse as any).sources) {
      ragSources = (llmResponse as any).sources.map((source: any) => ({
        id: source.id,
        content: source.content.substring(0, 200) + '...',
        filename: source.filename,
        similarity: source.similarity
      }))
      contextUsed = (llmResponse as any).contextUsed || false
      console.log(`[AssistAPI] Enhanced multimodal prompt with ${ragSources.length} RAG sources`)
    }
  } else {
    // Generate response using multimodal LLM service without RAG
    llmResponse = await LLMOrchestrationService.generateMultimodalContent({
      content: {
        text: fullPrompt,
        audioData,
        imageData,
        audioMimeType,
        imageMimeType
      },
      temperature: 0.7,
      maxOutputTokens: 500
    })
  }

  // Log the request
  llmLogger.llmRequest(fullPrompt, requestId, userId)
  monitor.checkpoint('multimodal_prompt_generated')

  // Log the response
  llmLogger.llmResponse(llmResponse, requestId, userId)
  monitor.checkpoint('multimodal_response_received')

  // Handle LLM errors
  handleLLMError(llmResponse, 'Multimodal sales assistance generation')

  // Parse the response into structured suggestions
  const suggestions = llmResponse.text!
    .split('\n')
    .map(s => s.trim())
    .filter(s => s.length > 0)

  const totalDuration = monitor.end('multimodal_assistance_complete')

  res.status(200).json({
    success: true,
    assistanceType,
    suggestions,
    rawResponse: llmResponse.text,
    usage: llmResponse.usage,
    performance: {
      totalDuration,
      requestId
    },
    timestamp: new Date().toISOString(),
    inputTypes: {
      hasAudio: !!audioData,
      hasImage: !!imageData,
      hasText: !!query
    },
    // Include RAG metadata at top level for desktop app compatibility
    contextUsed: useRAG ? contextUsed : false,
    sources: useRAG ? ragSources : [],
    rag: useRAG ? {
      enabled: true,
      contextUsed,
      sources: ragSources
    } : { enabled: false }
  })
}))

/**
 * GET /api/v1/assist/health
 * Health check endpoint for the assist service
 */
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  const connectionCheck = await LLMOrchestrationService.validateConnection()

  if (connectionCheck.success) {
    res.status(200).json({
      status: 'healthy',
      service: 'assist',
      llm: 'connected',
      timestamp: new Date().toISOString()
    })
  } else {
    throw createHealthCheckError('Gemini LLM', connectionCheck.error)
  }
}))

export default router
