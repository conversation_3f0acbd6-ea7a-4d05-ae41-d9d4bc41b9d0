export interface RAGConfig {
  openai: {
    apiKey: string
    embeddingModel: string
    embeddingDimensions: number
  }
  supabase: {
    url: string
    serviceRoleKey: string
  }
  redis: {
    host: string
    port: number
    password?: string
  }
  processing: {
    maxFileSizeMB: number
    chunkSize: number
    chunkOverlap: number
    defaultSimilarityThreshold: number
    maxSearchResults: number
  }
  rateLimiting: {
    upload: {
      windowMs: number
      max: number
    }
    search: {
      windowMs: number
      max: number
    }
  }
  logging: {
    level: string
    format: string
  }
}

export const ragConfig: RAGConfig = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    embeddingModel: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-large',
    embeddingDimensions: parseInt(process.env.OPENAI_EMBEDDING_DIMENSIONS || '1536')
  },
  supabase: {
    url: process.env.SUPABASE_URL || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || ''
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD
  },
  processing: {
    maxFileSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB || '50'),
    chunkSize: parseInt(process.env.CHUNK_SIZE || '1000'),
    chunkOverlap: parseInt(process.env.CHUNK_OVERLAP || '200'),
    defaultSimilarityThreshold: parseFloat(process.env.DEFAULT_SIMILARITY_THRESHOLD || '0.7'),
    maxSearchResults: parseInt(process.env.MAX_SEARCH_RESULTS || '50')
  },
  rateLimiting: {
    upload: {
      windowMs: parseInt(process.env.UPLOAD_RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      max: parseInt(process.env.UPLOAD_RATE_LIMIT_MAX || '10')
    },
    search: {
      windowMs: parseInt(process.env.SEARCH_RATE_LIMIT_WINDOW_MS || '60000'), // 1 minute
      max: parseInt(process.env.SEARCH_RATE_LIMIT_MAX || '60')
    }
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json'
  }
}

// Validation function
export function validateRAGConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!ragConfig.openai.apiKey) {
    errors.push('OPENAI_API_KEY is required')
  }

  if (!ragConfig.supabase.url) {
    errors.push('SUPABASE_URL is required')
  }

  if (!ragConfig.supabase.serviceRoleKey) {
    errors.push('SUPABASE_SERVICE_ROLE_KEY is required')
  }

  if (ragConfig.openai.embeddingDimensions <= 0) {
    errors.push('OPENAI_EMBEDDING_DIMENSIONS must be a positive number')
  }

  if (ragConfig.processing.chunkSize <= 0) {
    errors.push('CHUNK_SIZE must be a positive number')
  }

  if (ragConfig.processing.chunkOverlap < 0) {
    errors.push('CHUNK_OVERLAP must be non-negative')
  }

  if (ragConfig.processing.chunkOverlap >= ragConfig.processing.chunkSize) {
    errors.push('CHUNK_OVERLAP must be less than CHUNK_SIZE')
  }

  if (ragConfig.processing.defaultSimilarityThreshold < 0 || ragConfig.processing.defaultSimilarityThreshold > 1) {
    errors.push('DEFAULT_SIMILARITY_THRESHOLD must be between 0 and 1')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}
