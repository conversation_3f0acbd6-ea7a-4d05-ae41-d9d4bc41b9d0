import { LLMOrchestrationService } from './llmOrchestrationService';
import { AgentStateService, AgentState } from './agentStateService';
import { MemoryService } from './memoryService';
import { ToolOrchestrator, ToolResult } from './toolOrchestrator';
import { PlanningEngine, PlanningContext } from './planningEngine';
import { ReasoningEngine } from './reasoningEngine';
import { ProactiveSuggestionEngine, SuggestionContext } from './proactiveSuggestionEngine';
import { AdvancedToolOrchestrator } from './advancedToolOrchestrator';
import { CallContext } from '../prompts/salesPromptTemplates';
import { logger } from '../utils/logger';

// Enhanced interfaces extending existing ones
export interface AgentGenerateOptions {
  prompt: string;
  conversationId: string;
  userId: string;
  context: CallContext;
  useAgent?: boolean;
  useRAG?: boolean;
  temperature?: number;
  maxOutputTokens?: number;
  enableToolCalling?: boolean;
  maxToolCalls?: number;
  enablePlanning?: boolean;
  enableReasoning?: boolean;
  enableProactiveSuggestions?: boolean;
  reasoningPattern?: 'react' | 'chain_of_thought' | 'tree_of_thoughts';
}

export interface AgentGenerateResponse {
  success: boolean;
  text?: string;
  error?: string;
  agentState?: AgentState;
  toolsUsed?: ToolResult[];
  memoryContext?: string;
  sources?: any[];
  contextUsed?: string;
  usage?: any;
  planningSession?: any;
  reasoningChain?: any;
  proactiveSuggestions?: any[];
  metadata?: {
    agentEnabled: boolean;
    toolCallsExecuted: number;
    memoryItemsRetrieved: number;
    processingTime: number;
    planningEnabled?: boolean;
    reasoningEnabled?: boolean;
    suggestionsGenerated?: number;
  };
}

/**
 * Enhanced LLM Orchestration Service
 * Wraps the existing LLM service with agent capabilities including
 * memory management, tool calling, and state persistence
 */
export interface EnhancedLLMServiceDependencies {
  agentStateService: AgentStateService;
  memoryService: MemoryService;
  toolOrchestrator: ToolOrchestrator;
  planningEngine: PlanningEngine;
  reasoningEngine: ReasoningEngine;
  proactiveSuggestionEngine: ProactiveSuggestionEngine;
  advancedToolOrchestrator: AdvancedToolOrchestrator;
}

export class EnhancedLLMOrchestrationService {
  private agentStateService: AgentStateService;
  private memoryService: MemoryService;
  private toolOrchestrator: ToolOrchestrator;
  private planningEngine: PlanningEngine;
  private reasoningEngine: ReasoningEngine;
  private proactiveSuggestionEngine: ProactiveSuggestionEngine;
  private advancedToolOrchestrator: AdvancedToolOrchestrator;
  private maxToolCalls = 5;

  constructor(dependencies?: EnhancedLLMServiceDependencies) {
    if (dependencies) {
      // Use provided dependencies (dependency injection)
      this.agentStateService = dependencies.agentStateService;
      this.memoryService = dependencies.memoryService;
      this.toolOrchestrator = dependencies.toolOrchestrator;
      this.planningEngine = dependencies.planningEngine;
      this.reasoningEngine = dependencies.reasoningEngine;
      this.proactiveSuggestionEngine = dependencies.proactiveSuggestionEngine;
      this.advancedToolOrchestrator = dependencies.advancedToolOrchestrator;
    } else {
      // Fallback to creating new instances (backward compatibility)
      this.agentStateService = new AgentStateService();
      this.memoryService = new MemoryService();
      this.toolOrchestrator = new ToolOrchestrator();
      this.planningEngine = new PlanningEngine();
      this.reasoningEngine = new ReasoningEngine();
      this.proactiveSuggestionEngine = new ProactiveSuggestionEngine();
      this.advancedToolOrchestrator = new AdvancedToolOrchestrator();
    }

    logger.info('[EnhancedLLMService] Initialized with advanced agent capabilities');
  }

  /**
   * Generate response with agent capabilities
   * Falls back to regular LLM service if agent features are disabled
   */
  async generateWithAgent(options: AgentGenerateOptions): Promise<AgentGenerateResponse> {
    const startTime = Date.now();
    
    try {
      const {
        prompt,
        conversationId,
        userId,
        context,
        useAgent = true,
        useRAG = false,
        enableToolCalling = true,
        maxToolCalls = this.maxToolCalls,
        enablePlanning = false,
        enableReasoning = false,
        enableProactiveSuggestions = true,
        reasoningPattern = 'react',
        ...llmOptions
      } = options;

      // If agent is disabled, use regular LLM service
      if (!useAgent) {
        return await this.generateWithoutAgent(options);
      }

      // Get or create agent state
      let agentState = await this.agentStateService.getAgentState(conversationId);
      if (!agentState) {
        agentState = await this.agentStateService.createNewAgentState(conversationId, userId, context);
      }

      // Update context
      agentState.context = { ...agentState.context, ...context };

      // Build memory context
      const memoryContext = await this.memoryService.getMemoryContext(agentState, prompt);

      // Initialize advanced capabilities
      let planningSession: any = null;
      let reasoningChain: any = null;
      let proactiveSuggestions: any[] = [];

      // Execute planning if enabled
      if (enablePlanning) {
        try {
          const planningContext: PlanningContext = {
            agentState,
            availableTools: this.toolOrchestrator.getAvailableTools().map(t => t.name),
            timeConstraints: { maxDuration: 30 },
            resources: { maxToolCalls, maxSteps: 10 }
          };

          planningSession = await this.planningEngine.createPlanningSession(
            prompt,
            planningContext,
            { name: 'adaptive', description: 'Adaptive planning for user request' }
          );
        } catch (error: any) {
          logger.warn(`[EnhancedLLMService] Planning failed: ${error.message}`);
        }
      }

      // Execute reasoning if enabled
      if (enableReasoning) {
        try {
          switch (reasoningPattern) {
            case 'react':
              reasoningChain = await this.reasoningEngine.executeReActReasoning(prompt, agentState, 3);
              break;
            case 'chain_of_thought':
              reasoningChain = await this.reasoningEngine.executeChainOfThought(prompt, agentState, 5);
              break;
            case 'tree_of_thoughts':
              reasoningChain = await this.reasoningEngine.executeTreeOfThoughts(prompt, agentState, 2, 2);
              break;
          }
        } catch (error: any) {
          logger.warn(`[EnhancedLLMService] Reasoning failed: ${error.message}`);
        }
      }

      // Generate proactive suggestions if enabled
      if (enableProactiveSuggestions) {
        try {
          const suggestionContext: SuggestionContext = {
            agentState,
            conversationStage: this.determineConversationStage(agentState),
            timeInConversation: this.calculateConversationTime(agentState),
            lastActivity: new Date(),
            recentActions: agentState.toolResults?.slice(-5) || [],
            goals: [agentState.currentGoal || prompt].filter(Boolean) as string[]
          };

          proactiveSuggestions = await this.proactiveSuggestionEngine.generateSuggestions(suggestionContext);
        } catch (error: any) {
          logger.warn(`[EnhancedLLMService] Suggestion generation failed: ${error.message}`);
        }
      }

      // Build enhanced prompt with agent context and advanced capabilities
      const enhancedPrompt = this.buildAdvancedAgentPrompt(
        prompt,
        agentState,
        memoryContext,
        planningSession,
        reasoningChain,
        proactiveSuggestions
      );

      // Generate response with potential tool calling
      const response = await this.generateWithToolCalling(
        enhancedPrompt,
        agentState,
        { ...llmOptions, useRAG },
        enableToolCalling,
        maxToolCalls
      );

      // Store interaction in memory
      await this.memoryService.addMemory(
        agentState,
        `User: ${prompt}\nAssistant: ${response.text}`,
        'conversation',
        6
      );

      // Update agent state
      await this.agentStateService.updateAgentState(agentState);

      const processingTime = Date.now() - startTime;

      return {
        ...response,
        agentState,
        memoryContext,
        planningSession,
        reasoningChain,
        proactiveSuggestions,
        metadata: {
          agentEnabled: true,
          toolCallsExecuted: agentState.toolResults?.length || 0,
          memoryItemsRetrieved: memoryContext.split('\n').length,
          processingTime,
          planningEnabled: enablePlanning,
          reasoningEnabled: enableReasoning,
          suggestionsGenerated: proactiveSuggestions.length
        }
      };

    } catch (error: any) {
      logger.error(`[EnhancedLLMService] Agent generation error: ${error.message}`);
      
      // Fallback to regular generation
      try {
        const fallbackResponse = await this.generateWithoutAgent(options);
        return {
          ...fallbackResponse,
          metadata: {
            agentEnabled: false,
            toolCallsExecuted: 0,
            memoryItemsRetrieved: 0,
            processingTime: Date.now() - startTime
          }
        };
      } catch (fallbackError: any) {
        return {
          success: false,
          error: `Agent and fallback generation failed: ${error.message}, ${fallbackError.message}`,
          metadata: {
            agentEnabled: false,
            toolCallsExecuted: 0,
            memoryItemsRetrieved: 0,
            processingTime: Date.now() - startTime
          }
        };
      }
    }
  }

  /**
   * Generate response without agent features (fallback)
   */
  private async generateWithoutAgent(options: AgentGenerateOptions): Promise<AgentGenerateResponse> {
    try {
      const { prompt, useRAG, ...llmOptions } = options;

      let response;
      if (useRAG) {
        response = await LLMOrchestrationService.generateTextWithRAG({
          prompt,
          useRAG: true,
          ...llmOptions
        });
      } else {
        response = await LLMOrchestrationService.generateText({
          prompt,
          ...llmOptions
        });
      }

      return {
        success: response.success,
        text: response.text,
        error: response.error,
        sources: (response as any).sources,
        contextUsed: (response as any).contextUsed,
        usage: response.usage
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Build advanced agent prompt with all capabilities
   */
  private buildAdvancedAgentPrompt(
    prompt: string,
    agentState: AgentState,
    memoryContext: string,
    planningSession: any,
    reasoningChain: any,
    proactiveSuggestions: any[]
  ): string {
    const availableTools = this.toolOrchestrator.getAvailableTools(undefined, agentState.userId);

    let enhancedPrompt = `You are Closezly, an advanced AI Sales Co-Pilot with sophisticated agent capabilities.

AGENT CONTEXT:
- Conversation ID: ${agentState.conversationId}
- Current Goal: ${agentState.currentGoal || 'Assist with sales conversation'}

${memoryContext ? `MEMORY CONTEXT:\n${memoryContext}` : ''}

${planningSession ? `ACTIVE PLANNING SESSION:
Goal: ${planningSession.goal.description}
Progress: ${planningSession.progress}%
Next Steps: ${planningSession.steps.filter((s: any) => s.status === 'pending').slice(0, 3).map((s: any) => s.description).join(', ')}
` : ''}

${reasoningChain ? `REASONING INSIGHTS:
Pattern: ${reasoningChain.pattern.name}
Key Insights: ${reasoningChain.steps.slice(-2).map((s: any) => s.content).join(' | ')}
` : ''}

${proactiveSuggestions.length > 0 ? `PROACTIVE SUGGESTIONS:
${proactiveSuggestions.slice(0, 3).map(s => `- ${s.title}: ${s.description}`).join('\n')}
` : ''}

AVAILABLE TOOLS:
${availableTools.map(t => `- ${t.name}: ${t.description}`).join('\n')}

CURRENT CONTEXT:
${this.formatContext(agentState.context)}

ADVANCED CAPABILITIES:
- Planning: Decompose complex goals into actionable steps
- Reasoning: Use ReAct, Chain-of-Thought, or Tree-of-Thoughts patterns
- Proactive Suggestions: Anticipate needs and suggest next actions
- Tool Chaining: Execute complex multi-step workflows

TOOL CALLING INSTRUCTIONS:
To use a tool, include in your response: [TOOL:tool_name({"param1": "value1", "param2": "value2"})]
You can use multiple tools in sequence if needed.

USER REQUEST: ${prompt}

Instructions:
1. Consider all available context, memories, and insights
2. Use planning and reasoning capabilities for complex requests
3. Leverage proactive suggestions to add value
4. Use tools when they would provide better information
5. Provide helpful, contextual, and forward-thinking assistance

RESPONSE:`;

    return enhancedPrompt;
  }

  /**
   * Build enhanced prompt with agent context (legacy method for backward compatibility)
   */
  private buildAgentPrompt(prompt: string, agentState: AgentState, memoryContext: string): string {
    const availableTools = this.toolOrchestrator.getAvailableTools(undefined, agentState.userId);
    
    let enhancedPrompt = `You are Closezly, an AI Sales Co-Pilot with advanced agent capabilities.

AGENT CONTEXT:
- Conversation ID: ${agentState.conversationId}
- Current Goal: ${agentState.currentGoal || 'Assist with sales conversation'}

${memoryContext ? `MEMORY CONTEXT:\n${memoryContext}` : ''}

AVAILABLE TOOLS:
${availableTools.map(t => `- ${t.name}: ${t.description}`).join('\n')}

CURRENT CONTEXT:
${this.formatContext(agentState.context)}

TOOL CALLING INSTRUCTIONS:
To use a tool, include in your response: [TOOL:tool_name({"param1": "value1", "param2": "value2"})]
You can use multiple tools in sequence if needed.

USER REQUEST: ${prompt}

Instructions:
1. Consider the conversation history and relevant memories
2. If you need additional information, use available tools
3. Provide helpful, contextual assistance
4. Remember important information for future interactions
5. Use tools when they would provide better information than your general knowledge

RESPONSE:`;

    return enhancedPrompt;
  }

  /**
   * Generate response with tool calling capability
   */
  private async generateWithToolCalling(
    prompt: string,
    agentState: AgentState,
    llmOptions: any,
    enableToolCalling: boolean,
    maxToolCalls: number
  ): Promise<AgentGenerateResponse> {
    let currentPrompt = prompt;
    let toolCallCount = 0;
    const toolsUsed: ToolResult[] = [];

    // Generate initial response
    let response;
    if (llmOptions.useRAG) {
      response = await LLMOrchestrationService.generateTextWithRAG({
        prompt: currentPrompt,
        useRAG: true,
        userId: agentState.userId,
        ...llmOptions
      });
    } else {
      response = await LLMOrchestrationService.generateText({
        prompt: currentPrompt,
        ...llmOptions
      });
    }

    if (!response.success || !enableToolCalling) {
      return {
        success: response.success,
        text: response.text,
        error: response.error,
        toolsUsed,
        sources: (response as any).sources,
        contextUsed: (response as any).contextUsed,
        usage: response.usage
      };
    }

    let finalResponse = response.text!;

    // Process tool calls
    const toolCallPattern = /\[TOOL:(\w+)\s*\((.*?)\)\]/g;
    let match;
    let hasToolCalls = false;

    while ((match = toolCallPattern.exec(finalResponse)) !== null && toolCallCount < maxToolCalls) {
      hasToolCalls = true;
      const toolName = match[1];
      const toolParamsStr = match[2];

      try {
        const toolParams = this.parseToolParams(toolParamsStr);
        
        const toolResult = await this.toolOrchestrator.executeTool(
          toolName,
          toolParams,
          {
            agentState,
            userId: agentState.userId,
            conversationId: agentState.conversationId
          }
        );

        toolsUsed.push(toolResult);
        toolCallCount++;

        // Replace tool call with result
        const toolResultText = toolResult.success ? 
          `[TOOL_RESULT: ${JSON.stringify(toolResult.data)}]` : 
          `[TOOL_ERROR: ${toolResult.error}]`;

        finalResponse = finalResponse.replace(match[0], toolResultText);

        // Store tool result in working memory
        await this.memoryService.addToWorkingMemory(agentState, {
          tool: toolName,
          params: toolParams,
          result: toolResult
        });

        // Add to agent state tool results
        if (!agentState.toolResults) {
          agentState.toolResults = [];
        }
        agentState.toolResults.push({
          id: `tool_${Date.now()}`,
          toolName,
          parameters: toolParams,
          result: toolResult.data,
          success: toolResult.success,
          error: toolResult.error,
          executionTime: toolResult.executionTime || 0,
          timestamp: new Date()
        });

      } catch (error: any) {
        logger.error(`[EnhancedLLMService] Tool execution error: ${error.message}`);
        finalResponse = finalResponse.replace(match[0], `[TOOL_ERROR: ${error.message}]`);
      }
    }

    // If we made tool calls, generate a follow-up response to incorporate the results
    if (hasToolCalls && toolCallCount > 0) {
      const followUpPrompt = `${prompt}

TOOL RESULTS:
${toolsUsed.map(t => `${t.success ? 'SUCCESS' : 'FAILED'}: ${JSON.stringify(t.data || t.error)}`).join('\n')}

Based on the tool results above, provide a comprehensive response to the user's request:`;

      const followUpResponse = await LLMOrchestrationService.generateText({
        prompt: followUpPrompt,
        ...llmOptions
      });

      if (followUpResponse.success) {
        finalResponse = followUpResponse.text!;
      }
    }

    return {
      success: true,
      text: finalResponse,
      toolsUsed,
      sources: (response as any).sources,
      contextUsed: (response as any).contextUsed,
      usage: response.usage
    };
  }

  /**
   * Parse tool parameters from string
   */
  private parseToolParams(paramString: string): any {
    try {
      // Handle empty parameters
      if (!paramString.trim()) {
        return {};
      }
      
      // Try to parse as JSON object
      return JSON.parse(`{${paramString}}`);
    } catch (error) {
      logger.warn(`[EnhancedLLMService] Failed to parse tool params: ${paramString}`);
      return {};
    }
  }

  /**
   * Format context for prompt
   */
  private formatContext(context: CallContext): string {
    return Object.entries(context)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  }

  /**
   * Get agent state for a conversation
   */
  async getAgentState(conversationId: string): Promise<AgentState | null> {
    return await this.agentStateService.getAgentState(conversationId);
  }

  /**
   * Update agent goal
   */
  async updateAgentGoal(conversationId: string, goal: string): Promise<void> {
    const agentState = await this.agentStateService.getAgentState(conversationId);
    if (agentState) {
      agentState.currentGoal = goal;
      await this.agentStateService.updateAgentState(agentState);
      
      // Store goal as important memory
      await this.memoryService.addMemory(
        agentState,
        `New goal set: ${goal}`,
        'insight',
        8
      );
    }
  }

  /**
   * Clear agent memory for a conversation
   */
  async clearAgentMemory(conversationId: string): Promise<void> {
    await this.memoryService.clearMemories(conversationId);
    await this.agentStateService.deleteAgentState(conversationId);
  }

  /**
   * Get available tools for a user
   */
  getAvailableTools(userId: string, category?: string) {
    return this.toolOrchestrator.getAvailableTools(category, userId);
  }

  /**
   * Health check for all agent services
   */
  async healthCheck(): Promise<any> {
    const agentHealth = await this.agentStateService.healthCheck();
    const llmHealth = await LLMOrchestrationService.validateConnection();

    return {
      agent: agentHealth,
      llm: llmHealth,
      overall: agentHealth.redis && agentHealth.database && llmHealth.success
    };
  }

  /**
   * Determine conversation stage based on agent state
   */
  private determineConversationStage(agentState: AgentState): 'opening' | 'discovery' | 'presentation' | 'objection_handling' | 'closing' | 'follow_up' {
    const dealStage = agentState.context.dealStage;
    const messageCount = agentState.memory.shortTerm.length;

    if (dealStage) {
      switch (dealStage.toLowerCase()) {
        case 'discovery': return 'discovery';
        case 'presentation': return 'presentation';
        case 'negotiation': return 'objection_handling';
        case 'closing': return 'closing';
        case 'closed': return 'follow_up';
        default: return messageCount < 5 ? 'opening' : 'discovery';
      }
    }

    // Fallback based on message count
    if (messageCount < 3) return 'opening';
    if (messageCount < 10) return 'discovery';
    if (messageCount < 20) return 'presentation';
    return 'closing';
  }

  /**
   * Calculate conversation time in minutes
   */
  private calculateConversationTime(agentState: AgentState): number {
    const now = new Date();
    const created = agentState.createdAt;
    return Math.round((now.getTime() - created.getTime()) / (1000 * 60));
  }

  /**
   * Execute planning session step
   */
  async executePlanningStep(conversationId: string): Promise<any> {
    const agentState = await this.agentStateService.getAgentState(conversationId);
    if (!agentState) {
      throw new Error('Agent state not found');
    }

    const activeSessions = await this.planningEngine.getActiveSessions(conversationId);
    if (activeSessions.length === 0) {
      return { message: 'No active planning sessions' };
    }

    const session = activeSessions[0];
    const planningContext: PlanningContext = {
      agentState,
      availableTools: this.toolOrchestrator.getAvailableTools().map(t => t.name),
      timeConstraints: { maxDuration: 30 },
      resources: { maxToolCalls: 5, maxSteps: 10 }
    };

    return await this.planningEngine.executeNextStep(session.id, planningContext);
  }

  /**
   * Start reasoning chain
   */
  async startReasoningChain(
    conversationId: string,
    goal: string,
    pattern: 'react' | 'chain_of_thought' | 'tree_of_thoughts' = 'react'
  ): Promise<any> {
    const agentState = await this.agentStateService.getAgentState(conversationId);
    if (!agentState) {
      throw new Error('Agent state not found');
    }

    switch (pattern) {
      case 'react':
        return await this.reasoningEngine.executeReActReasoning(goal, agentState, 5);
      case 'chain_of_thought':
        return await this.reasoningEngine.executeChainOfThought(goal, agentState, 8);
      case 'tree_of_thoughts':
        return await this.reasoningEngine.executeTreeOfThoughts(goal, agentState, 3, 3);
      default:
        throw new Error(`Unknown reasoning pattern: ${pattern}`);
    }
  }

  /**
   * Get proactive suggestions for a conversation
   */
  async getProactiveSuggestions(conversationId: string): Promise<any[]> {
    const agentState = await this.agentStateService.getAgentState(conversationId);
    if (!agentState) {
      return [];
    }

    const suggestionContext: SuggestionContext = {
      agentState,
      conversationStage: this.determineConversationStage(agentState),
      timeInConversation: this.calculateConversationTime(agentState),
      lastActivity: new Date(),
      recentActions: agentState.toolResults?.slice(-5) || [],
      goals: [agentState.currentGoal].filter(Boolean) as string[]
    };

    return await this.proactiveSuggestionEngine.generateSuggestions(suggestionContext);
  }

  /**
   * Execute workflow template
   */
  async executeWorkflow(
    conversationId: string,
    templateId: string,
    parameters: Record<string, any>
  ): Promise<any> {
    const agentState = await this.agentStateService.getAgentState(conversationId);
    if (!agentState) {
      throw new Error('Agent state not found');
    }

    const context = {
      agentState,
      userId: agentState.userId,
      conversationId
    };

    return await this.advancedToolOrchestrator.executeWorkflow(templateId, parameters, context);
  }

  /**
   * Get available workflow templates
   */
  getWorkflowTemplates(category?: string): any[] {
    return this.advancedToolOrchestrator.getWorkflowTemplates(category);
  }

  /**
   * Get active planning sessions
   */
  async getActivePlanningSessions(conversationId: string): Promise<any[]> {
    return await this.planningEngine.getActiveSessions(conversationId);
  }

  /**
   * Get active reasoning chains
   */
  getActiveReasoningChains(conversationId: string): any[] {
    return this.reasoningEngine.getActiveChains(conversationId);
  }

  /**
   * Get suggestion statistics
   */
  getSuggestionStats(conversationId?: string): any {
    return this.proactiveSuggestionEngine.getSuggestionStats(conversationId);
  }
}
