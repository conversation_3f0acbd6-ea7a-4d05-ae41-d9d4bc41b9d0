import { VectorSearchService } from './vectorSearchService';
import { AgentState, Message, WorkingMemoryItem } from './agentStateService';
import { logger } from '../utils/logger';
import { createClient } from '@supabase/supabase-js';
import { OpenAI } from 'openai';

export interface MemoryItem {
  id: string;
  content: string;
  type: 'conversation' | 'insight' | 'preference' | 'outcome' | 'working';
  timestamp: Date;
  importance: number; // 1-10 scale
  embedding?: number[];
  metadata?: any;
}

export interface MemorySearchOptions {
  limit?: number;
  threshold?: number;
  memoryType?: string;
  importanceThreshold?: number;
}

export interface MemorySearchResult extends MemoryItem {
  similarity: number;
}

export interface MemoryServiceDependencies {
  vectorSearchService?: VectorSearchService;
}

/**
 * Memory Service
 * Manages agent memory including short-term, working, and long-term memory
 * Integrates with existing VectorSearchService for semantic memory storage
 */
export class MemoryService {
  private vectorSearch: VectorSearchService;
  private supabase: any;
  private openai: OpenAI;
  private maxShortTermMemory = 20;
  private maxWorkingMemory = 10;
  private importanceThreshold = 7; // Memories with importance >= 7 go to long-term storage

  constructor(dependencies?: MemoryServiceDependencies) {
    this.vectorSearch = dependencies?.vectorSearchService || new VectorSearchService();

    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    logger.info('[MemoryService] Initialized with vector search integration');
  }

  /**
   * Add memory to agent state
   * Manages memory limits and stores important memories in long-term storage
   */
  async addMemory(
    state: AgentState, 
    content: string, 
    type: MemoryItem['type'], 
    importance: number = 5,
    metadata?: any
  ): Promise<void> {
    try {
      const memoryItem: MemoryItem = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content,
        type,
        timestamp: new Date(),
        importance,
        metadata
      };

      // Add to short-term memory
      const message: Message = {
        role: 'system',
        content: `[MEMORY] ${type}: ${content}`,
        timestamp: memoryItem.timestamp,
        metadata: { memoryId: memoryItem.id, importance, type }
      };

      state.memory.shortTerm.push(message);

      // Manage memory limits
      if (state.memory.shortTerm.length > this.maxShortTermMemory) {
        await this.consolidateMemory(state);
      }

      // Store important memories in long-term storage
      if (importance >= this.importanceThreshold) {
        await this.storeInLongTermMemory(state.conversationId, memoryItem);
      }

      logger.info(`[MemoryService] Added ${type} memory (importance: ${importance}) to conversation: ${state.conversationId}`);

    } catch (error: any) {
      logger.error(`[MemoryService] Error adding memory: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieve relevant memories using semantic search
   */
  async retrieveRelevantMemories(
    query: string, 
    conversationId: string, 
    options: MemorySearchOptions = {}
  ): Promise<MemorySearchResult[]> {
    try {
      const {
        limit = 5,
        threshold = 0.75,
        memoryType,
        importanceThreshold = 1
      } = options;

      // Generate query embedding
      const queryEmbedding = await this.generateQueryEmbedding(query);

      // Search agent memories using the stored procedure
      const { data: memories, error } = await this.supabase.rpc(
        'search_agent_memories',
        {
          query_embedding: queryEmbedding,
          conversation_id_filter: conversationId,
          similarity_threshold: threshold,
          match_count: limit,
          memory_type_filter: memoryType,
          importance_threshold: importanceThreshold
        }
      );

      if (error) {
        throw new Error(`Memory search failed: ${error.message}`);
      }

      const results: MemorySearchResult[] = memories.map((memory: any) => ({
        id: memory.id,
        content: memory.content,
        type: memory.memory_type,
        timestamp: new Date(memory.created_at),
        importance: memory.importance,
        similarity: memory.similarity,
        metadata: memory.metadata
      }));

      logger.info(`[MemoryService] Retrieved ${results.length} relevant memories for query: "${query}"`);
      return results;

    } catch (error: any) {
      logger.error(`[MemoryService] Memory retrieval error: ${error.message}`);
      return [];
    }
  }

  /**
   * Add item to working memory
   */
  async addToWorkingMemory(state: AgentState, item: any, type: string = 'working'): Promise<void> {
    try {
      const workingItem: WorkingMemoryItem = {
        id: `working_${Date.now()}`,
        content: typeof item === 'string' ? item : JSON.stringify(item),
        timestamp: new Date(),
        type: type as any,
        metadata: typeof item === 'object' ? item : undefined
      };

      state.memory.workingMemory.push(workingItem);

      // Limit working memory size
      if (state.memory.workingMemory.length > this.maxWorkingMemory) {
        state.memory.workingMemory = state.memory.workingMemory.slice(-this.maxWorkingMemory);
      }

      logger.info(`[MemoryService] Added item to working memory: ${workingItem.id}`);

    } catch (error: any) {
      logger.error(`[MemoryService] Error adding to working memory: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get memory context for prompt building
   */
  async getMemoryContext(
    state: AgentState, 
    query: string, 
    includeRecentMemories: boolean = true
  ): Promise<string> {
    try {
      let context = '';

      // Add recent short-term memories
      if (includeRecentMemories && state.memory.shortTerm.length > 0) {
        const recentMemories = state.memory.shortTerm
          .slice(-5) // Last 5 memories
          .map(m => m.content)
          .join('\n');
        
        context += `Recent Context:\n${recentMemories}\n\n`;
      }

      // Add working memory
      if (state.memory.workingMemory.length > 0) {
        const workingContext = state.memory.workingMemory
          .map(item => `${item.type}: ${item.content}`)
          .join('\n');
        
        context += `Working Memory:\n${workingContext}\n\n`;
      }

      // Add relevant long-term memories
      const relevantMemories = await this.retrieveRelevantMemories(
        query, 
        state.conversationId, 
        { limit: 3, threshold: 0.8 }
      );

      if (relevantMemories.length > 0) {
        const longTermContext = relevantMemories
          .map(m => `${m.type} (importance: ${m.importance}): ${m.content}`)
          .join('\n');
        
        context += `Relevant Memories:\n${longTermContext}\n\n`;
      }

      // Add user preferences if available
      if (state.memory.userPreferences) {
        const preferences = Object.entries(state.memory.userPreferences)
          .map(([key, value]) => `${key}: ${value}`)
          .join(', ');
        
        context += `User Preferences: ${preferences}\n\n`;
      }

      return context.trim();

    } catch (error: any) {
      logger.error(`[MemoryService] Error building memory context: ${error.message}`);
      return '';
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(
    state: AgentState, 
    preferences: Record<string, any>
  ): Promise<void> {
    try {
      state.memory.userPreferences = {
        ...state.memory.userPreferences,
        ...preferences
      };

      // Store as important memory
      await this.addMemory(
        state,
        `User preferences updated: ${JSON.stringify(preferences)}`,
        'preference',
        8,
        { preferences }
      );

      logger.info(`[MemoryService] Updated user preferences for conversation: ${state.conversationId}`);

    } catch (error: any) {
      logger.error(`[MemoryService] Error updating user preferences: ${error.message}`);
      throw error;
    }
  }

  /**
   * Consolidate memory by moving older memories to long-term storage
   */
  private async consolidateMemory(state: AgentState): Promise<void> {
    try {
      // Move older, important memories to long-term storage
      const oldMemories = state.memory.shortTerm.slice(0, -this.maxShortTermMemory);
      
      for (const memory of oldMemories) {
        if (this.isImportantMemory(memory)) {
          const memoryItem: MemoryItem = {
            id: `consolidated_${Date.now()}`,
            content: memory.content,
            type: 'conversation',
            timestamp: memory.timestamp || new Date(),
            importance: 6,
            metadata: memory.metadata
          };

          await this.storeInLongTermMemory(state.conversationId, memoryItem);
        }
      }

      // Keep only recent memories in short-term
      state.memory.shortTerm = state.memory.shortTerm.slice(-this.maxShortTermMemory);

      logger.info(`[MemoryService] Consolidated memory for conversation: ${state.conversationId}`);

    } catch (error: any) {
      logger.error(`[MemoryService] Memory consolidation error: ${error.message}`);
    }
  }

  /**
   * Store memory in long-term vector storage
   */
  private async storeInLongTermMemory(conversationId: string, memory: MemoryItem): Promise<void> {
    try {
      // Generate embedding for the memory
      const embedding = await this.generateQueryEmbedding(memory.content);
      
      // Store in agent_memories table
      const { error } = await this.supabase
        .from('agent_memories')
        .insert({
          id: memory.id,
          conversation_id: conversationId,
          content: memory.content,
          embedding,
          memory_type: memory.type,
          importance: memory.importance,
          metadata: memory.metadata || {},
          created_at: memory.timestamp.toISOString()
        });

      if (error) {
        throw new Error(`Long-term memory storage failed: ${error.message}`);
      }

      logger.info(`[MemoryService] Stored memory in long-term storage: ${memory.id}`);

    } catch (error: any) {
      logger.error(`[MemoryService] Long-term memory storage error: ${error.message}`);
    }
  }

  /**
   * Generate embedding for memory content
   */
  private async generateQueryEmbedding(content: string): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-large',
        input: content,
        dimensions: parseInt(process.env.OPENAI_EMBEDDING_DIMENSIONS || '3072')
      });

      return response.data[0].embedding;

    } catch (error: any) {
      logger.error(`[MemoryService] Embedding generation error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Determine if a memory is important enough for long-term storage
   */
  private isImportantMemory(memory: Message): boolean {
    const importantKeywords = [
      'decision', 'preference', 'objection', 'commitment', 'next steps',
      'budget', 'timeline', 'authority', 'pain point', 'requirement'
    ];
    
    return importantKeywords.some(keyword => 
      memory.content.toLowerCase().includes(keyword)
    );
  }

  /**
   * Clear memories for a conversation (cleanup)
   */
  async clearMemories(conversationId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('agent_memories')
        .delete()
        .eq('conversation_id', conversationId);

      if (error) {
        throw new Error(`Memory cleanup failed: ${error.message}`);
      }

      logger.info(`[MemoryService] Cleared memories for conversation: ${conversationId}`);

    } catch (error: any) {
      logger.error(`[MemoryService] Memory cleanup error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get memory statistics for a conversation
   */
  async getMemoryStats(conversationId: string): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('agent_memories')
        .select('memory_type, importance')
        .eq('conversation_id', conversationId);

      if (error) {
        throw new Error(`Memory stats query failed: ${error.message}`);
      }

      const stats = {
        total: data.length,
        byType: {} as Record<string, number>,
        byImportance: {} as Record<string, number>,
        averageImportance: 0
      };

      let totalImportance = 0;

      for (const memory of data) {
        // Count by type
        stats.byType[memory.memory_type] = (stats.byType[memory.memory_type] || 0) + 1;
        
        // Count by importance
        const importanceRange = memory.importance <= 3 ? 'low' : 
                               memory.importance <= 6 ? 'medium' : 'high';
        stats.byImportance[importanceRange] = (stats.byImportance[importanceRange] || 0) + 1;
        
        totalImportance += memory.importance;
      }

      stats.averageImportance = data.length > 0 ? totalImportance / data.length : 0;

      return stats;

    } catch (error: any) {
      logger.error(`[MemoryService] Memory stats error: ${error.message}`);
      return { total: 0, byType: {}, byImportance: {}, averageImportance: 0 };
    }
  }
}
