import { AgentState } from './agentStateService';
import { VectorSearchService } from './vectorSearchService';
import { logger } from '../utils/logger';
import { createClient } from '@supabase/supabase-js';

export interface Tool {
  name: string;
  description: string;
  parameters: ToolParameter[];
  execute: (params: any, context: ToolContext) => Promise<ToolResult>;
  category: 'crm' | 'communication' | 'analysis' | 'knowledge' | 'utility';
  requiredPermissions?: string[];
  timeout?: number; // milliseconds
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  required: boolean;
  default?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
}

export interface ToolContext {
  agentState: AgentState;
  userId: string;
  conversationId: string;
  requestId?: string;
}

export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: any;
  followUpActions?: string[];
  executionTime?: number;
}

export interface ToolExecutionLog {
  id: string;
  conversationId: string;
  userId: string;
  toolName: string;
  parameters: any;
  result: ToolResult;
  executedAt: Date;
}

export interface ToolOrchestratorDependencies {
  vectorSearchService?: VectorSearchService;
}

/**
 * Tool Orchestrator
 * Manages tool registration, validation, execution, and logging
 */
export class ToolOrchestrator {
  private tools: Map<string, Tool> = new Map();
  private supabase: any;
  private vectorSearch: VectorSearchService;
  private defaultTimeout = 10000; // 10 seconds

  constructor(dependencies?: ToolOrchestratorDependencies) {
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    this.vectorSearch = dependencies?.vectorSearchService || new VectorSearchService();
    this.registerDefaultTools();

    logger.info('[ToolOrchestrator] Initialized with default tools');
  }

  /**
   * Register a new tool
   */
  registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    logger.info(`[ToolOrchestrator] Registered tool: ${tool.name} (${tool.category})`);
  }

  /**
   * Execute a tool with validation and logging
   */
  async executeTool(
    toolName: string, 
    parameters: any, 
    context: ToolContext
  ): Promise<ToolResult> {
    const startTime = Date.now();
    
    try {
      const tool = this.tools.get(toolName);
      if (!tool) {
        return {
          success: false,
          error: `Tool '${toolName}' not found`,
          executionTime: Date.now() - startTime
        };
      }

      // Validate parameters
      const validationResult = this.validateParameters(tool.parameters, parameters);
      if (!validationResult.valid) {
        return {
          success: false,
          error: `Parameter validation failed: ${validationResult.error}`,
          executionTime: Date.now() - startTime
        };
      }

      // Check permissions
      if (tool.requiredPermissions && !this.hasPermissions(context.userId, tool.requiredPermissions)) {
        return {
          success: false,
          error: 'Insufficient permissions to execute this tool',
          executionTime: Date.now() - startTime
        };
      }

      // Execute tool with timeout
      const timeout = tool.timeout || this.defaultTimeout;
      const result = await this.executeWithTimeout(
        () => tool.execute(parameters, context),
        timeout
      );

      result.executionTime = Date.now() - startTime;

      // Log tool execution
      await this.logToolExecution(toolName, parameters, result, context);

      logger.info(`[ToolOrchestrator] Executed ${toolName}: ${result.success ? 'SUCCESS' : 'FAILED'} (${result.executionTime}ms)`);

      return result;

    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      const result: ToolResult = {
        success: false,
        error: `Tool execution failed: ${error.message}`,
        executionTime
      };

      // Log failed execution
      await this.logToolExecution(toolName, parameters, result, context);

      logger.error(`[ToolOrchestrator] Error executing ${toolName}: ${error.message}`);
      return result;
    }
  }

  /**
   * Get available tools, optionally filtered by category
   */
  getAvailableTools(category?: string, userId?: string): Tool[] {
    const allTools = Array.from(this.tools.values());
    let filteredTools = category ? allTools.filter(tool => tool.category === category) : allTools;

    // Filter by permissions if userId provided
    if (userId) {
      filteredTools = filteredTools.filter(tool => 
        !tool.requiredPermissions || this.hasPermissions(userId, tool.requiredPermissions)
      );
    }

    return filteredTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
      category: tool.category,
      requiredPermissions: tool.requiredPermissions,
      timeout: tool.timeout,
      execute: tool.execute // Keep execute function for actual tool objects
    }));
  }

  /**
   * Get tool by name
   */
  getTool(toolName: string): Tool | undefined {
    return this.tools.get(toolName);
  }

  /**
   * Register default tools
   */
  private registerDefaultTools(): void {
    // CRM Search Tool
    this.registerTool({
      name: 'search_crm',
      description: 'Search for customer information in CRM system',
      category: 'crm',
      parameters: [
        { 
          name: 'query', 
          type: 'string', 
          description: 'Search query for customer/prospect information', 
          required: true 
        },
        { 
          name: 'type', 
          type: 'string', 
          description: 'Type of record to search', 
          required: false, 
          default: 'contact',
          validation: { enum: ['contact', 'account', 'opportunity', 'lead'] }
        },
        {
          name: 'limit',
          type: 'number',
          description: 'Maximum number of results',
          required: false,
          default: 5,
          validation: { min: 1, max: 20 }
        }
      ],
      execute: async (params, context) => {
        // Placeholder implementation - would integrate with actual CRM API
        return {
          success: true,
          data: {
            message: `Searching CRM for: ${params.query}`,
            type: params.type,
            results: []
          },
          metadata: { source: 'crm', searchType: params.type }
        };
      }
    });

    // Knowledge Search Tool (RAG Integration)
    this.registerTool({
      name: 'search_knowledge',
      description: 'Search company knowledge base for relevant information',
      category: 'knowledge',
      parameters: [
        { 
          name: 'query', 
          type: 'string', 
          description: 'Search query for knowledge base', 
          required: true 
        },
        { 
          name: 'limit', 
          type: 'number', 
          description: 'Maximum results to return', 
          required: false, 
          default: 5,
          validation: { min: 1, max: 10 }
        },
        {
          name: 'threshold',
          type: 'number',
          description: 'Similarity threshold for results',
          required: false,
          default: 0.75,
          validation: { min: 0.1, max: 1.0 }
        }
      ],
      execute: async (params, context) => {
        try {
          const searchResponse = await this.vectorSearch.search(params.query, {
            limit: params.limit,
            threshold: params.threshold,
            userId: context.userId
          });

          return {
            success: true,
            data: {
              results: searchResponse.results,
              totalFound: searchResponse.totalResults,
              processingTime: searchResponse.processingTime
            },
            metadata: { 
              source: 'knowledge_base', 
              query: params.query,
              threshold: params.threshold
            }
          };
        } catch (error: any) {
          return {
            success: false,
            error: `Knowledge search failed: ${error.message}`
          };
        }
      }
    });

    // Email Tool
    this.registerTool({
      name: 'send_email',
      description: 'Send email to prospect or team member',
      category: 'communication',
      parameters: [
        { 
          name: 'to', 
          type: 'string', 
          description: 'Recipient email address', 
          required: true,
          validation: { pattern: '^[^@]+@[^@]+\.[^@]+$' }
        },
        { 
          name: 'subject', 
          type: 'string', 
          description: 'Email subject line', 
          required: true 
        },
        { 
          name: 'body', 
          type: 'string', 
          description: 'Email body content', 
          required: true 
        },
        { 
          name: 'cc', 
          type: 'array', 
          description: 'CC recipients', 
          required: false 
        },
        {
          name: 'priority',
          type: 'string',
          description: 'Email priority level',
          required: false,
          default: 'normal',
          validation: { enum: ['low', 'normal', 'high'] }
        }
      ],
      execute: async (params, context) => {
        // Placeholder implementation - would integrate with email service
        return {
          success: true,
          data: { 
            message: `Email sent to ${params.to}`,
            messageId: `msg_${Date.now()}`,
            subject: params.subject
          },
          followUpActions: ['log_activity', 'schedule_followup'],
          metadata: { 
            emailService: 'placeholder',
            priority: params.priority
          }
        };
      }
    });

    // Analysis Tool
    this.registerTool({
      name: 'analyze_conversation',
      description: 'Analyze conversation for insights and next steps',
      category: 'analysis',
      parameters: [
        { 
          name: 'conversation_text', 
          type: 'string', 
          description: 'Conversation transcript to analyze', 
          required: true 
        },
        {
          name: 'analysis_type',
          type: 'string',
          description: 'Type of analysis to perform',
          required: false,
          default: 'general',
          validation: { enum: ['general', 'sentiment', 'objections', 'opportunities'] }
        }
      ],
      execute: async (params, context) => {
        // Placeholder implementation - would use AI analysis
        const insights = {
          sentiment: 'positive',
          keyTopics: ['pricing', 'timeline', 'features'],
          objections: [],
          opportunities: ['schedule demo', 'send proposal'],
          nextSteps: ['Follow up on pricing questions', 'Schedule technical demo']
        };

        return {
          success: true,
          data: insights,
          metadata: { 
            analysisType: params.analysis_type,
            conversationLength: params.conversation_text.length
          }
        };
      }
    });

    // Calendar Tool
    this.registerTool({
      name: 'schedule_meeting',
      description: 'Schedule a meeting with prospect or team',
      category: 'utility',
      parameters: [
        { 
          name: 'attendees', 
          type: 'array', 
          description: 'List of attendee email addresses', 
          required: true 
        },
        { 
          name: 'title', 
          type: 'string', 
          description: 'Meeting title', 
          required: true 
        },
        { 
          name: 'duration', 
          type: 'number', 
          description: 'Meeting duration in minutes', 
          required: false, 
          default: 30,
          validation: { min: 15, max: 240 }
        },
        {
          name: 'description',
          type: 'string',
          description: 'Meeting description/agenda',
          required: false
        }
      ],
      execute: async (params, context) => {
        // Placeholder implementation - would integrate with calendar service
        return {
          success: true,
          data: {
            message: `Meeting scheduled: ${params.title}`,
            meetingId: `meeting_${Date.now()}`,
            attendees: params.attendees,
            duration: params.duration
          },
          followUpActions: ['send_calendar_invite', 'log_activity'],
          metadata: { 
            calendarService: 'placeholder',
            attendeeCount: params.attendees.length
          }
        };
      }
    });
  }

  /**
   * Validate tool parameters
   */
  private validateParameters(toolParams: ToolParameter[], providedParams: any): { valid: boolean; error?: string } {
    for (const param of toolParams) {
      if (param.required && !(param.name in providedParams)) {
        return { valid: false, error: `Missing required parameter: ${param.name}` };
      }

      if (param.name in providedParams) {
        const value = providedParams[param.name];
        
        // Type validation
        if (!this.isValidType(value, param.type)) {
          return { valid: false, error: `Parameter ${param.name} must be of type ${param.type}` };
        }

        // Additional validation
        if (param.validation) {
          const validationError = this.validateValue(value, param.validation, param.name);
          if (validationError) {
            return { valid: false, error: validationError };
          }
        }
      }
    }

    return { valid: true };
  }

  /**
   * Validate parameter value against validation rules
   */
  private validateValue(value: any, validation: any, paramName: string): string | null {
    if (validation.min !== undefined && value < validation.min) {
      return `Parameter ${paramName} must be >= ${validation.min}`;
    }
    
    if (validation.max !== undefined && value > validation.max) {
      return `Parameter ${paramName} must be <= ${validation.max}`;
    }
    
    if (validation.pattern && typeof value === 'string') {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        return `Parameter ${paramName} does not match required pattern`;
      }
    }
    
    if (validation.enum && !validation.enum.includes(value)) {
      return `Parameter ${paramName} must be one of: ${validation.enum.join(', ')}`;
    }

    return null;
  }

  /**
   * Check if value matches expected type
   */
  private isValidType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string': return typeof value === 'string';
      case 'number': return typeof value === 'number';
      case 'boolean': return typeof value === 'boolean';
      case 'object': return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array': return Array.isArray(value);
      default: return true;
    }
  }

  /**
   * Check if user has required permissions
   */
  private hasPermissions(userId: string, requiredPermissions: string[]): boolean {
    // Placeholder implementation - would check actual user permissions
    // For now, return true (implement proper permission checking)
    return true;
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T>, 
    timeoutMs: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      fn()
        .then(result => {
          clearTimeout(timeout);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  /**
   * Log tool execution to database
   */
  private async logToolExecution(
    toolName: string,
    parameters: any,
    result: ToolResult,
    context: ToolContext
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('tool_executions')
        .insert({
          conversation_id: context.conversationId,
          user_id: context.userId,
          tool_name: toolName,
          parameters,
          result: result.data,
          success: result.success,
          error_message: result.error,
          execution_time_ms: result.executionTime,
          executed_at: new Date().toISOString()
        });

      if (error) {
        logger.warn(`[ToolOrchestrator] Failed to log tool execution: ${error.message}`);
      }
    } catch (error: any) {
      logger.warn(`[ToolOrchestrator] Tool execution logging failed: ${error.message}`);
    }
  }

  /**
   * Get tool execution history for a conversation
   */
  async getExecutionHistory(conversationId: string, limit: number = 10): Promise<ToolExecutionLog[]> {
    try {
      const { data, error } = await this.supabase
        .from('tool_executions')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('executed_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(`Failed to get execution history: ${error.message}`);
      }

      return data.map((item: any) => ({
        id: item.id,
        conversationId: item.conversation_id,
        userId: item.user_id,
        toolName: item.tool_name,
        parameters: item.parameters,
        result: {
          success: item.success,
          data: item.result,
          error: item.error_message,
          executionTime: item.execution_time_ms
        },
        executedAt: new Date(item.executed_at)
      }));

    } catch (error: any) {
      logger.error(`[ToolOrchestrator] Error getting execution history: ${error.message}`);
      return [];
    }
  }

  /**
   * Get tool usage statistics
   */
  async getToolStats(userId?: string, timeRange?: { start: Date; end: Date }): Promise<any> {
    try {
      let query = this.supabase
        .from('tool_executions')
        .select('tool_name, success, execution_time_ms, executed_at');

      if (userId) {
        query = query.eq('user_id', userId);
      }

      if (timeRange) {
        query = query
          .gte('executed_at', timeRange.start.toISOString())
          .lte('executed_at', timeRange.end.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to get tool stats: ${error.message}`);
      }

      const stats = {
        totalExecutions: data.length,
        successRate: 0,
        averageExecutionTime: 0,
        toolUsage: {} as Record<string, number>,
        failureRate: 0
      };

      if (data.length > 0) {
        const successful = data.filter((item: any) => item.success).length;
        stats.successRate = (successful / data.length) * 100;
        stats.failureRate = 100 - stats.successRate;

        const totalTime = data.reduce((sum: number, item: any) => sum + (item.execution_time_ms || 0), 0);
        stats.averageExecutionTime = totalTime / data.length;

        // Count tool usage
        for (const item of data) {
          stats.toolUsage[item.tool_name] = (stats.toolUsage[item.tool_name] || 0) + 1;
        }
      }

      return stats;

    } catch (error: any) {
      logger.error(`[ToolOrchestrator] Error getting tool stats: ${error.message}`);
      return { totalExecutions: 0, successRate: 0, averageExecutionTime: 0, toolUsage: {}, failureRate: 0 };
    }
  }
}
