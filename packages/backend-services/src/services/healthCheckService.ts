import { OpenAI } from 'openai';
import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { logger } from '../utils/logger';

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  details?: any;
  error?: string;
}

export interface SystemHealthStatus {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: HealthCheckResult[];
  summary: {
    healthy: number;
    unhealthy: number;
    degraded: number;
    total: number;
  };
}

export class HealthCheckService {
  private openai: OpenAI;
  private supabase: any;
  private redis: Redis | null = null;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    this.supabase = createClient(
      process.env.SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );
  }

  /**
   * Perform comprehensive health check of all services
   */
  async performHealthCheck(): Promise<SystemHealthStatus> {
    const startTime = Date.now();
    logger.info('[HealthCheck] Starting comprehensive health check');

    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkOpenAI(),
      this.checkDocumentProcessing(),
      this.checkVectorSearch()
    ]);

    const services: HealthCheckResult[] = checks.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        const serviceNames = ['database', 'redis', 'openai', 'document_processing', 'vector_search'];
        return {
          service: serviceNames[index],
          status: 'unhealthy' as const,
          responseTime: 0,
          error: result.reason?.message || 'Unknown error'
        };
      }
    });

    // Calculate overall status
    const summary = {
      healthy: services.filter(s => s.status === 'healthy').length,
      unhealthy: services.filter(s => s.status === 'unhealthy').length,
      degraded: services.filter(s => s.status === 'degraded').length,
      total: services.length
    };

    let overall: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    if (summary.unhealthy > 0) {
      overall = 'unhealthy';
    } else if (summary.degraded > 0) {
      overall = 'degraded';
    }

    const totalTime = Date.now() - startTime;
    logger.info(`[HealthCheck] Health check completed in ${totalTime}ms - Overall status: ${overall}`);

    return {
      overall,
      timestamp: new Date().toISOString(),
      services,
      summary
    };
  }

  /**
   * Check database connectivity and performance
   */
  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test basic connectivity
      const { data, error } = await this.supabase
        .from('users')
        .select('count')
        .limit(1);

      if (error) {
        throw new Error(`Database query failed: ${error.message}`);
      }

      const responseTime = Date.now() - startTime;
      
      // Check response time thresholds
      let status: 'healthy' | 'degraded' = 'healthy';
      if (responseTime > 1000) {
        status = 'degraded';
      }

      return {
        service: 'database',
        status,
        responseTime,
        details: {
          connected: true,
          url: process.env.SUPABASE_URL
        }
      };
    } catch (error: any) {
      return {
        service: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Check Redis connectivity and performance
   */
  private async checkRedis(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      if (!this.redis) {
        this.redis = new Redis({
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
          connectTimeout: 5000,
          lazyConnect: true
        });
      }

      // Test connectivity with ping
      const result = await this.redis.ping();
      
      if (result !== 'PONG') {
        throw new Error('Redis ping failed');
      }

      const responseTime = Date.now() - startTime;
      
      // Check response time thresholds
      let status: 'healthy' | 'degraded' = 'healthy';
      if (responseTime > 500) {
        status = 'degraded';
      }

      return {
        service: 'redis',
        status,
        responseTime,
        details: {
          connected: true,
          host: process.env.REDIS_HOST,
          port: process.env.REDIS_PORT
        }
      };
    } catch (error: any) {
      return {
        service: 'redis',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Check OpenAI API connectivity and rate limits
   */
  private async checkOpenAI(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test with a simple embedding request
      const response = await this.openai.embeddings.create({
        model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-large',
        input: 'health check test',
        dimensions: parseInt(process.env.OPENAI_EMBEDDING_DIMENSIONS || '1536')
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('OpenAI API returned empty response');
      }

      const responseTime = Date.now() - startTime;
      
      // Check response time thresholds
      let status: 'healthy' | 'degraded' = 'healthy';
      if (responseTime > 5000) {
        status = 'degraded';
      }

      return {
        service: 'openai',
        status,
        responseTime,
        details: {
          connected: true,
          model: process.env.OPENAI_EMBEDDING_MODEL,
          dimensions: response.data[0].embedding.length
        }
      };
    } catch (error: any) {
      let status: 'unhealthy' | 'degraded' = 'unhealthy';
      
      // Check if it's a rate limit error (degraded rather than unhealthy)
      if (error.message?.includes('rate limit') || error.message?.includes('quota')) {
        status = 'degraded';
      }

      return {
        service: 'openai',
        status,
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Check document processing queue health
   */
  private async checkDocumentProcessing(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Check for stuck documents (processing for more than 10 minutes)
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
      
      const { data: stuckDocs, error } = await this.supabase
        .from('documents')
        .select('id')
        .eq('processing_status', 'processing')
        .lt('processed_at', tenMinutesAgo);

      if (error) {
        throw new Error(`Failed to check stuck documents: ${error.message}`);
      }

      const responseTime = Date.now() - startTime;
      const stuckCount = stuckDocs?.length || 0;
      
      let status: 'healthy' | 'degraded' = 'healthy';
      if (stuckCount > 0) {
        status = 'degraded';
      }

      return {
        service: 'document_processing',
        status,
        responseTime,
        details: {
          stuckDocuments: stuckCount,
          queueHealthy: stuckCount === 0
        }
      };
    } catch (error: any) {
      return {
        service: 'document_processing',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Check vector search functionality
   */
  private async checkVectorSearch(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test vector search function exists and is callable
      const { data, error } = await this.supabase.rpc('search_document_chunks', {
        query_embedding: new Array(1536).fill(0.1),
        similarity_threshold: 0.9,
        match_count: 1,
        user_id_filter: null,
        document_ids_filter: null
      });

      if (error) {
        throw new Error(`Vector search function failed: ${error.message}`);
      }

      const responseTime = Date.now() - startTime;
      
      let status: 'healthy' | 'degraded' = 'healthy';
      if (responseTime > 2000) {
        status = 'degraded';
      }

      return {
        service: 'vector_search',
        status,
        responseTime,
        details: {
          functionExists: true,
          searchWorking: true
        }
      };
    } catch (error: any) {
      return {
        service: 'vector_search',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
      this.redis = null;
    }
  }
}

// Singleton instance
let healthCheckService: HealthCheckService | null = null;

export function getHealthCheckService(): HealthCheckService {
  if (!healthCheckService) {
    healthCheckService = new HealthCheckService();
  }
  return healthCheckService;
}
