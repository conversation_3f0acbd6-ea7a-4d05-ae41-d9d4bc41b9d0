import { createClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger';
import { getHealthCheckService } from './healthCheckService';

export interface ProcessingMetrics {
  totalDocuments: number;
  successfulProcessing: number;
  failedProcessing: number;
  averageProcessingTime: number;
  queueLength: number;
  stuckDocuments: number;
}

export interface SystemMetrics {
  timestamp: string;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  uptime: number;
  activeConnections: number;
  errorRate: number;
}

export interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info';
  service: string;
  message: string;
  timestamp: string;
  resolved: boolean;
  metadata?: any;
}

export class MonitoringService {
  private supabase: any;
  private alerts: Alert[] = [];
  private metrics: SystemMetrics[] = [];
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || ''
    );
  }

  /**
   * Start monitoring system
   */
  startMonitoring(intervalMs: number = 60000): void {
    if (this.isMonitoring) {
      logger.warn('[Monitoring] Monitoring already started');
      return;
    }

    this.isMonitoring = true;
    logger.info(`[Monitoring] Starting system monitoring with ${intervalMs}ms interval`);

    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectMetrics();
        await this.checkAlerts();
      } catch (error: any) {
        logger.error(`[Monitoring] Error during monitoring cycle: ${error.message}`);
      }
    }, intervalMs);

    // Initial metrics collection
    this.collectMetrics().catch(error => {
      logger.error(`[Monitoring] Initial metrics collection failed: ${error.message}`);
    });
  }

  /**
   * Stop monitoring system
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    logger.info('[Monitoring] System monitoring stopped');
  }

  /**
   * Collect system and processing metrics
   */
  async collectMetrics(): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      
      // Collect system metrics
      const systemMetrics: SystemMetrics = {
        timestamp,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        uptime: process.uptime(),
        activeConnections: 0, // Would need to track this in your app
        errorRate: await this.calculateErrorRate()
      };

      this.metrics.push(systemMetrics);

      // Keep only last 100 metrics entries
      if (this.metrics.length > 100) {
        this.metrics = this.metrics.slice(-100);
      }

      // Log key metrics
      const memoryMB = Math.round(systemMetrics.memoryUsage.heapUsed / 1024 / 1024);
      logger.info(`[Monitoring] Memory: ${memoryMB}MB, Uptime: ${Math.round(systemMetrics.uptime)}s, Error Rate: ${systemMetrics.errorRate.toFixed(2)}%`);

    } catch (error: any) {
      logger.error(`[Monitoring] Failed to collect metrics: ${error.message}`);
    }
  }

  /**
   * Check for alert conditions
   */
  async checkAlerts(): Promise<void> {
    try {
      // Check memory usage
      const currentMetrics = this.metrics[this.metrics.length - 1];
      if (currentMetrics) {
        const memoryUsageMB = currentMetrics.memoryUsage.heapUsed / 1024 / 1024;
        
        if (memoryUsageMB > 3000) { // 3GB threshold
          this.createAlert('error', 'system', `High memory usage: ${Math.round(memoryUsageMB)}MB`, {
            memoryUsage: currentMetrics.memoryUsage
          });
        }

        if (currentMetrics.errorRate > 10) { // 10% error rate threshold
          this.createAlert('warning', 'system', `High error rate: ${currentMetrics.errorRate.toFixed(2)}%`, {
            errorRate: currentMetrics.errorRate
          });
        }
      }

      // Check processing metrics
      const processingMetrics = await this.getProcessingMetrics();
      
      if (processingMetrics.stuckDocuments > 0) {
        this.createAlert('warning', 'document_processing', `${processingMetrics.stuckDocuments} documents stuck in processing`, {
          stuckDocuments: processingMetrics.stuckDocuments
        });
      }

      if (processingMetrics.queueLength > 50) {
        this.createAlert('warning', 'document_processing', `Large processing queue: ${processingMetrics.queueLength} documents`, {
          queueLength: processingMetrics.queueLength
        });
      }

      // Check service health
      const healthService = getHealthCheckService();
      const healthStatus = await healthService.performHealthCheck();
      
      for (const service of healthStatus.services) {
        if (service.status === 'unhealthy') {
          this.createAlert('error', service.service, `Service unhealthy: ${service.error}`, {
            responseTime: service.responseTime,
            details: service.details
          });
        } else if (service.status === 'degraded') {
          this.createAlert('warning', service.service, `Service degraded: slow response time ${service.responseTime}ms`, {
            responseTime: service.responseTime,
            details: service.details
          });
        }
      }

    } catch (error: any) {
      logger.error(`[Monitoring] Failed to check alerts: ${error.message}`);
    }
  }

  /**
   * Create a new alert
   */
  private createAlert(type: 'error' | 'warning' | 'info', service: string, message: string, metadata?: any): void {
    const alertId = `${service}-${Date.now()}`;
    
    // Check if similar alert already exists and is unresolved
    const existingAlert = this.alerts.find(alert => 
      !alert.resolved && 
      alert.service === service && 
      alert.message === message
    );

    if (existingAlert) {
      return; // Don't create duplicate alerts
    }

    const alert: Alert = {
      id: alertId,
      type,
      service,
      message,
      timestamp: new Date().toISOString(),
      resolved: false,
      metadata
    };

    this.alerts.push(alert);

    // Log alert
    const logLevel = type === 'error' ? 'error' : type === 'warning' ? 'warn' : 'info';
    logger[logLevel](`[Monitoring] ALERT [${type.toUpperCase()}] ${service}: ${message}`);

    // Keep only last 200 alerts
    if (this.alerts.length > 200) {
      this.alerts = this.alerts.slice(-200);
    }

    // TODO: Send to external monitoring service (e.g., Slack, PagerDuty)
    // this.sendExternalAlert(alert);
  }

  /**
   * Get processing metrics
   */
  async getProcessingMetrics(): Promise<ProcessingMetrics> {
    try {
      // Get document counts by status
      const { data: documents, error } = await this.supabase
        .from('documents')
        .select('processing_status, created_at, processed_at');

      if (error) {
        throw new Error(`Failed to get processing metrics: ${error.message}`);
      }

      const totalDocuments = documents?.length || 0;
      const successfulProcessing = documents?.filter((d: any) => d.processing_status === 'completed').length || 0;
      const failedProcessing = documents?.filter((d: any) => d.processing_status === 'failed').length || 0;
      const queueLength = documents?.filter((d: any) => d.processing_status === 'queued').length || 0;

      // Calculate stuck documents (processing for more than 10 minutes)
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
      const stuckDocuments = documents?.filter((d: any) =>
        d.processing_status === 'processing' &&
        new Date(d.processed_at || d.created_at) < tenMinutesAgo
      ).length || 0;

      // Calculate average processing time for completed documents
      const completedDocs = documents?.filter((d: any) =>
        d.processing_status === 'completed' &&
        d.processed_at &&
        d.created_at
      ) || [];

      let averageProcessingTime = 0;
      if (completedDocs.length > 0) {
        const totalProcessingTime = completedDocs.reduce((sum: number, doc: any) => {
          const start = new Date(doc.created_at).getTime();
          const end = new Date(doc.processed_at).getTime();
          return sum + (end - start);
        }, 0);
        averageProcessingTime = totalProcessingTime / completedDocs.length;
      }

      return {
        totalDocuments,
        successfulProcessing,
        failedProcessing,
        averageProcessingTime,
        queueLength,
        stuckDocuments
      };

    } catch (error: any) {
      logger.error(`[Monitoring] Failed to get processing metrics: ${error.message}`);
      return {
        totalDocuments: 0,
        successfulProcessing: 0,
        failedProcessing: 0,
        averageProcessingTime: 0,
        queueLength: 0,
        stuckDocuments: 0
      };
    }
  }

  /**
   * Calculate error rate from recent logs
   */
  private async calculateErrorRate(): Promise<number> {
    // This is a simplified implementation
    // In a real system, you'd track request/error counts
    try {
      const recentMetrics = this.metrics.slice(-10); // Last 10 metrics
      if (recentMetrics.length === 0) return 0;

      // For now, return 0 as we don't have error tracking implemented
      return 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get current alerts
   */
  getAlerts(resolved: boolean = false): Alert[] {
    return this.alerts.filter(alert => alert.resolved === resolved);
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      logger.info(`[Monitoring] Alert resolved: ${alertId}`);
      return true;
    }
    return false;
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics(): SystemMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  /**
   * Get metrics history
   */
  getMetricsHistory(limit: number = 50): SystemMetrics[] {
    return this.metrics.slice(-limit);
  }
}

// Singleton instance
let monitoringService: MonitoringService | null = null;

export function getMonitoringService(): MonitoringService {
  if (!monitoringService) {
    monitoringService = new MonitoringService();
  }
  return monitoringService;
}
