import { AgentState } from './agentStateService';
import { MemoryService } from './memoryService';
import { ToolOrchestrator } from './toolOrchestrator';
import { ReasoningEngine } from './reasoningEngine';
import { LLMOrchestrationService } from './llmOrchestrationService';
import { logger } from '../utils/logger';
import { createClient } from '@supabase/supabase-js';

// Suggestion interfaces
export interface Suggestion {
  id: string;
  type: 'action' | 'information' | 'strategy' | 'warning' | 'opportunity';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  reasoning: string;
  confidence: number; // 0-1 scale
  actionable: boolean;
  suggestedActions?: SuggestedAction[];
  context?: any;
  expiresAt?: Date;
  createdAt: Date;
}

export interface SuggestedAction {
  id: string;
  description: string;
  toolName?: string;
  parameters?: any;
  estimatedDuration?: number; // minutes
  expectedOutcome: string;
  confidence: number;
}

export interface SuggestionContext {
  agentState: AgentState;
  conversationStage: 'opening' | 'discovery' | 'presentation' | 'objection_handling' | 'closing' | 'follow_up';
  timeInConversation: number; // minutes
  lastActivity: Date;
  recentActions: any[];
  goals: string[];
}

export interface SuggestionPattern {
  name: string;
  description: string;
  triggers: string[];
  conditions: (context: SuggestionContext) => boolean;
  generator: (context: SuggestionContext) => Promise<Suggestion[]>;
}

/**
 * Proactive Suggestion Engine
 * Analyzes conversation patterns and suggests next best actions
 * based on context, goals, and historical patterns
 */
export class ProactiveSuggestionEngine {
  private memoryService: MemoryService;
  private toolOrchestrator: ToolOrchestrator;
  private reasoningEngine: ReasoningEngine;
  private supabase: any;
  private suggestionPatterns: Map<string, SuggestionPattern> = new Map();
  private activeSuggestions: Map<string, Suggestion[]> = new Map();

  constructor() {
    this.memoryService = new MemoryService();
    this.toolOrchestrator = new ToolOrchestrator();
    this.reasoningEngine = new ReasoningEngine();
    
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    this.initializeSuggestionPatterns();
    
    logger.info('[ProactiveSuggestionEngine] Initialized with pattern-based suggestion generation');
  }

  /**
   * Generate proactive suggestions based on current context
   */
  async generateSuggestions(context: SuggestionContext): Promise<Suggestion[]> {
    try {
      const suggestions: Suggestion[] = [];

      // Run all applicable suggestion patterns
      for (const pattern of this.suggestionPatterns.values()) {
        try {
          if (pattern.conditions(context)) {
            const patternSuggestions = await pattern.generator(context);
            suggestions.push(...patternSuggestions);
          }
        } catch (error: any) {
          logger.warn(`[ProactiveSuggestionEngine] Pattern ${pattern.name} failed: ${error.message}`);
        }
      }

      // Analyze conversation for additional suggestions
      const conversationSuggestions = await this.analyzeConversationForSuggestions(context);
      suggestions.push(...conversationSuggestions);

      // Rank and filter suggestions
      const rankedSuggestions = this.rankSuggestions(suggestions, context);
      const filteredSuggestions = this.filterSuggestions(rankedSuggestions, context);

      // Store suggestions
      this.activeSuggestions.set(context.agentState.conversationId, filteredSuggestions);

      // Add to agent memory
      if (filteredSuggestions.length > 0) {
        await this.memoryService.addMemory(
          context.agentState,
          `Generated ${filteredSuggestions.length} proactive suggestions`,
          'insight',
          6,
          { suggestionCount: filteredSuggestions.length, stage: context.conversationStage }
        );
      }

      logger.info(`[ProactiveSuggestionEngine] Generated ${filteredSuggestions.length} suggestions for conversation: ${context.agentState.conversationId}`);
      return filteredSuggestions;

    } catch (error: any) {
      logger.error(`[ProactiveSuggestionEngine] Error generating suggestions: ${error.message}`);
      return [];
    }
  }

  /**
   * Initialize built-in suggestion patterns
   */
  private initializeSuggestionPatterns(): void {
    // Conversation stagnation pattern
    this.suggestionPatterns.set('stagnation', {
      name: 'Conversation Stagnation',
      description: 'Detect when conversation is stagnating and suggest re-engagement',
      triggers: ['long_pause', 'repetitive_responses', 'low_engagement'],
      conditions: (context) => {
        const timeSinceLastActivity = Date.now() - context.lastActivity.getTime();
        return timeSinceLastActivity > 5 * 60 * 1000; // 5 minutes
      },
      generator: async (context) => {
        return [{
          id: `stagnation_${Date.now()}`,
          type: 'strategy',
          priority: 'medium',
          title: 'Re-engage the conversation',
          description: 'The conversation has been quiet. Consider asking an engaging question or sharing relevant insights.',
          reasoning: 'Long periods of silence can indicate lost interest or confusion',
          confidence: 0.8,
          actionable: true,
          suggestedActions: [
            {
              id: 'ask_question',
              description: 'Ask an open-ended question about their challenges',
              expectedOutcome: 'Restart meaningful dialogue',
              confidence: 0.7
            },
            {
              id: 'share_insight',
              description: 'Share a relevant industry insight or case study',
              expectedOutcome: 'Provide value and spark interest',
              confidence: 0.8
            }
          ],
          createdAt: new Date()
        }];
      }
    });

    // Objection handling pattern
    this.suggestionPatterns.set('objection_detected', {
      name: 'Objection Detection',
      description: 'Detect objections and suggest handling strategies',
      triggers: ['negative_sentiment', 'concern_keywords', 'hesitation'],
      conditions: (context) => {
        const recentMessages = context.agentState.memory.shortTerm.slice(-3);
        return recentMessages.some(msg => 
          msg.content.toLowerCase().includes('but') ||
          msg.content.toLowerCase().includes('however') ||
          msg.content.toLowerCase().includes('concern')
        );
      },
      generator: async (context) => {
        return [{
          id: `objection_${Date.now()}`,
          type: 'strategy',
          priority: 'high',
          title: 'Address potential objection',
          description: 'An objection or concern may have been raised. Consider acknowledging and addressing it directly.',
          reasoning: 'Unaddressed objections can derail sales conversations',
          confidence: 0.9,
          actionable: true,
          suggestedActions: [
            {
              id: 'acknowledge_concern',
              description: 'Acknowledge their concern and ask for clarification',
              expectedOutcome: 'Show empathy and gather more information',
              confidence: 0.9
            },
            {
              id: 'provide_evidence',
              description: 'Provide evidence or case studies that address the concern',
              toolName: 'search_knowledge',
              parameters: { query: 'objection handling case studies' },
              expectedOutcome: 'Build credibility and address concerns',
              confidence: 0.8
            }
          ],
          createdAt: new Date()
        }];
      }
    });

    // Opportunity identification pattern
    this.suggestionPatterns.set('opportunity_identification', {
      name: 'Opportunity Identification',
      description: 'Identify sales opportunities based on conversation cues',
      triggers: ['pain_point_mentioned', 'budget_discussed', 'timeline_mentioned'],
      conditions: (context) => {
        const recentMessages = context.agentState.memory.shortTerm.slice(-5);
        const opportunityKeywords = ['budget', 'timeline', 'decision', 'problem', 'challenge', 'need'];
        return recentMessages.some(msg => 
          opportunityKeywords.some(keyword => 
            msg.content.toLowerCase().includes(keyword)
          )
        );
      },
      generator: async (context) => {
        return [{
          id: `opportunity_${Date.now()}`,
          type: 'opportunity',
          priority: 'high',
          title: 'Sales opportunity identified',
          description: 'The prospect has mentioned key buying signals. Consider advancing the conversation.',
          reasoning: 'Buying signals indicate readiness to move forward',
          confidence: 0.85,
          actionable: true,
          suggestedActions: [
            {
              id: 'qualify_opportunity',
              description: 'Ask qualifying questions about budget, authority, need, and timeline',
              expectedOutcome: 'Better understand the opportunity size and likelihood',
              confidence: 0.9
            },
            {
              id: 'propose_next_steps',
              description: 'Suggest concrete next steps like a demo or proposal',
              expectedOutcome: 'Move the opportunity forward in the sales process',
              confidence: 0.8
            }
          ],
          createdAt: new Date()
        }];
      }
    });

    // Information gap pattern
    this.suggestionPatterns.set('information_gap', {
      name: 'Information Gap',
      description: 'Identify when more information is needed',
      triggers: ['incomplete_context', 'missing_details', 'unclear_requirements'],
      conditions: (context) => {
        const hasCompanyInfo = context.agentState.context.companyName;
        const hasContactInfo = context.agentState.context.prospectName;
        const hasStageInfo = context.agentState.context.dealStage;
        return !hasCompanyInfo || !hasContactInfo || !hasStageInfo;
      },
      generator: async (context) => {
        const missingSuggestions: Suggestion[] = [];

        if (!context.agentState.context.companyName) {
          missingSuggestions.push({
            id: `info_company_${Date.now()}`,
            type: 'information',
            priority: 'medium',
            title: 'Gather company information',
            description: 'Company details are missing. Consider researching the prospect\'s company.',
            reasoning: 'Company context helps personalize the conversation',
            confidence: 0.7,
            actionable: true,
            suggestedActions: [{
              id: 'search_company',
              description: 'Search for company information',
              toolName: 'search_crm',
              parameters: { query: 'company research', type: 'account' },
              expectedOutcome: 'Better understanding of prospect\'s business',
              confidence: 0.8
            }],
            createdAt: new Date()
          });
        }

        return missingSuggestions;
      }
    });

    // Follow-up timing pattern
    this.suggestionPatterns.set('follow_up_timing', {
      name: 'Follow-up Timing',
      description: 'Suggest optimal follow-up timing',
      triggers: ['conversation_end', 'commitment_made', 'next_steps_discussed'],
      conditions: (context) => {
        return context.conversationStage === 'follow_up' || 
               context.timeInConversation > 30; // 30+ minutes
      },
      generator: async (context) => {
        return [{
          id: `followup_${Date.now()}`,
          type: 'action',
          priority: 'medium',
          title: 'Schedule follow-up',
          description: 'Consider scheduling a follow-up meeting or call to maintain momentum.',
          reasoning: 'Timely follow-up is crucial for sales success',
          confidence: 0.8,
          actionable: true,
          suggestedActions: [
            {
              id: 'schedule_meeting',
              description: 'Schedule a follow-up meeting',
              toolName: 'schedule_meeting',
              parameters: { 
                title: 'Follow-up Discussion',
                duration: 30,
                attendees: [context.agentState.context.prospectName]
              },
              expectedOutcome: 'Maintain engagement and move opportunity forward',
              confidence: 0.9
            },
            {
              id: 'send_summary',
              description: 'Send a conversation summary and next steps',
              toolName: 'send_email',
              expectedOutcome: 'Reinforce key points and commitments',
              confidence: 0.8
            }
          ],
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
          createdAt: new Date()
        }];
      }
    });
  }

  /**
   * Analyze conversation for additional suggestions using LLM
   */
  private async analyzeConversationForSuggestions(context: SuggestionContext): Promise<Suggestion[]> {
    try {
      const recentContext = context.agentState.memory.shortTerm
        .slice(-5)
        .map(m => m.content)
        .join('\n');

      const prompt = `Analyze this sales conversation context and suggest proactive actions:

CONVERSATION STAGE: ${context.conversationStage}
TIME IN CONVERSATION: ${context.timeInConversation} minutes
RECENT CONTEXT:
${recentContext}

CURRENT GOALS: ${context.goals.join(', ')}

Based on this context, what proactive suggestions would help advance the sales conversation?
Consider:
- Opportunities to add value
- Information gaps to fill
- Next logical steps
- Potential risks to address

Respond with JSON array of suggestions:
[
  {
    "type": "action|information|strategy|warning|opportunity",
    "priority": "low|medium|high|urgent",
    "title": "Brief title",
    "description": "Detailed description",
    "reasoning": "Why this suggestion is relevant",
    "confidence": 0.0-1.0,
    "actionable": true/false
  }
]`;

      const response = await LLMOrchestrationService.generateText({
        prompt,
        temperature: 0.6,
        maxOutputTokens: 500
      });

      if (!response.success) {
        return [];
      }

      const suggestions = this.parseSuggestionsFromResponse(response.text!);
      return suggestions.map((s: any, index: number) => ({
        id: `llm_suggestion_${Date.now()}_${index}`,
        ...s,
        createdAt: new Date()
      }));

    } catch (error: any) {
      logger.warn(`[ProactiveSuggestionEngine] LLM analysis failed: ${error.message}`);
      return [];
    }
  }

  /**
   * Parse suggestions from LLM response
   */
  private parseSuggestionsFromResponse(response: string): any[] {
    try {
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return [];
    } catch (error) {
      logger.warn('[ProactiveSuggestionEngine] Failed to parse LLM suggestions');
      return [];
    }
  }

  /**
   * Rank suggestions by relevance and priority
   */
  private rankSuggestions(suggestions: Suggestion[], context: SuggestionContext): Suggestion[] {
    return suggestions.sort((a, b) => {
      // Priority weights
      const priorityWeights = { urgent: 4, high: 3, medium: 2, low: 1 };
      
      // Calculate score
      const scoreA = priorityWeights[a.priority] * a.confidence;
      const scoreB = priorityWeights[b.priority] * b.confidence;
      
      return scoreB - scoreA;
    });
  }

  /**
   * Filter suggestions to avoid duplicates and low-quality suggestions
   */
  private filterSuggestions(suggestions: Suggestion[], context: SuggestionContext): Suggestion[] {
    const filtered: Suggestion[] = [];
    const seenTypes = new Set<string>();

    for (const suggestion of suggestions) {
      // Skip low confidence suggestions
      if (suggestion.confidence < 0.5) continue;

      // Skip duplicate types (keep highest priority)
      const typeKey = `${suggestion.type}_${suggestion.title}`;
      if (seenTypes.has(typeKey)) continue;
      seenTypes.add(typeKey);

      // Skip expired suggestions
      if (suggestion.expiresAt && suggestion.expiresAt < new Date()) continue;

      filtered.push(suggestion);
    }

    // Limit to top 5 suggestions
    return filtered.slice(0, 5);
  }

  /**
   * Get active suggestions for a conversation
   */
  getActiveSuggestions(conversationId: string): Suggestion[] {
    return this.activeSuggestions.get(conversationId) || [];
  }

  /**
   * Mark a suggestion as acted upon
   */
  async markSuggestionActedUpon(
    conversationId: string, 
    suggestionId: string, 
    action: string,
    result: any
  ): Promise<void> {
    const suggestions = this.activeSuggestions.get(conversationId) || [];
    const suggestion = suggestions.find(s => s.id === suggestionId);
    
    if (suggestion) {
      // Remove from active suggestions
      const updatedSuggestions = suggestions.filter(s => s.id !== suggestionId);
      this.activeSuggestions.set(conversationId, updatedSuggestions);

      // Log the action for learning
      logger.info(`[ProactiveSuggestionEngine] Suggestion acted upon: ${suggestionId} -> ${action}`);
    }
  }

  /**
   * Clear suggestions for a conversation
   */
  clearSuggestions(conversationId: string): void {
    this.activeSuggestions.delete(conversationId);
  }

  /**
   * Get suggestion statistics
   */
  getSuggestionStats(conversationId?: string): any {
    if (conversationId) {
      const suggestions = this.activeSuggestions.get(conversationId) || [];
      return {
        total: suggestions.length,
        byType: this.groupBy(suggestions, 'type'),
        byPriority: this.groupBy(suggestions, 'priority'),
        averageConfidence: suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length || 0
      };
    } else {
      const allSuggestions = Array.from(this.activeSuggestions.values()).flat();
      return {
        totalConversations: this.activeSuggestions.size,
        totalSuggestions: allSuggestions.length,
        byType: this.groupBy(allSuggestions, 'type'),
        byPriority: this.groupBy(allSuggestions, 'priority'),
        averageConfidence: allSuggestions.reduce((sum, s) => sum + s.confidence, 0) / allSuggestions.length || 0
      };
    }
  }

  /**
   * Helper function to group array by property
   */
  private groupBy(array: any[], property: string): Record<string, number> {
    return array.reduce((groups, item) => {
      const key = item[property];
      groups[key] = (groups[key] || 0) + 1;
      return groups;
    }, {});
  }

  /**
   * Register a custom suggestion pattern
   */
  registerSuggestionPattern(pattern: SuggestionPattern): void {
    this.suggestionPatterns.set(pattern.name, pattern);
    logger.info(`[ProactiveSuggestionEngine] Registered custom pattern: ${pattern.name}`);
  }

  /**
   * Remove a suggestion pattern
   */
  removeSuggestionPattern(patternName: string): void {
    this.suggestionPatterns.delete(patternName);
    logger.info(`[ProactiveSuggestionEngine] Removed pattern: ${patternName}`);
  }
}
