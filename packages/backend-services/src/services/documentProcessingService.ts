import { OpenAI } from 'openai';
import pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import { createClient } from '@supabase/supabase-js';
import Bull from 'bull';
import { logger } from '../utils/logger';

// Memory management and performance constants
const MAX_CHUNK_BATCH_SIZE = 10; // Process embeddings in smaller batches
const MAX_DOCUMENT_SIZE_MB = 50;
const EMBEDDING_RETRY_ATTEMPTS = 3;
const EMBEDDING_RETRY_DELAY_MS = 1000;
const PROCESSING_TIMEOUT_MS = 300000; // 5 minutes

export interface DocumentMetadata {
  filename: string;
  mimetype: string;
  size: number;
  uploadedAt: Date;
  userId: string;
  processingStatus: 'queued' | 'processing' | 'completed' | 'failed';
}

export interface ProcessingResult {
  jobId: string;
  status: string;
  documentId?: string;
  error?: string;
  retryCount?: number;
  estimatedProcessingTime?: number;
}

export interface ProcessingError {
  code: string;
  message: string;
  retryable: boolean;
  timestamp: Date;
}

export class DocumentProcessingService {
  private openai: OpenAI;
  public supabase: any;
  private processingQueue: any;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Initialize Bull queue lazily to avoid connection issues during import
    this.processingQueue = null;
  }

  /**
   * Initialize the processing queue lazily
   */
  private initializeQueue() {
    if (!this.processingQueue) {
      this.processingQueue = Bull('document processing', {
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
        }
      });

      // Set up queue processing
      this.setupQueueProcessing();
    }
    return this.processingQueue;
  }

  /**
   * Add document to processing queue with enhanced validation and error handling
   */
  async processDocument(file: Express.Multer.File, userId: string): Promise<ProcessingResult> {
    try {
      logger.info(`[DocumentProcessing] Queuing document: ${file.originalname} (${file.size} bytes) for user: ${userId}`);

      // Enhanced validation
      await this.validateDocument(file);

      // Check if user exists and is valid
      await this.validateUser(userId);

      // Create document record in database
      const insertData = {
        title: file.originalname, // Use filename as title
        description: `Uploaded document: ${file.originalname}`,
        filename: file.originalname,
        mimetype: file.mimetype,
        file_type: file.mimetype, // Set file_type to mimetype for web portal display
        size: file.size,
        user_id: userId,
        processing_status: 'queued',
        uploaded_at: new Date().toISOString()
      };

      logger.info(`[DocumentProcessing] Inserting document with data:`, insertData);

      const { data: document, error } = await this.supabase
        .from('documents')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // Add to processing queue
      // Convert Buffer to base64 string for proper serialization through Redis
      const queue = this.initializeQueue();
      const job = await queue.add('process', {
        documentId: document.id,
        fileBuffer: file.buffer.toString('base64'),
        filename: file.originalname,
        mimetype: file.mimetype,
        userId
      });

      logger.info(`[DocumentProcessing] Document queued with job ID: ${job.id}`);

      return { 
        jobId: job.id.toString(), 
        status: 'queued',
        documentId: document.id
      };
    } catch (error: any) {
      logger.error(`[DocumentProcessing] Error queuing document: ${error.message}`);
      throw error;
    }
  }

  /**
   * Extract text from various file formats
   */
  async extractText(buffer: Buffer, mimetype: string): Promise<string> {
    try {
      switch (mimetype) {
        case 'application/pdf':
          const pdfData = await pdfParse(buffer);
          return pdfData.text;
        
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          const docxResult = await mammoth.extractRawText({ buffer });
          return docxResult.value;
        
        case 'text/plain':
          return buffer.toString('utf-8');
        
        case 'text/html':
          // Basic HTML text extraction (remove tags)
          const htmlText = buffer.toString('utf-8');
          return htmlText.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
        
        default:
          throw new Error(`Unsupported file type: ${mimetype}`);
      }
    } catch (error: any) {
      logger.error(`[DocumentProcessing] Text extraction failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Split text into chunks for embedding
   */
  chunkText(text: string, chunkSize: number = 1000, overlap: number = 200): string[] {
    const chunks: string[] = [];
    let start = 0;

    // Clean and normalize text
    const cleanText = text.replace(/\s+/g, ' ').trim();

    while (start < cleanText.length) {
      const end = Math.min(start + chunkSize, cleanText.length);
      let chunk = cleanText.slice(start, end);

      // Try to break at sentence boundaries
      if (end < cleanText.length) {
        const lastSentence = chunk.lastIndexOf('.');
        const lastNewline = chunk.lastIndexOf('\n');
        const breakPoint = Math.max(lastSentence, lastNewline);
        
        if (breakPoint > start + chunkSize * 0.5) {
          chunk = cleanText.slice(start, breakPoint + 1);
          start = breakPoint + 1 - overlap;
        } else {
          start = end - overlap;
        }
      } else {
        start = end;
      }

      if (chunk.trim().length > 0) {
        chunks.push(chunk.trim());
      }
    }

    return chunks;
  }

  /**
   * Generate embeddings for text chunks with memory management and retry logic
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    try {
      logger.info(`[DocumentProcessing] Generating embeddings for ${texts.length} chunks`);

      // Process in smaller batches to manage memory
      const embeddings: number[][] = [];
      const batchSize = Math.min(MAX_CHUNK_BATCH_SIZE, texts.length);

      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        logger.info(`[DocumentProcessing] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(texts.length / batchSize)} (${batch.length} chunks)`);

        const batchEmbeddings = await this.generateEmbeddingsBatch(batch);
        embeddings.push(...batchEmbeddings);

        // Force garbage collection between batches if available
        if (global.gc) {
          global.gc();
        }

        // Small delay to prevent rate limiting
        if (i + batchSize < texts.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      logger.info(`[DocumentProcessing] Successfully generated ${embeddings.length} embeddings`);
      return embeddings;
    } catch (error: any) {
      logger.error(`[DocumentProcessing] Embedding generation failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate embeddings for a single batch with retry logic
   */
  private async generateEmbeddingsBatch(texts: string[], retryCount = 0): Promise<number[][]> {
    try {
      const response = await this.openai.embeddings.create({
        model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-large',
        input: texts,
        dimensions: parseInt(process.env.OPENAI_EMBEDDING_DIMENSIONS || '1536')
      });

      // Validate embeddings
      const embeddings = response.data.map(item => item.embedding);
      this.validateEmbeddings(embeddings);

      return embeddings;
    } catch (error: any) {
      if (retryCount < EMBEDDING_RETRY_ATTEMPTS) {
        const delay = EMBEDDING_RETRY_DELAY_MS * Math.pow(2, retryCount);
        logger.warn(`[DocumentProcessing] Embedding batch failed, retrying in ${delay}ms (attempt ${retryCount + 1}/${EMBEDDING_RETRY_ATTEMPTS}): ${error.message}`);

        await new Promise(resolve => setTimeout(resolve, delay));
        return this.generateEmbeddingsBatch(texts, retryCount + 1);
      }

      throw new Error(`Embedding generation failed after ${EMBEDDING_RETRY_ATTEMPTS} attempts: ${error.message}`);
    }
  }

  /**
   * Enhanced document validation
   */
  private async validateDocument(file: Express.Multer.File): Promise<void> {
    // Check file type
    if (!this.isSupportedFileType(file.mimetype)) {
      throw new Error(`Unsupported file type: ${file.mimetype}. Supported types: PDF, DOCX, TXT, HTML`);
    }

    // Check file size
    const maxSizeBytes = MAX_DOCUMENT_SIZE_MB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      throw new Error(`File size ${Math.round(file.size / 1024 / 1024)}MB exceeds maximum allowed size of ${MAX_DOCUMENT_SIZE_MB}MB`);
    }

    // Check for empty files
    if (file.size === 0) {
      throw new Error('Cannot process empty file');
    }

    // Validate file buffer
    if (!file.buffer || file.buffer.length === 0) {
      throw new Error('File buffer is empty or corrupted');
    }

    logger.info(`[DocumentProcessing] Document validation passed: ${file.originalname} (${file.size} bytes, ${file.mimetype})`);
  }

  /**
   * Validate user exists and is authorized
   */
  private async validateUser(userId: string): Promise<void> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid user ID provided');
    }

    // Check if user exists in database
    const { data: user, error } = await this.supabase
      .from('users')
      .select('id')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error(`[DocumentProcessing] User validation error: ${error.message}`);
      throw new Error('Failed to validate user');
    }

    if (!user) {
      throw new Error(`User ${userId} not found`);
    }

    logger.info(`[DocumentProcessing] User validation passed: ${userId}`);
  }

  /**
   * Validate generated embeddings
   */
  private validateEmbeddings(embeddings: number[][]): void {
    const expectedDimensions = parseInt(process.env.OPENAI_EMBEDDING_DIMENSIONS || '1536');

    for (let i = 0; i < embeddings.length; i++) {
      const embedding = embeddings[i];

      if (!Array.isArray(embedding)) {
        throw new Error(`Embedding ${i} is not an array`);
      }

      if (embedding.length !== expectedDimensions) {
        throw new Error(`Embedding ${i} has ${embedding.length} dimensions, expected ${expectedDimensions}`);
      }

      if (embedding.some(val => typeof val !== 'number' || isNaN(val))) {
        throw new Error(`Embedding ${i} contains invalid values`);
      }
    }

    logger.info(`[DocumentProcessing] Embedding validation passed: ${embeddings.length} embeddings with ${expectedDimensions} dimensions each`);
  }

  /**
   * Check if file type is supported
   */
  private isSupportedFileType(mimetype: string): boolean {
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/html'
    ];
    return supportedTypes.includes(mimetype);
  }

  /**
   * Set up queue processing handlers with enhanced error handling
   */
  private setupQueueProcessing(): void {
    const queue = this.processingQueue!; // Already initialized at this point

    // Configure job processing with timeout
    queue.process('process', 1, async (job: any) => {
      const { documentId, fileBuffer, filename, mimetype, userId } = job.data;
      const startTime = Date.now();

      // Set processing timeout
      const timeoutId = setTimeout(() => {
        logger.error(`[DocumentProcessing] Processing timeout for document ${documentId} after ${PROCESSING_TIMEOUT_MS}ms`);
        job.moveToFailed(new Error('Processing timeout'), true);
      }, PROCESSING_TIMEOUT_MS);

      try {
        logger.info(`[DocumentProcessing] Processing document: ${filename} (ID: ${documentId}) for user: ${userId}`);

        // Update status to processing with timestamp
        await this.supabase
          .from('documents')
          .update({
            processing_status: 'processing',
            processed_at: new Date().toISOString()
          })
          .eq('id', documentId);

        // Convert base64 string back to Buffer with validation
        const buffer = Buffer.from(fileBuffer, 'base64');
        logger.info(`[DocumentProcessing] File buffer size: ${buffer.length} bytes`);

        if (buffer.length === 0) {
          throw new Error('File buffer is empty after base64 conversion');
        }

        // Extract text with timeout protection
        const text = await Promise.race([
          this.extractText(buffer, mimetype),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Text extraction timeout')), 60000)
          )
        ]);

        logger.info(`[DocumentProcessing] Extracted text length: ${text.length} characters`);

        if (text.length === 0) {
          throw new Error('No text content extracted from document');
        }

        logger.info(`[DocumentProcessing] Text preview: ${text.substring(0, 200)}...`);

        // Chunk text with validation
        const chunks = this.chunkText(text);
        logger.info(`[DocumentProcessing] Generated ${chunks.length} chunks`);

        if (chunks.length === 0) {
          throw new Error('No chunks generated from document text');
        }

        // Update progress
        job.progress(30);

        // Generate embeddings with memory management
        logger.info(`[DocumentProcessing] Starting embedding generation for ${chunks.length} chunks`);
        const embeddings = await this.generateEmbeddings(chunks);

        if (embeddings.length !== chunks.length) {
          throw new Error(`Embedding count mismatch: ${embeddings.length} embeddings for ${chunks.length} chunks`);
        }

        job.progress(70);

        // Store chunks and embeddings in database
        logger.info(`[DocumentProcessing] Preparing to store ${chunks.length} chunks`);
        chunks.forEach((chunk, index) => {
          logger.info(`[DocumentProcessing] Chunk ${index}: "${String(chunk).substring(0, 100)}..." (type: ${typeof chunk}, constructor: ${chunk?.constructor?.name})`);
        });

        const chunkInserts = chunks.map((chunk, index) => ({
          document_id: documentId,
          content: String(chunk), // Ensure content is properly stringified
          embedding: embeddings[index],
          chunk_index: index,
          user_id: userId
        }));

        logger.info(`[DocumentProcessing] Chunk inserts prepared: ${chunkInserts.length} items`);

        const { error: insertError } = await this.supabase
          .from('document_chunks')
          .insert(chunkInserts);

        if (insertError) {
          throw new Error(`Failed to store chunks: ${insertError.message}`);
        }

        // Update document status to completed
        await this.supabase
          .from('documents')
          .update({ 
            processing_status: 'completed',
            processed_at: new Date().toISOString(),
            chunk_count: chunks.length
          })
          .eq('id', documentId);

        // Clear timeout on successful completion
        clearTimeout(timeoutId);

        const processingTime = Date.now() - startTime;
        logger.info(`[DocumentProcessing] Successfully processed document: ${filename} in ${processingTime}ms`);

        job.progress(100);

        return {
          success: true,
          documentId,
          chunkCount: chunks.length,
          processingTimeMs: processingTime
        };
      } catch (error: any) {
        // Clear timeout on error
        clearTimeout(timeoutId);

        const processingTime = Date.now() - startTime;
        logger.error(`[DocumentProcessing] Processing failed for ${filename} after ${processingTime}ms: ${error.message}`);
        logger.error(`[DocumentProcessing] Error stack: ${error.stack}`);

        // Determine if error is retryable
        const isRetryable = this.isRetryableError(error);

        // Update status to failed with detailed error information
        await this.supabase
          .from('documents')
          .update({
            processing_status: 'failed',
            error_message: `${error.message} (Processing time: ${processingTime}ms, Retryable: ${isRetryable})`
          })
          .eq('id', documentId);

        // Log error for monitoring
        this.logProcessingError(documentId, filename, error, processingTime, isRetryable);

        throw error;
      }
    });

    // Enhanced queue event handling
    queue.on('completed', (job: any, result: any) => {
      logger.info(`[DocumentProcessing] Job ${job.id} completed successfully: ${JSON.stringify(result)}`);
    });

    queue.on('failed', (job: any, err: any) => {
      logger.error(`[DocumentProcessing] Job ${job.id} failed: ${err.message}`);
      logger.error(`[DocumentProcessing] Job data: ${JSON.stringify(job.data)}`);

      // Check if job should be retried
      const retryCount = job.opts.attempts || 0;
      if (retryCount < 3 && this.isRetryableError(err)) {
        logger.info(`[DocumentProcessing] Job ${job.id} will be retried (attempt ${retryCount + 1}/3)`);
      }
    });

    queue.on('stalled', (job: any) => {
      logger.warn(`[DocumentProcessing] Job ${job.id} stalled and will be retried`);
    });

    queue.on('progress', (job: any, progress: number) => {
      logger.info(`[DocumentProcessing] Job ${job.id} progress: ${progress}%`);
    });
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    const retryableErrors = [
      'timeout',
      'rate limit',
      'network',
      'connection',
      'temporary',
      'service unavailable',
      'too many requests'
    ];

    const errorMessage = error.message?.toLowerCase() || '';
    return retryableErrors.some(keyword => errorMessage.includes(keyword));
  }

  /**
   * Log processing errors for monitoring and analytics
   */
  private logProcessingError(documentId: string, filename: string, error: any, processingTime: number, isRetryable: boolean): void {
    const errorLog = {
      timestamp: new Date().toISOString(),
      documentId,
      filename,
      errorMessage: error.message,
      errorStack: error.stack,
      processingTime,
      isRetryable,
      memoryUsage: process.memoryUsage()
    };

    // Log to console for immediate visibility
    logger.error(`[DocumentProcessing] Error details: ${JSON.stringify(errorLog, null, 2)}`);

    // TODO: Send to monitoring service (e.g., Sentry, DataDog)
    // this.sendToMonitoring(errorLog);
  }

  /**
   * Get processing status for a job
   */
  async getJobStatus(jobId: string): Promise<any> {
    try {
      const queue = this.initializeQueue();
      const job = await queue.getJob(jobId);
      if (!job) {
        return { status: 'not_found' };
      }

      const state = await job.getState();
      return {
        id: job.id,
        status: state,
        progress: job.progress(),
        data: job.data,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn,
        failedReason: job.failedReason
      };
    } catch (error: any) {
      logger.error(`[DocumentProcessing] Error getting job status: ${error.message}`);
      throw error;
    }
  }
}

// Singleton instance
let documentService: DocumentProcessingService | null = null;

export function getDocumentService(): DocumentProcessingService {
  if (!documentService) {
    documentService = new DocumentProcessingService();
  }
  return documentService;
}
