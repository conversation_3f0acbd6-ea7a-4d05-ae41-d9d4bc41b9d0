import { AgentStateService } from './agentStateService';
import { MemoryService, MemoryServiceDependencies } from './memoryService';
import { ToolOrchestrator, ToolOrchestratorDependencies } from './toolOrchestrator';
import { VectorSearchService } from './vectorSearchService';
import { PlanningEngine } from './planningEngine';
import { ReasoningEngine } from './reasoningEngine';
import { ProactiveSuggestionEngine } from './proactiveSuggestionEngine';
import { AdvancedToolOrchestrator } from './advancedToolOrchestrator';
import { EnhancedLLMOrchestrationService, EnhancedLLMServiceDependencies } from './enhancedLLMOrchestrationService';
import { logger } from '../utils/logger';

/**
 * Service Container
 * Manages singleton instances of all agent services to prevent multiple instantiation
 * and optimize resource usage
 */
export class ServiceContainer {
  private static instance: ServiceContainer;
  private services: Map<string, any> = new Map();
  private initialized = false;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of the service container
   */
  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Initialize all services in the correct order
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      logger.info('[ServiceContainer] Initializing agent services...');

      // Initialize core services first (no dependencies)
      this.services.set('vectorSearch', new VectorSearchService());
      this.services.set('agentState', new AgentStateService());
      
      // Initialize services with dependencies
      this.services.set('memory', new MemoryService({
        vectorSearchService: this.services.get('vectorSearch')
      }));
      this.services.set('toolOrchestrator', new ToolOrchestrator({
        vectorSearchService: this.services.get('vectorSearch')
      }));
      this.services.set('planningEngine', new PlanningEngine());
      this.services.set('reasoningEngine', new ReasoningEngine());
      this.services.set('proactiveSuggestion', new ProactiveSuggestionEngine());
      this.services.set('advancedToolOrchestrator', new AdvancedToolOrchestrator());

      // Initialize enhanced LLM service last (depends on all others)
      const enhancedLLMService = new EnhancedLLMOrchestrationService({
        agentStateService: this.services.get('agentState'),
        memoryService: this.services.get('memory'),
        toolOrchestrator: this.services.get('toolOrchestrator'),
        planningEngine: this.services.get('planningEngine'),
        reasoningEngine: this.services.get('reasoningEngine'),
        proactiveSuggestionEngine: this.services.get('proactiveSuggestion'),
        advancedToolOrchestrator: this.services.get('advancedToolOrchestrator')
      });
      this.services.set('enhancedLLM', enhancedLLMService);
      
      this.initialized = true;
      logger.info('[ServiceContainer] All agent services initialized successfully');

    } catch (error: any) {
      logger.error(`[ServiceContainer] Failed to initialize services: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a service instance by name
   */
  getService<T>(serviceName: string): T {
    if (!this.initialized) {
      throw new Error('ServiceContainer not initialized. Call initialize() first.');
    }

    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service '${serviceName}' not found in container`);
    }

    return service as T;
  }

  /**
   * Get AgentStateService instance
   */
  getAgentStateService(): AgentStateService {
    return this.getService<AgentStateService>('agentState');
  }

  /**
   * Get MemoryService instance
   */
  getMemoryService(): MemoryService {
    return this.getService<MemoryService>('memory');
  }

  /**
   * Get ToolOrchestrator instance
   */
  getToolOrchestrator(): ToolOrchestrator {
    return this.getService<ToolOrchestrator>('toolOrchestrator');
  }

  /**
   * Get VectorSearchService instance
   */
  getVectorSearchService(): VectorSearchService {
    return this.getService<VectorSearchService>('vectorSearch');
  }

  /**
   * Get PlanningEngine instance
   */
  getPlanningEngine(): PlanningEngine {
    return this.getService<PlanningEngine>('planningEngine');
  }

  /**
   * Get ReasoningEngine instance
   */
  getReasoningEngine(): ReasoningEngine {
    return this.getService<ReasoningEngine>('reasoningEngine');
  }

  /**
   * Get ProactiveSuggestionEngine instance
   */
  getProactiveSuggestionEngine(): ProactiveSuggestionEngine {
    return this.getService<ProactiveSuggestionEngine>('proactiveSuggestion');
  }

  /**
   * Get AdvancedToolOrchestrator instance
   */
  getAdvancedToolOrchestrator(): AdvancedToolOrchestrator {
    return this.getService<AdvancedToolOrchestrator>('advancedToolOrchestrator');
  }

  /**
   * Get EnhancedLLMOrchestrationService instance
   */
  getEnhancedLLMService(): EnhancedLLMOrchestrationService {
    return this.getService<EnhancedLLMOrchestrationService>('enhancedLLM');
  }

  /**
   * Register a custom service
   */
  registerService(name: string, service: any): void {
    this.services.set(name, service);
    logger.info(`[ServiceContainer] Registered custom service: ${name}`);
  }

  /**
   * Check if container is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get health status of all services
   */
  async getHealthStatus(): Promise<any> {
    if (!this.initialized) {
      return { status: 'not_initialized', services: {} };
    }

    const healthStatus: any = {
      status: 'healthy',
      services: {}
    };

    try {
      // Check agent state service
      const agentStateService = this.getAgentStateService();
      healthStatus.services.agentState = await agentStateService.healthCheck();

      // Add other health checks as needed
      healthStatus.services.memory = { status: 'healthy' };
      healthStatus.services.toolOrchestrator = { status: 'healthy' };
      healthStatus.services.vectorSearch = { status: 'healthy' };

      // Overall status
      const allHealthy = Object.values(healthStatus.services).every((service: any) => 
        service.status === 'healthy' || (service.redis && service.database)
      );
      
      healthStatus.status = allHealthy ? 'healthy' : 'degraded';

    } catch (error: any) {
      healthStatus.status = 'error';
      healthStatus.error = error.message;
    }

    return healthStatus;
  }

  /**
   * Cleanup all services (for testing or shutdown)
   */
  async cleanup(): Promise<void> {
    logger.info('[ServiceContainer] Cleaning up services...');
    
    // Close Redis connections and other cleanup
    try {
      const agentStateService = this.getAgentStateService();
      // Add cleanup logic if needed
    } catch (error) {
      // Service might not be initialized
    }

    this.services.clear();
    this.initialized = false;
    logger.info('[ServiceContainer] Services cleaned up');
  }
}

/**
 * Global service container instance
 */
export const serviceContainer = ServiceContainer.getInstance();
