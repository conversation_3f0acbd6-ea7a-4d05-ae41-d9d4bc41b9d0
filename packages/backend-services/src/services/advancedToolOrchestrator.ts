import { ToolOrchestrator, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolContext } from './toolOrchestrator';
import { AgentState } from './agentStateService';
import { MemoryService } from './memoryService';
import { ReasoningEngine } from './reasoningEngine';
import { logger } from '../utils/logger';

// Advanced tool interfaces
export interface ToolChain {
  id: string;
  name: string;
  description: string;
  steps: ToolChainStep[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  results: ToolResult[];
  startedAt: Date;
  completedAt?: Date;
  metadata?: any;
}

export interface ToolChainStep {
  id: string;
  toolName: string;
  parameters: any;
  condition?: ToolCondition;
  onSuccess?: ToolAction[];
  onFailure?: ToolAction[];
  timeout?: number;
  retries?: number;
  dependencies?: string[]; // IDs of previous steps that must succeed
}

export interface ToolCondition {
  type: 'always' | 'if_success' | 'if_failure' | 'if_value' | 'if_contains';
  value?: any;
  comparison?: 'equals' | 'contains' | 'greater_than' | 'less_than';
  path?: string; // JSON path for value extraction
}

export interface ToolAction {
  type: 'continue' | 'skip' | 'retry' | 'branch' | 'stop';
  target?: string; // For branch actions
  parameters?: any;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'sales' | 'research' | 'analysis' | 'communication' | 'automation';
  steps: ToolChainStep[];
  parameters: WorkflowParameter[];
  estimatedDuration: number; // minutes
}

export interface WorkflowParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  description: string;
  required: boolean;
  default?: any;
}

/**
 * Advanced Tool Orchestrator
 * Extends basic tool orchestration with chaining, conditional execution,
 * and complex multi-step workflows
 */
export class AdvancedToolOrchestrator extends ToolOrchestrator {
  private memoryService: MemoryService;
  private reasoningEngine: ReasoningEngine;
  private activeChains: Map<string, ToolChain> = new Map();
  private workflowTemplates: Map<string, WorkflowTemplate> = new Map();

  constructor() {
    super();
    this.memoryService = new MemoryService();
    this.reasoningEngine = new ReasoningEngine();
    
    this.initializeWorkflowTemplates();
    
    logger.info('[AdvancedToolOrchestrator] Initialized with tool chaining and workflow capabilities');
  }

  /**
   * Execute a tool chain
   */
  async executeToolChain(
    chain: ToolChain,
    context: ToolContext
  ): Promise<ToolChain> {
    try {
      chain.status = 'running';
      chain.startedAt = new Date();
      this.activeChains.set(chain.id, chain);

      logger.info(`[AdvancedToolOrchestrator] Starting tool chain: ${chain.id} with ${chain.steps.length} steps`);

      for (let i = 0; i < chain.steps.length; i++) {
        const step = chain.steps[i];
        
        // Check dependencies
        if (step.dependencies && !this.checkDependencies(step.dependencies, chain.results)) {
          logger.warn(`[AdvancedToolOrchestrator] Step ${step.id} dependencies not met, skipping`);
          continue;
        }

        // Check condition
        if (step.condition && !this.evaluateCondition(step.condition, chain.results)) {
          logger.info(`[AdvancedToolOrchestrator] Step ${step.id} condition not met, skipping`);
          continue;
        }

        // Execute step with retries
        const stepResult = await this.executeStepWithRetries(step, context);
        chain.results.push(stepResult);

        // Handle step result
        const action = stepResult.success ? 
          (step.onSuccess?.[0] || { type: 'continue' }) :
          (step.onFailure?.[0] || { type: 'continue' });

        switch (action.type) {
          case 'stop':
            logger.info(`[AdvancedToolOrchestrator] Chain stopped at step ${step.id}`);
            chain.status = stepResult.success ? 'completed' : 'failed';
            chain.completedAt = new Date();
            return chain;

          case 'skip':
            logger.info(`[AdvancedToolOrchestrator] Skipping remaining steps after ${step.id}`);
            break;

          case 'branch':
            if (action.target) {
              const branchIndex = chain.steps.findIndex(s => s.id === action.target);
              if (branchIndex !== -1) {
                i = branchIndex - 1; // -1 because loop will increment
                logger.info(`[AdvancedToolOrchestrator] Branching to step ${action.target}`);
              }
            }
            break;

          case 'retry':
            i--; // Retry current step
            logger.info(`[AdvancedToolOrchestrator] Retrying step ${step.id}`);
            break;

          case 'continue':
          default:
            // Continue to next step
            break;
        }
      }

      chain.status = 'completed';
      chain.completedAt = new Date();

      // Store chain results in memory
      await this.memoryService.addMemory(
        context.agentState,
        `Completed tool chain: ${chain.name} with ${chain.results.length} steps`,
        'insight',
        7,
        { chainId: chain.id, results: chain.results.length }
      );

      logger.info(`[AdvancedToolOrchestrator] Completed tool chain: ${chain.id}`);
      return chain;

    } catch (error: any) {
      chain.status = 'failed';
      chain.completedAt = new Date();
      logger.error(`[AdvancedToolOrchestrator] Tool chain failed: ${error.message}`);
      throw error;
    } finally {
      this.activeChains.delete(chain.id);
    }
  }

  /**
   * Execute a workflow template
   */
  async executeWorkflow(
    templateId: string,
    parameters: Record<string, any>,
    context: ToolContext
  ): Promise<ToolChain> {
    const template = this.workflowTemplates.get(templateId);
    if (!template) {
      throw new Error(`Workflow template not found: ${templateId}`);
    }

    // Validate parameters
    this.validateWorkflowParameters(template, parameters);

    // Create tool chain from template
    const chain: ToolChain = {
      id: `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: template.name,
      description: template.description,
      steps: this.instantiateWorkflowSteps(template.steps, parameters),
      status: 'pending',
      results: [],
      startedAt: new Date(),
      metadata: { templateId, parameters }
    };

    return await this.executeToolChain(chain, context);
  }

  /**
   * Create a custom tool chain
   */
  createToolChain(
    name: string,
    description: string,
    steps: ToolChainStep[]
  ): ToolChain {
    return {
      id: `chain_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      steps,
      status: 'pending',
      results: [],
      startedAt: new Date()
    };
  }

  /**
   * Execute a step with retry logic
   */
  private async executeStepWithRetries(
    step: ToolChainStep,
    context: ToolContext
  ): Promise<ToolResult> {
    const maxRetries = step.retries || 1;
    let lastError: any;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        logger.debug(`[AdvancedToolOrchestrator] Executing step ${step.id}, attempt ${attempt + 1}/${maxRetries}`);
        
        const result = await this.executeTool(step.toolName, step.parameters, context);
        
        if (result.success) {
          return result;
        } else {
          lastError = new Error(result.error || 'Tool execution failed');
          if (attempt < maxRetries - 1) {
            logger.warn(`[AdvancedToolOrchestrator] Step ${step.id} failed, retrying...`);
            await this.delay(1000 * (attempt + 1)); // Exponential backoff
          }
        }
      } catch (error: any) {
        lastError = error;
        if (attempt < maxRetries - 1) {
          logger.warn(`[AdvancedToolOrchestrator] Step ${step.id} error, retrying: ${error.message}`);
          await this.delay(1000 * (attempt + 1));
        }
      }
    }

    // All retries failed
    return {
      success: false,
      error: `Step failed after ${maxRetries} attempts: ${lastError.message}`,
      executionTime: 0
    };
  }

  /**
   * Check if step dependencies are satisfied
   */
  private checkDependencies(dependencies: string[], results: ToolResult[]): boolean {
    // For now, simple check - all previous steps must have succeeded
    // In a more sophisticated implementation, we'd track step IDs and their results
    return results.length >= dependencies.length && 
           results.slice(-dependencies.length).every(r => r.success);
  }

  /**
   * Evaluate a tool condition
   */
  private evaluateCondition(condition: ToolCondition, results: ToolResult[]): boolean {
    switch (condition.type) {
      case 'always':
        return true;

      case 'if_success':
        return results.length > 0 && results[results.length - 1].success;

      case 'if_failure':
        return results.length > 0 && !results[results.length - 1].success;

      case 'if_value':
        if (results.length === 0) return false;
        const lastResult = results[results.length - 1];
        const value = condition.path ? 
          this.extractValueByPath(lastResult.data, condition.path) : 
          lastResult.data;
        return this.compareValues(value, condition.value, condition.comparison || 'equals');

      case 'if_contains':
        if (results.length === 0) return false;
        const data = results[results.length - 1].data;
        const searchValue = condition.value;
        return JSON.stringify(data).includes(searchValue);

      default:
        return true;
    }
  }

  /**
   * Extract value by JSON path
   */
  private extractValueByPath(data: any, path: string): any {
    const parts = path.split('.');
    let current = data;
    
    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  /**
   * Compare values based on comparison type
   */
  private compareValues(actual: any, expected: any, comparison: string): boolean {
    switch (comparison) {
      case 'equals':
        return actual === expected;
      case 'contains':
        return String(actual).includes(String(expected));
      case 'greater_than':
        return Number(actual) > Number(expected);
      case 'less_than':
        return Number(actual) < Number(expected);
      default:
        return actual === expected;
    }
  }

  /**
   * Initialize built-in workflow templates
   */
  private initializeWorkflowTemplates(): void {
    // Lead qualification workflow
    this.workflowTemplates.set('lead_qualification', {
      id: 'lead_qualification',
      name: 'Lead Qualification Workflow',
      description: 'Comprehensive lead qualification process',
      category: 'sales',
      estimatedDuration: 15,
      parameters: [
        { name: 'companyName', type: 'string', description: 'Company name to research', required: true },
        { name: 'contactEmail', type: 'string', description: 'Contact email address', required: false }
      ],
      steps: [
        {
          id: 'research_company',
          toolName: 'search_crm',
          parameters: { query: '{{companyName}}', type: 'account' },
          onSuccess: [{ type: 'continue' }],
          onFailure: [{ type: 'continue' }]
        },
        {
          id: 'search_knowledge',
          toolName: 'search_knowledge',
          parameters: { query: 'lead qualification best practices' },
          condition: { type: 'if_success' }
        },
        {
          id: 'analyze_fit',
          toolName: 'analyze_conversation',
          parameters: { analysis_type: 'opportunities' },
          dependencies: ['research_company']
        }
      ]
    });

    // Competitive analysis workflow
    this.workflowTemplates.set('competitive_analysis', {
      id: 'competitive_analysis',
      name: 'Competitive Analysis Workflow',
      description: 'Analyze competitive landscape and positioning',
      category: 'research',
      estimatedDuration: 20,
      parameters: [
        { name: 'competitor', type: 'string', description: 'Competitor name', required: true },
        { name: 'focusArea', type: 'string', description: 'Area of focus', required: false, default: 'general' }
      ],
      steps: [
        {
          id: 'research_competitor',
          toolName: 'search_knowledge',
          parameters: { query: 'competitor {{competitor}} analysis' }
        },
        {
          id: 'find_differentiators',
          toolName: 'search_knowledge',
          parameters: { query: 'competitive advantages vs {{competitor}}' },
          condition: { type: 'if_success' }
        },
        {
          id: 'analyze_positioning',
          toolName: 'analyze_conversation',
          parameters: { analysis_type: 'competitive' },
          dependencies: ['research_competitor', 'find_differentiators']
        }
      ]
    });

    // Follow-up sequence workflow
    this.workflowTemplates.set('follow_up_sequence', {
      id: 'follow_up_sequence',
      name: 'Follow-up Sequence Workflow',
      description: 'Automated follow-up sequence for prospects',
      category: 'communication',
      estimatedDuration: 10,
      parameters: [
        { name: 'prospectEmail', type: 'string', description: 'Prospect email', required: true },
        { name: 'meetingType', type: 'string', description: 'Type of meeting', required: false, default: 'demo' }
      ],
      steps: [
        {
          id: 'send_summary',
          toolName: 'send_email',
          parameters: { 
            to: '{{prospectEmail}}',
            subject: 'Thank you for our conversation',
            body: 'Following up on our discussion...'
          }
        },
        {
          id: 'schedule_followup',
          toolName: 'schedule_meeting',
          parameters: {
            attendees: ['{{prospectEmail}}'],
            title: '{{meetingType}} meeting',
            duration: 30
          },
          condition: { type: 'if_success' }
        }
      ]
    });
  }

  /**
   * Validate workflow parameters
   */
  private validateWorkflowParameters(template: WorkflowTemplate, parameters: Record<string, any>): void {
    for (const param of template.parameters) {
      if (param.required && !(param.name in parameters)) {
        throw new Error(`Required parameter missing: ${param.name}`);
      }
      
      if (param.name in parameters) {
        const value = parameters[param.name];
        if (!this.isValidParameterType(value, param.type)) {
          throw new Error(`Parameter ${param.name} must be of type ${param.type}`);
        }
      }
    }
  }

  /**
   * Check if parameter value matches expected type
   */
  private isValidParameterType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string': return typeof value === 'string';
      case 'number': return typeof value === 'number';
      case 'boolean': return typeof value === 'boolean';
      case 'object': return typeof value === 'object' && value !== null;
      default: return true;
    }
  }

  /**
   * Instantiate workflow steps with parameter substitution
   */
  private instantiateWorkflowSteps(
    templateSteps: ToolChainStep[],
    parameters: Record<string, any>
  ): ToolChainStep[] {
    return templateSteps.map(step => ({
      ...step,
      parameters: this.substituteParameters(step.parameters, parameters)
    }));
  }

  /**
   * Substitute template parameters in step parameters
   */
  private substituteParameters(stepParams: any, workflowParams: Record<string, any>): any {
    const paramString = JSON.stringify(stepParams);
    let substituted = paramString;

    for (const [key, value] of Object.entries(workflowParams)) {
      const placeholder = `{{${key}}}`;
      substituted = substituted.replace(new RegExp(placeholder, 'g'), String(value));
    }

    return JSON.parse(substituted);
  }

  /**
   * Get available workflow templates
   */
  getWorkflowTemplates(category?: string): WorkflowTemplate[] {
    const templates = Array.from(this.workflowTemplates.values());
    return category ? templates.filter(t => t.category === category) : templates;
  }

  /**
   * Get active tool chains
   */
  getActiveChains(): ToolChain[] {
    return Array.from(this.activeChains.values());
  }

  /**
   * Cancel a tool chain
   */
  cancelToolChain(chainId: string): boolean {
    const chain = this.activeChains.get(chainId);
    if (chain && chain.status === 'running') {
      chain.status = 'failed';
      chain.completedAt = new Date();
      this.activeChains.delete(chainId);
      return true;
    }
    return false;
  }

  /**
   * Register a custom workflow template
   */
  registerWorkflowTemplate(template: WorkflowTemplate): void {
    this.workflowTemplates.set(template.id, template);
    logger.info(`[AdvancedToolOrchestrator] Registered workflow template: ${template.id}`);
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
