import { AgentState } from './agentStateService';
import { MemoryService } from './memoryService';
import { ToolOrchestrator, ToolResult } from './toolOrchestrator';
import { LLMOrchestrationService } from './llmOrchestrationService';
import { logger } from '../utils/logger';

// Reasoning pattern interfaces
export interface ReasoningStep {
  id: string;
  type: 'thought' | 'action' | 'observation' | 'reflection';
  content: string;
  confidence: number; // 0-1 scale
  timestamp: Date;
  metadata?: any;
}

export interface ReasoningChain {
  id: string;
  pattern: ReasoningPattern;
  steps: ReasoningStep[];
  goal: string;
  status: 'active' | 'completed' | 'failed';
  result?: any;
  startedAt: Date;
  completedAt?: Date;
}

export interface ReasoningPattern {
  name: 'react' | 'chain_of_thought' | 'tree_of_thoughts' | 'self_reflection' | 'analogical';
  description: string;
  maxSteps: number;
  parameters?: any;
}

export interface ThoughtNode {
  id: string;
  content: string;
  confidence: number;
  children: ThoughtNode[];
  parent?: string;
  evaluation?: number; // -1 to 1 scale
  depth: number;
}

/**
 * Reasoning Engine
 * Implements advanced reasoning patterns including ReAct, Chain-of-Thought,
 * Tree-of-Thoughts, and self-reflection for complex problem solving
 */
export class ReasoningEngine {
  private memoryService: MemoryService;
  private toolOrchestrator: ToolOrchestrator;
  private activeChains: Map<string, ReasoningChain> = new Map();

  constructor() {
    this.memoryService = new MemoryService();
    this.toolOrchestrator = new ToolOrchestrator();
    
    logger.info('[ReasoningEngine] Initialized with advanced reasoning patterns');
  }

  /**
   * Start a reasoning chain with the specified pattern
   */
  async startReasoningChain(
    goal: string,
    pattern: ReasoningPattern,
    agentState: AgentState
  ): Promise<ReasoningChain> {
    try {
      const chainId = `reasoning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const chain: ReasoningChain = {
        id: chainId,
        pattern,
        steps: [],
        goal,
        status: 'active',
        startedAt: new Date()
      };

      this.activeChains.set(chainId, chain);

      // Add initial reasoning step
      await this.addReasoningStep(chain, {
        type: 'thought',
        content: `Starting ${pattern.name} reasoning for goal: ${goal}`,
        confidence: 0.8
      });

      // Store in agent memory
      await this.memoryService.addMemory(
        agentState,
        `Started ${pattern.name} reasoning chain for: ${goal}`,
        'insight',
        7,
        { chainId, pattern: pattern.name }
      );

      logger.info(`[ReasoningEngine] Started ${pattern.name} reasoning chain: ${chainId}`);
      return chain;

    } catch (error: any) {
      logger.error(`[ReasoningEngine] Error starting reasoning chain: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute ReAct (Reasoning + Acting) pattern
   */
  async executeReActReasoning(
    goal: string,
    agentState: AgentState,
    maxIterations: number = 5
  ): Promise<ReasoningChain> {
    const pattern: ReasoningPattern = {
      name: 'react',
      description: 'Reasoning and Acting in synergy',
      maxSteps: maxIterations * 3, // thought, action, observation per iteration
      parameters: { maxIterations }
    };

    const chain = await this.startReasoningChain(goal, pattern, agentState);

    try {
      for (let iteration = 0; iteration < maxIterations; iteration++) {
        // Thought step
        const thought = await this.generateThought(goal, chain, agentState);
        await this.addReasoningStep(chain, {
          type: 'thought',
          content: thought,
          confidence: 0.7
        });

        // Determine if action is needed
        const actionNeeded = await this.shouldTakeAction(thought, chain);
        
        if (actionNeeded.needed) {
          // Action step
          const actionResult = await this.executeAction(actionNeeded.action, agentState);
          await this.addReasoningStep(chain, {
            type: 'action',
            content: `Executed: ${actionNeeded.action.description}`,
            confidence: 0.8,
            metadata: { toolResult: actionResult }
          });

          // Observation step
          const observation = await this.generateObservation(actionResult, goal);
          await this.addReasoningStep(chain, {
            type: 'observation',
            content: observation,
            confidence: 0.9
          });

          // Check if goal is achieved
          const goalAchieved = await this.checkGoalAchievement(goal, chain, agentState);
          if (goalAchieved.achieved) {
            chain.status = 'completed';
            chain.result = goalAchieved.result;
            chain.completedAt = new Date();
            break;
          }
        } else {
          // No action needed, check if we can conclude
          const conclusion = await this.generateConclusion(goal, chain, agentState);
          if (conclusion.canConclude) {
            chain.status = 'completed';
            chain.result = conclusion.result;
            chain.completedAt = new Date();
            break;
          }
        }
      }

      // Store final reasoning chain in memory
      await this.memoryService.addMemory(
        agentState,
        `Completed ReAct reasoning: ${chain.result?.summary || 'No clear conclusion'}`,
        'insight',
        8,
        { chainId: chain.id, steps: chain.steps.length, status: chain.status }
      );

      logger.info(`[ReasoningEngine] Completed ReAct reasoning chain: ${chain.id}`);
      return chain;

    } catch (error: any) {
      chain.status = 'failed';
      chain.completedAt = new Date();
      logger.error(`[ReasoningEngine] ReAct reasoning failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute Chain-of-Thought reasoning
   */
  async executeChainOfThought(
    problem: string,
    agentState: AgentState,
    maxSteps: number = 8
  ): Promise<ReasoningChain> {
    const pattern: ReasoningPattern = {
      name: 'chain_of_thought',
      description: 'Step-by-step logical reasoning',
      maxSteps,
      parameters: { decomposition: true }
    };

    const chain = await this.startReasoningChain(problem, pattern, agentState);

    try {
      // Break down the problem
      const decomposition = await this.decomposeProblem(problem, agentState);
      await this.addReasoningStep(chain, {
        type: 'thought',
        content: `Problem decomposition: ${decomposition}`,
        confidence: 0.8
      });

      // Generate reasoning steps
      for (let step = 0; step < maxSteps; step++) {
        const reasoningStep = await this.generateReasoningStep(problem, chain, agentState);
        
        if (!reasoningStep.content) break; // No more reasoning needed
        
        await this.addReasoningStep(chain, reasoningStep);

        // Check if we've reached a conclusion
        if (reasoningStep.type === 'reflection' && reasoningStep.confidence > 0.8) {
          break;
        }
      }

      // Generate final conclusion
      const conclusion = await this.synthesizeConclusion(chain, agentState);
      await this.addReasoningStep(chain, {
        type: 'reflection',
        content: conclusion,
        confidence: 0.9
      });

      chain.status = 'completed';
      chain.result = { conclusion, steps: chain.steps.length };
      chain.completedAt = new Date();

      // Store in memory
      await this.memoryService.addMemory(
        agentState,
        `Chain-of-thought reasoning conclusion: ${conclusion}`,
        'insight',
        8,
        { chainId: chain.id, problem }
      );

      logger.info(`[ReasoningEngine] Completed Chain-of-Thought reasoning: ${chain.id}`);
      return chain;

    } catch (error: any) {
      chain.status = 'failed';
      chain.completedAt = new Date();
      logger.error(`[ReasoningEngine] Chain-of-Thought reasoning failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute Tree-of-Thoughts reasoning
   */
  async executeTreeOfThoughts(
    problem: string,
    agentState: AgentState,
    maxDepth: number = 3,
    branchingFactor: number = 3
  ): Promise<ReasoningChain> {
    const pattern: ReasoningPattern = {
      name: 'tree_of_thoughts',
      description: 'Explore multiple reasoning paths',
      maxSteps: maxDepth * branchingFactor * 2,
      parameters: { maxDepth, branchingFactor }
    };

    const chain = await this.startReasoningChain(problem, pattern, agentState);

    try {
      // Create root thought node
      const rootNode: ThoughtNode = {
        id: 'root',
        content: `Root problem: ${problem}`,
        confidence: 1.0,
        children: [],
        depth: 0
      };

      // Build thought tree
      const thoughtTree = await this.buildThoughtTree(rootNode, maxDepth, branchingFactor, agentState);

      // Evaluate all paths
      const evaluatedPaths = await this.evaluateThoughtPaths(thoughtTree, problem, agentState);

      // Select best path
      const bestPath = this.selectBestPath(evaluatedPaths);

      // Convert best path to reasoning steps
      for (const node of bestPath) {
        await this.addReasoningStep(chain, {
          type: 'thought',
          content: node.content,
          confidence: node.confidence,
          metadata: { nodeId: node.id, evaluation: node.evaluation }
        });
      }

      chain.status = 'completed';
      chain.result = {
        bestPath: bestPath.map(n => n.content),
        totalPaths: evaluatedPaths.length,
        bestEvaluation: bestPath[bestPath.length - 1]?.evaluation
      };
      chain.completedAt = new Date();

      // Store in memory
      await this.memoryService.addMemory(
        agentState,
        `Tree-of-thoughts explored ${evaluatedPaths.length} paths, best solution: ${bestPath[bestPath.length - 1]?.content}`,
        'insight',
        9,
        { chainId: chain.id, pathsExplored: evaluatedPaths.length }
      );

      logger.info(`[ReasoningEngine] Completed Tree-of-Thoughts reasoning: ${chain.id}`);
      return chain;

    } catch (error: any) {
      chain.status = 'failed';
      chain.completedAt = new Date();
      logger.error(`[ReasoningEngine] Tree-of-Thoughts reasoning failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add a reasoning step to a chain
   */
  private async addReasoningStep(
    chain: ReasoningChain,
    stepData: Omit<ReasoningStep, 'id' | 'timestamp'>
  ): Promise<void> {
    const step: ReasoningStep = {
      id: `step_${chain.steps.length + 1}`,
      timestamp: new Date(),
      ...stepData
    };

    chain.steps.push(step);
    logger.debug(`[ReasoningEngine] Added ${step.type} step to chain ${chain.id}: ${step.content.substring(0, 100)}...`);
  }

  /**
   * Generate a thought for ReAct reasoning
   */
  private async generateThought(goal: string, chain: ReasoningChain, agentState: AgentState): Promise<string> {
    const context = await this.memoryService.getMemoryContext(agentState, goal);
    const previousSteps = chain.steps.map(s => `${s.type}: ${s.content}`).join('\n');

    const prompt = `You are reasoning about: ${goal}

CONTEXT: ${context}

PREVIOUS REASONING STEPS:
${previousSteps}

Generate your next thought. What should you think about or consider next to make progress toward the goal?
Be specific and actionable. Respond with just the thought, no additional formatting.`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.7,
      maxOutputTokens: 200
    });

    return response.success ? response.text! : 'Unable to generate thought at this time.';
  }

  /**
   * Determine if an action should be taken
   */
  private async shouldTakeAction(thought: string, chain: ReasoningChain): Promise<{ needed: boolean; action?: any }> {
    const prompt = `Based on this thought: "${thought}"

Should an action be taken? If yes, what action?

Available actions:
- search_knowledge: Search for information
- search_crm: Search customer data
- analyze_conversation: Analyze current conversation
- send_email: Send an email
- schedule_meeting: Schedule a meeting

Respond with JSON:
{
  "needed": true/false,
  "action": {
    "tool": "tool_name",
    "description": "what the action will do",
    "params": {"param": "value"}
  }
}`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.3,
      maxOutputTokens: 150
    });

    try {
      const result = JSON.parse(response.text!);
      return result;
    } catch {
      return { needed: false };
    }
  }

  /**
   * Execute an action
   */
  private async executeAction(action: any, agentState: AgentState): Promise<ToolResult> {
    return await this.toolOrchestrator.executeTool(
      action.tool,
      action.params || {},
      {
        agentState,
        userId: agentState.userId,
        conversationId: agentState.conversationId
      }
    );
  }

  /**
   * Generate an observation from action result
   */
  private async generateObservation(actionResult: ToolResult, goal: string): Promise<string> {
    const prompt = `You executed an action and got this result: ${JSON.stringify(actionResult)}

Goal: ${goal}

What do you observe from this result? How does it help or hinder progress toward the goal?
Provide a concise observation.`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.5,
      maxOutputTokens: 150
    });

    return response.success ? response.text! : 'No clear observation from the action result.';
  }

  /**
   * Check if goal is achieved
   */
  private async checkGoalAchievement(
    goal: string, 
    chain: ReasoningChain, 
    agentState: AgentState
  ): Promise<{ achieved: boolean; result?: any }> {
    const recentSteps = chain.steps.slice(-5).map(s => `${s.type}: ${s.content}`).join('\n');

    const prompt = `Goal: ${goal}

Recent reasoning steps:
${recentSteps}

Has the goal been achieved? Respond with JSON:
{
  "achieved": true/false,
  "result": {
    "summary": "brief summary of what was accomplished",
    "confidence": 0.0-1.0
  }
}`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.3,
      maxOutputTokens: 200
    });

    try {
      return JSON.parse(response.text!);
    } catch {
      return { achieved: false };
    }
  }

  /**
   * Generate conclusion when no action is needed
   */
  private async generateConclusion(
    goal: string,
    chain: ReasoningChain,
    agentState: AgentState
  ): Promise<{ canConclude: boolean; result?: any }> {
    const allSteps = chain.steps.map(s => `${s.type}: ${s.content}`).join('\n');

    const prompt = `Goal: ${goal}

All reasoning steps:
${allSteps}

Can you draw a conclusion? Respond with JSON:
{
  "canConclude": true/false,
  "result": {
    "conclusion": "your conclusion",
    "confidence": 0.0-1.0,
    "reasoning": "why you reached this conclusion"
  }
}`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.4,
      maxOutputTokens: 250
    });

    try {
      return JSON.parse(response.text!);
    } catch {
      return { canConclude: false };
    }
  }

  /**
   * Decompose a problem for Chain-of-Thought
   */
  private async decomposeProblem(problem: string, agentState: AgentState): Promise<string> {
    const context = await this.memoryService.getMemoryContext(agentState, problem);

    const prompt = `Problem: ${problem}

Context: ${context}

Break this problem down into smaller, manageable parts. What are the key components that need to be addressed?
Provide a clear decomposition.`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.6,
      maxOutputTokens: 300
    });

    return response.success ? response.text! : 'Unable to decompose problem.';
  }

  /**
   * Generate a reasoning step for Chain-of-Thought
   */
  private async generateReasoningStep(
    problem: string,
    chain: ReasoningChain,
    agentState: AgentState
  ): Promise<ReasoningStep> {
    const previousSteps = chain.steps.map(s => s.content).join('\n');

    const prompt = `Problem: ${problem}

Previous reasoning:
${previousSteps}

What's the next logical step in your reasoning? Continue the chain of thought.
If you're ready to conclude, start with "CONCLUSION:".`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.6,
      maxOutputTokens: 200
    });

    const content = response.success ? response.text! : '';
    const isConclusion = content.startsWith('CONCLUSION:');

    return {
      id: '',
      type: isConclusion ? 'reflection' : 'thought',
      content,
      confidence: isConclusion ? 0.9 : 0.7,
      timestamp: new Date()
    };
  }

  /**
   * Synthesize conclusion from Chain-of-Thought steps
   */
  private async synthesizeConclusion(chain: ReasoningChain, agentState: AgentState): Promise<string> {
    const allThoughts = chain.steps
      .filter(s => s.type === 'thought')
      .map(s => s.content)
      .join('\n');

    const prompt = `Based on this chain of reasoning:

${allThoughts}

Synthesize a clear, actionable conclusion. What is the answer or recommended course of action?`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.4,
      maxOutputTokens: 200
    });

    return response.success ? response.text! : 'Unable to synthesize conclusion.';
  }

  /**
   * Build thought tree for Tree-of-Thoughts
   */
  private async buildThoughtTree(
    node: ThoughtNode,
    maxDepth: number,
    branchingFactor: number,
    agentState: AgentState
  ): Promise<ThoughtNode> {
    if (node.depth >= maxDepth) {
      return node;
    }

    // Generate child thoughts
    for (let i = 0; i < branchingFactor; i++) {
      const childThought = await this.generateChildThought(node, i, agentState);
      const childNode: ThoughtNode = {
        id: `${node.id}_${i}`,
        content: childThought,
        confidence: Math.random() * 0.4 + 0.6, // 0.6-1.0
        children: [],
        parent: node.id,
        depth: node.depth + 1
      };

      node.children.push(childNode);

      // Recursively build subtree
      await this.buildThoughtTree(childNode, maxDepth, branchingFactor, agentState);
    }

    return node;
  }

  /**
   * Generate a child thought for Tree-of-Thoughts
   */
  private async generateChildThought(
    parentNode: ThoughtNode,
    childIndex: number,
    agentState: AgentState
  ): Promise<string> {
    const prompt = `Parent thought: ${parentNode.content}

Generate alternative thought #${childIndex + 1} that builds on or explores a different aspect of the parent thought.
Be creative and consider different perspectives.`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.8,
      maxOutputTokens: 100
    });

    return response.success ? response.text! : `Alternative thought ${childIndex + 1}`;
  }

  /**
   * Evaluate all paths in thought tree
   */
  private async evaluateThoughtPaths(
    tree: ThoughtNode,
    problem: string,
    agentState: AgentState
  ): Promise<ThoughtNode[][]> {
    const paths: ThoughtNode[][] = [];
    this.collectPaths(tree, [], paths);

    // Evaluate each path
    for (const path of paths) {
      const evaluation = await this.evaluatePath(path, problem);
      path[path.length - 1].evaluation = evaluation;
    }

    return paths;
  }

  /**
   * Collect all paths from root to leaves
   */
  private collectPaths(node: ThoughtNode, currentPath: ThoughtNode[], allPaths: ThoughtNode[][]): void {
    currentPath.push(node);

    if (node.children.length === 0) {
      // Leaf node - add path
      allPaths.push([...currentPath]);
    } else {
      // Continue to children
      for (const child of node.children) {
        this.collectPaths(child, currentPath, allPaths);
      }
    }

    currentPath.pop();
  }

  /**
   * Evaluate a thought path
   */
  private async evaluatePath(path: ThoughtNode[], problem: string): Promise<number> {
    const pathContent = path.map(n => n.content).join(' -> ');

    const prompt = `Problem: ${problem}

Thought path: ${pathContent}

Rate this reasoning path from -1 (very poor) to 1 (excellent) based on:
- Logical consistency
- Relevance to the problem
- Likelihood of leading to a solution

Respond with just a number between -1 and 1.`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.3,
      maxOutputTokens: 10
    });

    try {
      const score = parseFloat(response.text!);
      return isNaN(score) ? 0 : Math.max(-1, Math.min(1, score));
    } catch {
      return 0;
    }
  }

  /**
   * Select the best path from evaluated paths
   */
  private selectBestPath(paths: ThoughtNode[][]): ThoughtNode[] {
    let bestPath = paths[0];
    let bestScore = bestPath[bestPath.length - 1]?.evaluation || -1;

    for (const path of paths) {
      const score = path[path.length - 1]?.evaluation || -1;
      if (score > bestScore) {
        bestScore = score;
        bestPath = path;
      }
    }

    return bestPath;
  }

  /**
   * Get active reasoning chains for a conversation
   */
  getActiveChains(conversationId: string): ReasoningChain[] {
    return Array.from(this.activeChains.values()).filter(
      chain => chain.status === 'active'
    );
  }

  /**
   * Get reasoning chain by ID
   */
  getReasoningChain(chainId: string): ReasoningChain | undefined {
    return this.activeChains.get(chainId);
  }

  /**
   * Cancel a reasoning chain
   */
  cancelReasoningChain(chainId: string): void {
    const chain = this.activeChains.get(chainId);
    if (chain) {
      chain.status = 'failed';
      chain.completedAt = new Date();
      this.activeChains.delete(chainId);
    }
  }

  /**
   * Execute self-reflection reasoning
   */
  async executeSelfReflection(
    topic: string,
    agentState: AgentState,
    previousActions: any[]
  ): Promise<ReasoningChain> {
    const pattern: ReasoningPattern = {
      name: 'self_reflection',
      description: 'Reflect on past actions and decisions',
      maxSteps: 6,
      parameters: { introspective: true }
    };

    const chain = await this.startReasoningChain(topic, pattern, agentState);

    try {
      // Reflect on previous actions
      const actionReflection = await this.reflectOnActions(previousActions, agentState);
      await this.addReasoningStep(chain, {
        type: 'reflection',
        content: actionReflection,
        confidence: 0.8
      });

      // Identify patterns
      const patterns = await this.identifyPatterns(previousActions, agentState);
      await this.addReasoningStep(chain, {
        type: 'thought',
        content: `Identified patterns: ${patterns}`,
        confidence: 0.7
      });

      // Generate improvements
      const improvements = await this.generateImprovements(actionReflection, patterns, agentState);
      await this.addReasoningStep(chain, {
        type: 'reflection',
        content: improvements,
        confidence: 0.9
      });

      chain.status = 'completed';
      chain.result = { reflection: actionReflection, patterns, improvements };
      chain.completedAt = new Date();

      return chain;

    } catch (error: any) {
      chain.status = 'failed';
      chain.completedAt = new Date();
      logger.error(`[ReasoningEngine] Self-reflection failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Reflect on previous actions
   */
  private async reflectOnActions(actions: any[], agentState: AgentState): Promise<string> {
    const actionsText = actions.map(a => `${a.type}: ${a.description} -> ${a.result}`).join('\n');

    const prompt = `Reflect on these previous actions:

${actionsText}

What worked well? What could have been done better? What patterns do you notice?
Provide thoughtful reflection.`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.6,
      maxOutputTokens: 300
    });

    return response.success ? response.text! : 'Unable to reflect on actions.';
  }

  /**
   * Identify patterns in actions
   */
  private async identifyPatterns(actions: any[], agentState: AgentState): Promise<string> {
    const prompt = `Analyze these actions for patterns:

${JSON.stringify(actions, null, 2)}

What recurring themes, successful strategies, or problematic patterns do you see?`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.5,
      maxOutputTokens: 200
    });

    return response.success ? response.text! : 'No clear patterns identified.';
  }

  /**
   * Generate improvements based on reflection
   */
  private async generateImprovements(
    reflection: string,
    patterns: string,
    agentState: AgentState
  ): Promise<string> {
    const prompt = `Based on this reflection: ${reflection}

And these patterns: ${patterns}

What specific improvements can be made for future actions? Provide actionable recommendations.`;

    const response = await LLMOrchestrationService.generateText({
      prompt,
      temperature: 0.4,
      maxOutputTokens: 250
    });

    return response.success ? response.text! : 'No specific improvements identified.';
  }
}
