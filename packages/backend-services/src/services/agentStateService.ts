import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { logger } from '../utils/logger';
import { CallContext } from '../prompts/salesPromptTemplates';

// Agent State Interfaces
export interface AgentState {
  conversationId: string;
  userId: string;
  context: CallContext;
  memory: ConversationMemory;
  currentGoal?: string;
  planningSteps?: PlanningStep[];
  toolResults?: ToolResult[];
  lastUpdated: Date;
  createdAt: Date;
}

export interface ConversationMemory {
  shortTerm: Message[];
  workingMemory: WorkingMemoryItem[];
  longTermSummary?: string;
  userPreferences?: UserPreferences;
}

export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;
}

export interface WorkingMemoryItem {
  id: string;
  content: string;
  timestamp: Date;
  type: 'working' | 'tool_result' | 'insight';
  metadata?: any;
}

export interface UserPreferences {
  communicationStyle?: string;
  preferredApproach?: string;
  industryFocus?: string;
  customSettings?: Record<string, any>;
}

export interface PlanningStep {
  id: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  toolsRequired?: string[];
  dependencies?: string[];
  result?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface ToolResult {
  id: string;
  toolName: string;
  parameters: any;
  result: any;
  success: boolean;
  error?: string;
  executionTime: number;
  timestamp: Date;
}

/**
 * Agent State Service
 * Manages persistent agent state with Redis caching and Supabase persistence
 */
export class AgentStateService {
  private redis: Redis;
  private supabase: any;
  private cachePrefix = 'agent_state:';
  private cacheTTL = 3600; // 1 hour

  constructor() {
    // Initialize Redis client following existing pattern
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      maxRetriesPerRequest: 3,
    });

    // Initialize Supabase client following existing pattern
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    logger.info('[AgentStateService] Initialized with Redis and Supabase connections');
  }

  /**
   * Get agent state for a conversation
   * Tries Redis cache first, falls back to database
   */
  async getAgentState(conversationId: string): Promise<AgentState | null> {
    try {
      // Try Redis cache first
      const cacheKey = `${this.cachePrefix}${conversationId}`;
      const cached = await this.redis.get(cacheKey);
      
      if (cached) {
        logger.info(`[AgentStateService] Cache hit for conversation: ${conversationId}`);
        return this.deserializeState(JSON.parse(cached));
      }

      // Fallback to database
      const { data, error } = await this.supabase
        .from('agent_states')
        .select('*')
        .eq('conversation_id', conversationId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found - this is normal for new conversations
          logger.info(`[AgentStateService] No state found for conversation: ${conversationId}`);
          return null;
        }
        throw new Error(`Database query failed: ${error.message}`);
      }

      if (!data) {
        return null;
      }

      const state = this.deserializeState(data);
      
      // Cache the result for future access
      await this.cacheState(conversationId, state);
      
      logger.info(`[AgentStateService] Retrieved state from database for conversation: ${conversationId}`);
      return state;

    } catch (error: any) {
      logger.error(`[AgentStateService] Error getting agent state: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update agent state in both cache and database
   */
  async updateAgentState(state: AgentState): Promise<void> {
    try {
      state.lastUpdated = new Date();

      // Update Redis cache
      await this.cacheState(state.conversationId, state);

      // Persist to database
      const serializedState = this.serializeState(state);
      const { error } = await this.supabase
        .from('agent_states')
        .upsert(serializedState, {
          onConflict: 'conversation_id'
        });

      if (error) {
        throw new Error(`Database upsert failed: ${error.message}`);
      }

      logger.info(`[AgentStateService] Updated state for conversation: ${state.conversationId}`);

    } catch (error: any) {
      logger.error(`[AgentStateService] Error updating agent state: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create new agent state for a conversation
   */
  async createNewAgentState(
    conversationId: string, 
    userId: string, 
    context: CallContext
  ): Promise<AgentState> {
    try {
      const now = new Date();
      const state: AgentState = {
        conversationId,
        userId,
        context,
        memory: {
          shortTerm: [],
          workingMemory: [],
        },
        planningSteps: [],
        toolResults: [],
        lastUpdated: now,
        createdAt: now
      };

      await this.updateAgentState(state);
      
      logger.info(`[AgentStateService] Created new state for conversation: ${conversationId}`);
      return state;

    } catch (error: any) {
      logger.error(`[AgentStateService] Error creating agent state: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete agent state (cleanup)
   */
  async deleteAgentState(conversationId: string): Promise<void> {
    try {
      // Remove from cache
      const cacheKey = `${this.cachePrefix}${conversationId}`;
      await this.redis.del(cacheKey);

      // Remove from database
      const { error } = await this.supabase
        .from('agent_states')
        .delete()
        .eq('conversation_id', conversationId);

      if (error) {
        throw new Error(`Database delete failed: ${error.message}`);
      }

      logger.info(`[AgentStateService] Deleted state for conversation: ${conversationId}`);

    } catch (error: any) {
      logger.error(`[AgentStateService] Error deleting agent state: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get states for a user (for analytics/management)
   */
  async getUserStates(userId: string, limit: number = 10): Promise<AgentState[]> {
    try {
      const { data, error } = await this.supabase
        .from('agent_states')
        .select('*')
        .eq('user_id', userId)
        .order('last_updated', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(`Database query failed: ${error.message}`);
      }

      return data.map((item: any) => this.deserializeState(item));

    } catch (error: any) {
      logger.error(`[AgentStateService] Error getting user states: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cache agent state in Redis
   */
  private async cacheState(conversationId: string, state: AgentState): Promise<void> {
    try {
      const cacheKey = `${this.cachePrefix}${conversationId}`;
      const serialized = JSON.stringify(this.serializeState(state));
      await this.redis.setex(cacheKey, this.cacheTTL, serialized);
    } catch (error: any) {
      logger.warn(`[AgentStateService] Cache storage failed: ${error.message}`);
      // Don't throw - caching is not critical
    }
  }

  /**
   * Serialize state for database storage
   */
  private serializeState(state: AgentState): any {
    return {
      conversation_id: state.conversationId,
      user_id: state.userId,
      context: JSON.stringify(state.context),
      memory: JSON.stringify(state.memory),
      current_goal: state.currentGoal,
      planning_steps: JSON.stringify(state.planningSteps || []),
      tool_results: JSON.stringify(state.toolResults || []),
      last_updated: state.lastUpdated.toISOString(),
      created_at: state.createdAt?.toISOString() || new Date().toISOString()
    };
  }

  /**
   * Deserialize state from database format
   */
  private deserializeState(data: any): AgentState {
    return {
      conversationId: data.conversation_id,
      userId: data.user_id,
      context: JSON.parse(data.context || '{}'),
      memory: JSON.parse(data.memory || '{"shortTerm": [], "workingMemory": []}'),
      currentGoal: data.current_goal,
      planningSteps: JSON.parse(data.planning_steps || '[]'),
      toolResults: JSON.parse(data.tool_results || '[]'),
      lastUpdated: new Date(data.last_updated),
      createdAt: new Date(data.created_at || data.last_updated)
    };
  }

  /**
   * Clear cache for conversation (useful for testing/debugging)
   */
  async clearCache(conversationId?: string): Promise<void> {
    try {
      if (conversationId) {
        const cacheKey = `${this.cachePrefix}${conversationId}`;
        await this.redis.del(cacheKey);
        logger.info(`[AgentStateService] Cleared cache for conversation: ${conversationId}`);
      } else {
        const keys = await this.redis.keys(`${this.cachePrefix}*`);
        if (keys.length > 0) {
          await this.redis.del(...keys);
          logger.info(`[AgentStateService] Cleared ${keys.length} cache entries`);
        }
      }
    } catch (error: any) {
      logger.error(`[AgentStateService] Cache clearing failed: ${error.message}`);
    }
  }

  /**
   * Health check for the service
   */
  async healthCheck(): Promise<{ redis: boolean; database: boolean }> {
    const health = { redis: false, database: false };

    try {
      // Test Redis connection
      await this.redis.ping();
      health.redis = true;
    } catch (error) {
      logger.error('[AgentStateService] Redis health check failed');
    }

    try {
      // Test database connection
      const { error } = await this.supabase
        .from('agent_states')
        .select('count')
        .limit(1);
      
      health.database = !error;
    } catch (error) {
      logger.error('[AgentStateService] Database health check failed');
    }

    return health;
  }
}
