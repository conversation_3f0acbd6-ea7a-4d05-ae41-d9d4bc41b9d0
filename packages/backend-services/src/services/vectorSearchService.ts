import { OpenAI } from 'openai';
import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { logger } from '../utils/logger';

export interface SearchResult {
  id: string;
  content: string;
  similarity: number;
  documentId: string;
  filename: string; // varchar(255) from database
  chunkIndex: number;
  metadata?: any;
}

export interface SearchOptions {
  limit?: number;
  threshold?: number;
  userId?: string;
  documentIds?: string[];
  includeMetadata?: boolean;
}

export interface SearchResponse {
  results: SearchResult[];
  query: string;
  totalResults: number;
  processingTime: number;
}

export class VectorSearchService {
  private openai: OpenAI;
  private supabase: any;
  private redis: Redis;
  private cachePrefix = 'vector_search:';
  private cacheTTL = 3600; // 1 hour

  constructor() {
    logger.info(`[VectorSearch] Initializing with embedding dimensions: ${process.env.OPENAI_EMBEDDING_DIMENSIONS || '1536'}`);

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      maxRetriesPerRequest: 3,
    });
  }

  /**
   * Perform semantic search across document chunks
   */
  async search(query: string, options: SearchOptions = {}): Promise<SearchResponse> {
    const startTime = Date.now();

    try {
      logger.info(`[VectorSearch] Searching for: "${query}"`);

      const {
        limit = 10,
        threshold = 0.3, // Lower default threshold for better results
        userId,
        documentIds,
        includeMetadata = false
      } = options;

      logger.info(`[VectorSearch] Search options: limit=${limit}, threshold=${threshold}, userId=${userId}`);

      // Check cache first
      const cacheKey = this.generateCacheKey(query, options);
      const cachedResult = await this.getCachedResult(cacheKey);
      
      if (cachedResult) {
        logger.info(`[VectorSearch] Cache hit for query: "${query}"`);
        return {
          ...cachedResult,
          processingTime: Date.now() - startTime
        };
      }

      // Generate query embedding
      logger.info(`[VectorSearch] About to generate query embedding...`);
      const queryEmbedding = await this.generateQueryEmbedding(query);
      logger.info(`[VectorSearch] Query embedding generated with ${queryEmbedding.length} dimensions`);

      // Build search query
      let searchQuery = this.supabase
        .from('document_chunks')
        .select(`
          id,
          content,
          chunk_index,
          document_id,
          documents!inner(
            id,
            filename,
            mimetype,
            user_id
          )
        `)
        .gte('similarity', threshold)
        .order('similarity', { ascending: false })
        .limit(limit);

      // Add user filter if provided
      if (userId) {
        searchQuery = searchQuery.eq('documents.user_id', userId);
      }

      // Add document filter if provided
      if (documentIds && documentIds.length > 0) {
        searchQuery = searchQuery.in('document_id', documentIds);
      }

      // Execute vector similarity search using pgvector
      const { data: chunks, error } = await this.supabase.rpc(
        'search_document_chunks',
        {
          query_embedding: queryEmbedding,
          similarity_threshold: threshold,
          match_count: limit,
          user_id_filter: userId,
          document_ids_filter: documentIds
        }
      );

      logger.info(`[VectorSearch] RPC call completed. Error: ${error ? error.message : 'none'}, Results: ${chunks ? chunks.length : 0}`);

      if (error) {
        logger.error(`[VectorSearch] Search failed: ${error.message}`);
        logger.error(`[VectorSearch] Error details:`, error);

        // If the function doesn't exist, provide helpful error message
        if (error.message.includes('function search_document_chunks')) {
          throw new Error('Vector search function not found. Please run the SQL setup script to create search_document_chunks function.');
        }

        throw new Error(`Vector search failed: ${error.message}`);
      }

      // Check if we got results
      if (!chunks || chunks.length === 0) {
        logger.info(`[VectorSearch] No results found for query: "${query}" with threshold ${threshold}`);
        logger.info(`[VectorSearch] Consider lowering the similarity threshold or checking if documents exist for user ${userId}`);
      }

      // Format results
      const results: SearchResult[] = (chunks || []).map((chunk: any) => ({
        id: chunk.id,
        content: chunk.content,
        similarity: chunk.similarity,
        documentId: chunk.document_id,
        filename: chunk.filename,
        chunkIndex: chunk.chunk_index,
        metadata: includeMetadata ? {
          // Note: metadata fields may not be available from the function
          // Add them if needed by modifying the SQL function
        } : undefined
      }));

      const response: SearchResponse = {
        results,
        query,
        totalResults: results.length,
        processingTime: Date.now() - startTime
      };

      // Cache the result
      await this.cacheResult(cacheKey, response);

      logger.info(`[VectorSearch] Found ${results.length} results in ${response.processingTime}ms`);
      
      return response;
    } catch (error: any) {
      logger.error(`[VectorSearch] Search failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get relevant context for RAG
   */
  async getRelevantContext(
    query: string, 
    options: SearchOptions = {}
  ): Promise<{ context: string; sources: SearchResult[] }> {
    try {
      const searchResponse = await this.search(query, {
        ...options,
        limit: options.limit || 5,
        threshold: options.threshold || 0.3
      });

      // Combine relevant chunks into context
      const context = searchResponse.results
        .map(result => result.content)
        .join('\n\n');

      return {
        context,
        sources: searchResponse.results
      };
    } catch (error: any) {
      logger.error(`[VectorSearch] Context retrieval failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate embedding for search query
   */
  private async generateQueryEmbedding(query: string): Promise<number[]> {
    const maxRetries = 2;
    const timeoutMs = 10000; // 10 second timeout

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const dimensions = parseInt(process.env.OPENAI_EMBEDDING_DIMENSIONS || '1536');
        logger.info(`[VectorSearch] Generating query embedding (attempt ${attempt}/${maxRetries}) with ${dimensions} dimensions`);

        // Create a timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('OpenAI API timeout')), timeoutMs);
        });

        // Race between the API call and timeout
        const apiPromise = this.openai.embeddings.create({
          model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-large',
          input: query,
          dimensions: dimensions
        });

        const response = await Promise.race([apiPromise, timeoutPromise]) as any;
        const embedding = response.data[0].embedding;
        logger.info(`[VectorSearch] Generated embedding with ${embedding.length} dimensions`);
        return embedding;
      } catch (error: any) {
        logger.error(`[VectorSearch] Query embedding generation failed (attempt ${attempt}/${maxRetries}): ${error.message}`);

        if (attempt === maxRetries) {
          // On final failure, return a fallback or throw
          throw new Error(`OpenAI API failed after ${maxRetries} attempts: ${error.message}`);
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }

    throw new Error('Unexpected error in embedding generation');
  }

  /**
   * Generate cache key for search query
   */
  private generateCacheKey(query: string, options: SearchOptions): string {
    const optionsHash = Buffer.from(JSON.stringify(options)).toString('base64');
    const queryHash = Buffer.from(query).toString('base64');
    return `${this.cachePrefix}${queryHash}:${optionsHash}`;
  }

  /**
   * Get cached search result
   */
  private async getCachedResult(cacheKey: string): Promise<SearchResponse | null> {
    try {
      const cached = await this.redis.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error: any) {
      logger.warn(`[VectorSearch] Cache retrieval failed: ${error.message}`);
      return null;
    }
  }

  /**
   * Cache search result
   */
  private async cacheResult(cacheKey: string, result: SearchResponse): Promise<void> {
    try {
      await this.redis.setex(cacheKey, this.cacheTTL, JSON.stringify(result));
    } catch (error: any) {
      logger.warn(`[VectorSearch] Cache storage failed: ${error.message}`);
    }
  }

  /**
   * Clear cache for user or specific patterns
   */
  async clearCache(pattern?: string): Promise<void> {
    try {
      const searchPattern = pattern || `${this.cachePrefix}*`;
      const keys = await this.redis.keys(searchPattern);
      
      if (keys.length > 0) {
        await this.redis.del(...keys);
        logger.info(`[VectorSearch] Cleared ${keys.length} cache entries`);
      }
    } catch (error: any) {
      logger.error(`[VectorSearch] Cache clearing failed: ${error.message}`);
    }
  }

  /**
   * Get search analytics
   */
  async getSearchAnalytics(userId?: string): Promise<any> {
    try {
      // Get document count
      let documentQuery = this.supabase
        .from('documents')
        .select('count', { count: 'exact' })
        .eq('processing_status', 'completed');

      if (userId) {
        documentQuery = documentQuery.eq('user_id', userId);
      }

      const { count: documentCount } = await documentQuery.single();

      // Get chunk count
      let chunkQuery = this.supabase
        .from('document_chunks')
        .select('count', { count: 'exact' });

      if (userId) {
        chunkQuery = chunkQuery.eq('user_id', userId);
      }

      const { count: chunkCount } = await chunkQuery.single();

      return {
        totalDocuments: documentCount || 0,
        totalChunks: chunkCount || 0,
        cacheStats: await this.getCacheStats()
      };
    } catch (error: any) {
      logger.error(`[VectorSearch] Analytics retrieval failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get cache statistics
   */
  private async getCacheStats(): Promise<any> {
    try {
      const keys = await this.redis.keys(`${this.cachePrefix}*`);
      return {
        totalCachedQueries: keys.length,
        cacheHitRate: 'N/A' // Would need to implement hit/miss tracking
      };
    } catch (error: any) {
      logger.warn(`[VectorSearch] Cache stats retrieval failed: ${error.message}`);
      return { totalCachedQueries: 0, cacheHitRate: 'N/A' };
    }
  }

  /**
   * Health check for vector search service
   */
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      // Test database connection
      const { data: dbTest } = await this.supabase
        .from('documents')
        .select('count', { count: 'exact' })
        .limit(1);

      // Test Redis connection
      await this.redis.ping();

      // Test OpenAI API
      await this.openai.embeddings.create({
        model: 'text-embedding-3-large',
        input: 'health check',
        dimensions: 1536
      });

      return {
        status: 'healthy',
        details: {
          database: 'connected',
          redis: 'connected',
          openai: 'connected',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error: any) {
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}
