import { AgentState } from './agentStateService';
import { MemoryService } from './memoryService';
import { ToolOrchestrator, ToolResult } from './toolOrchestrator';
import { LLMOrchestrationService } from './llmOrchestrationService';
import { logger } from '../utils/logger';
import { createClient } from '@supabase/supabase-js';

// Planning interfaces
export interface Goal {
  id: string;
  description: string;
  priority: number; // 1-10 scale
  deadline?: Date;
  status: 'active' | 'completed' | 'failed' | 'paused';
  parentGoalId?: string;
  subGoals?: Goal[];
  context?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlanningStep {
  id: string;
  goalId: string;
  description: string;
  action: 'tool_use' | 'information_gathering' | 'analysis' | 'communication' | 'decision';
  toolName?: string;
  toolParams?: any;
  dependencies?: string[]; // IDs of steps that must complete first
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  result?: any;
  reasoning?: string;
  estimatedDuration?: number; // minutes
  actualDuration?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlanningSession {
  id: string;
  conversationId: string;
  userId: string;
  goal: Goal;
  steps: PlanningStep[];
  strategy: PlanningStrategy;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100 percentage
  startedAt: Date;
  completedAt?: Date;
  metadata?: any;
}

export interface PlanningStrategy {
  name: 'sequential' | 'parallel' | 'adaptive' | 'react' | 'chain_of_thought';
  description: string;
  parameters?: any;
}

export interface PlanningContext {
  agentState: AgentState;
  availableTools: string[];
  timeConstraints?: {
    maxDuration: number; // minutes
    deadline?: Date;
  };
  resources?: {
    maxToolCalls: number;
    maxSteps: number;
  };
}

/**
 * Planning Engine
 * Implements goal decomposition, strategy selection, and step-by-step planning
 * with support for ReAct and Chain-of-Thought reasoning patterns
 */
export class PlanningEngine {
  private memoryService: MemoryService;
  private toolOrchestrator: ToolOrchestrator;
  private supabase: any;
  private activeSessions: Map<string, PlanningSession> = new Map();

  constructor() {
    this.memoryService = new MemoryService();
    this.toolOrchestrator = new ToolOrchestrator();
    
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    logger.info('[PlanningEngine] Initialized with goal decomposition and strategy selection');
  }

  /**
   * Create a new planning session for a goal
   */
  async createPlanningSession(
    goal: string,
    context: PlanningContext,
    strategy: PlanningStrategy = { name: 'adaptive', description: 'Adaptive planning with dynamic strategy selection' }
  ): Promise<PlanningSession> {
    try {
      const sessionId = `planning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const goalObj: Goal = {
        id: `goal_${Date.now()}`,
        description: goal,
        priority: 5,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Decompose goal into steps
      const steps = await this.decomposeGoal(goalObj, context, strategy);

      const session: PlanningSession = {
        id: sessionId,
        conversationId: context.agentState.conversationId,
        userId: context.agentState.userId,
        goal: goalObj,
        steps,
        strategy,
        status: 'active',
        progress: 0,
        startedAt: new Date(),
        metadata: {
          totalSteps: steps.length,
          estimatedDuration: steps.reduce((sum, step) => sum + (step.estimatedDuration || 5), 0)
        }
      };

      // Store session in database
      await this.storePlanningSession(session);

      // Cache active session
      this.activeSessions.set(sessionId, session);

      // Add planning session to agent memory
      await this.memoryService.addMemory(
        context.agentState,
        `Started planning session for goal: ${goal}`,
        'insight',
        7,
        { sessionId, strategy: strategy.name, stepCount: steps.length }
      );

      logger.info(`[PlanningEngine] Created planning session: ${sessionId} with ${steps.length} steps`);
      return session;

    } catch (error: any) {
      logger.error(`[PlanningEngine] Error creating planning session: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute the next step in a planning session
   */
  async executeNextStep(sessionId: string, context: PlanningContext): Promise<{ step: PlanningStep; result: any; completed: boolean }> {
    try {
      const session = this.activeSessions.get(sessionId) || await this.loadPlanningSession(sessionId);
      if (!session) {
        throw new Error(`Planning session not found: ${sessionId}`);
      }

      // Find next pending step that has all dependencies completed
      const nextStep = this.findNextExecutableStep(session);
      if (!nextStep) {
        // Check if all steps are completed
        const allCompleted = session.steps.every(step => 
          step.status === 'completed' || step.status === 'skipped'
        );
        
        if (allCompleted) {
          await this.completePlanningSession(session);
          return { step: nextStep!, result: null, completed: true };
        } else {
          throw new Error('No executable steps found - possible dependency deadlock');
        }
      }

      // Update step status
      nextStep.status = 'in_progress';
      nextStep.updatedAt = new Date();

      const startTime = Date.now();
      let result: any;

      // Execute step based on action type
      switch (nextStep.action) {
        case 'tool_use':
          result = await this.executeToolStep(nextStep, context);
          break;
        case 'information_gathering':
          result = await this.executeInformationGatheringStep(nextStep, context);
          break;
        case 'analysis':
          result = await this.executeAnalysisStep(nextStep, context);
          break;
        case 'communication':
          result = await this.executeCommunicationStep(nextStep, context);
          break;
        case 'decision':
          result = await this.executeDecisionStep(nextStep, context);
          break;
        default:
          throw new Error(`Unknown step action: ${nextStep.action}`);
      }

      // Update step with result
      nextStep.status = result.success ? 'completed' : 'failed';
      nextStep.result = result;
      nextStep.actualDuration = Math.round((Date.now() - startTime) / 1000 / 60); // minutes
      nextStep.updatedAt = new Date();

      // Update session progress
      const completedSteps = session.steps.filter(s => s.status === 'completed').length;
      session.progress = Math.round((completedSteps / session.steps.length) * 100);

      // Store updated session
      await this.storePlanningSession(session);

      // Add step result to working memory
      await this.memoryService.addToWorkingMemory(context.agentState, {
        stepId: nextStep.id,
        action: nextStep.action,
        result: result,
        reasoning: nextStep.reasoning
      });

      logger.info(`[PlanningEngine] Executed step ${nextStep.id}: ${nextStep.status}`);

      // Check if planning session is complete
      const sessionCompleted = session.steps.every(step => 
        step.status === 'completed' || step.status === 'skipped'
      );

      if (sessionCompleted) {
        await this.completePlanningSession(session);
      }

      return { step: nextStep, result, completed: sessionCompleted };

    } catch (error: any) {
      logger.error(`[PlanningEngine] Error executing step: ${error.message}`);
      throw error;
    }
  }

  /**
   * Decompose a goal into actionable steps using LLM reasoning
   */
  private async decomposeGoal(
    goal: Goal, 
    context: PlanningContext, 
    strategy: PlanningStrategy
  ): Promise<PlanningStep[]> {
    try {
      const availableTools = context.availableTools.join(', ');
      const memoryContext = await this.memoryService.getMemoryContext(
        context.agentState, 
        goal.description
      );

      const decompositionPrompt = `You are an AI planning expert. Decompose the following goal into specific, actionable steps.

GOAL: ${goal.description}

CONTEXT:
${memoryContext}

AVAILABLE TOOLS: ${availableTools}

STRATEGY: ${strategy.name} - ${strategy.description}

CONSTRAINTS:
- Maximum ${context.resources?.maxSteps || 10} steps
- Maximum ${context.resources?.maxToolCalls || 5} tool calls
- Time limit: ${context.timeConstraints?.maxDuration || 30} minutes

Please create a step-by-step plan. For each step, specify:
1. Description (what needs to be done)
2. Action type (tool_use, information_gathering, analysis, communication, decision)
3. Tool name (if action is tool_use)
4. Tool parameters (if applicable)
5. Dependencies (which previous steps must complete first)
6. Estimated duration in minutes
7. Reasoning (why this step is necessary)

Format your response as a JSON array of steps:
[
  {
    "description": "Step description",
    "action": "tool_use|information_gathering|analysis|communication|decision",
    "toolName": "tool_name_if_applicable",
    "toolParams": {"param": "value"},
    "dependencies": ["step_1", "step_2"],
    "estimatedDuration": 5,
    "reasoning": "Why this step is needed"
  }
]

Focus on creating a logical sequence that achieves the goal efficiently.`;

      const response = await LLMOrchestrationService.generateText({
        prompt: decompositionPrompt,
        temperature: 0.3,
        maxOutputTokens: 1000
      });

      if (!response.success) {
        throw new Error(`Goal decomposition failed: ${response.error}`);
      }

      // Parse the LLM response to extract steps
      const stepsData = this.parseStepsFromResponse(response.text!);
      
      // Convert to PlanningStep objects
      const steps: PlanningStep[] = stepsData.map((stepData: any, index: number) => ({
        id: `step_${index + 1}`,
        goalId: goal.id,
        description: stepData.description,
        action: stepData.action,
        toolName: stepData.toolName,
        toolParams: stepData.toolParams,
        dependencies: stepData.dependencies || [],
        status: 'pending',
        reasoning: stepData.reasoning,
        estimatedDuration: stepData.estimatedDuration || 5,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      logger.info(`[PlanningEngine] Decomposed goal into ${steps.length} steps using ${strategy.name} strategy`);
      return steps;

    } catch (error: any) {
      logger.error(`[PlanningEngine] Goal decomposition error: ${error.message}`);
      // Fallback to simple decomposition
      return this.createFallbackPlan(goal, context);
    }
  }

  /**
   * Parse steps from LLM response
   */
  private parseStepsFromResponse(response: string): any[] {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback parsing if JSON is not properly formatted
      throw new Error('Could not parse JSON from response');
    } catch (error) {
      logger.warn('[PlanningEngine] Failed to parse LLM response, using fallback');
      return [];
    }
  }

  /**
   * Create a simple fallback plan when LLM decomposition fails
   */
  private createFallbackPlan(goal: Goal, context: PlanningContext): PlanningStep[] {
    return [
      {
        id: 'step_1',
        goalId: goal.id,
        description: 'Gather relevant information',
        action: 'information_gathering',
        status: 'pending',
        reasoning: 'Need to collect context before proceeding',
        estimatedDuration: 5,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'step_2',
        goalId: goal.id,
        description: 'Analyze the situation',
        action: 'analysis',
        dependencies: ['step_1'],
        status: 'pending',
        reasoning: 'Analysis needed to determine best approach',
        estimatedDuration: 5,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'step_3',
        goalId: goal.id,
        description: 'Execute the plan',
        action: 'tool_use',
        toolName: 'search_knowledge',
        toolParams: { query: goal.description },
        dependencies: ['step_2'],
        status: 'pending',
        reasoning: 'Execute based on analysis',
        estimatedDuration: 10,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
  }

  /**
   * Find the next step that can be executed (all dependencies met)
   */
  private findNextExecutableStep(session: PlanningSession): PlanningStep | null {
    return session.steps.find(step => {
      if (step.status !== 'pending') return false;
      
      // Check if all dependencies are completed
      if (step.dependencies && step.dependencies.length > 0) {
        return step.dependencies.every(depId => {
          const depStep = session.steps.find(s => s.id === depId);
          return depStep && depStep.status === 'completed';
        });
      }
      
      return true;
    }) || null;
  }

  /**
   * Execute a tool-use step
   */
  private async executeToolStep(step: PlanningStep, context: PlanningContext): Promise<any> {
    if (!step.toolName) {
      return { success: false, error: 'Tool name not specified for tool_use step' };
    }

    const result = await this.toolOrchestrator.executeTool(
      step.toolName,
      step.toolParams || {},
      {
        agentState: context.agentState,
        userId: context.agentState.userId,
        conversationId: context.agentState.conversationId
      }
    );

    return result;
  }

  /**
   * Execute an information gathering step
   */
  private async executeInformationGatheringStep(step: PlanningStep, context: PlanningContext): Promise<any> {
    // Use memory service to gather relevant information
    const relevantMemories = await this.memoryService.retrieveRelevantMemories(
      step.description,
      context.agentState.conversationId,
      { limit: 5, threshold: 0.7 }
    );

    return {
      success: true,
      data: {
        memories: relevantMemories,
        context: context.agentState.context,
        workingMemory: context.agentState.memory.workingMemory
      }
    };
  }

  /**
   * Execute an analysis step
   */
  private async executeAnalysisStep(step: PlanningStep, context: PlanningContext): Promise<any> {
    const analysisPrompt = `Analyze the following situation for the step: ${step.description}

REASONING: ${step.reasoning}

CONTEXT: ${JSON.stringify(context.agentState.context)}

WORKING MEMORY: ${JSON.stringify(context.agentState.memory.workingMemory)}

Provide a brief analysis and recommendations for next steps.`;

    const response = await LLMOrchestrationService.generateText({
      prompt: analysisPrompt,
      temperature: 0.5,
      maxOutputTokens: 300
    });

    return {
      success: response.success,
      data: response.text,
      error: response.error
    };
  }

  /**
   * Execute a communication step
   */
  private async executeCommunicationStep(step: PlanningStep, context: PlanningContext): Promise<any> {
    // For now, this is a placeholder - would integrate with actual communication tools
    return {
      success: true,
      data: {
        message: `Communication step executed: ${step.description}`,
        type: 'internal_note'
      }
    };
  }

  /**
   * Execute a decision step
   */
  private async executeDecisionStep(step: PlanningStep, context: PlanningContext): Promise<any> {
    const decisionPrompt = `Make a decision for: ${step.description}

REASONING: ${step.reasoning}

AVAILABLE OPTIONS: Based on the context and previous steps, what are the possible decisions?

CONTEXT: ${JSON.stringify(context.agentState.context)}

Provide your decision and reasoning.`;

    const response = await LLMOrchestrationService.generateText({
      prompt: decisionPrompt,
      temperature: 0.3,
      maxOutputTokens: 200
    });

    return {
      success: response.success,
      data: {
        decision: response.text,
        reasoning: step.reasoning
      },
      error: response.error
    };
  }

  /**
   * Complete a planning session
   */
  private async completePlanningSession(session: PlanningSession): Promise<void> {
    session.status = 'completed';
    session.completedAt = new Date();
    session.progress = 100;

    await this.storePlanningSession(session);
    this.activeSessions.delete(session.id);

    logger.info(`[PlanningEngine] Completed planning session: ${session.id}`);
  }

  /**
   * Store planning session in database
   */
  private async storePlanningSession(session: PlanningSession): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('planning_sessions')
        .upsert({
          id: session.id,
          conversation_id: session.conversationId,
          user_id: session.userId,
          goal: JSON.stringify(session.goal),
          steps: JSON.stringify(session.steps),
          status: session.status,
          started_at: session.startedAt.toISOString(),
          completed_at: session.completedAt?.toISOString()
        }, { onConflict: 'id' });

      if (error) {
        throw new Error(`Failed to store planning session: ${error.message}`);
      }
    } catch (error: any) {
      logger.error(`[PlanningEngine] Storage error: ${error.message}`);
    }
  }

  /**
   * Load planning session from database
   */
  private async loadPlanningSession(sessionId: string): Promise<PlanningSession | null> {
    try {
      const { data, error } = await this.supabase
        .from('planning_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error || !data) {
        return null;
      }

      const session: PlanningSession = {
        id: data.id,
        conversationId: data.conversation_id,
        userId: data.user_id,
        goal: JSON.parse(data.goal),
        steps: JSON.parse(data.steps),
        strategy: { name: 'adaptive', description: 'Loaded session' },
        status: data.status,
        progress: 0,
        startedAt: new Date(data.started_at),
        completedAt: data.completed_at ? new Date(data.completed_at) : undefined
      };

      // Calculate progress
      const completedSteps = session.steps.filter(s => s.status === 'completed').length;
      session.progress = Math.round((completedSteps / session.steps.length) * 100);

      return session;
    } catch (error: any) {
      logger.error(`[PlanningEngine] Load error: ${error.message}`);
      return null;
    }
  }

  /**
   * Get active planning sessions for a conversation
   */
  async getActiveSessions(conversationId: string): Promise<PlanningSession[]> {
    try {
      const { data, error } = await this.supabase
        .from('planning_sessions')
        .select('*')
        .eq('conversation_id', conversationId)
        .eq('status', 'active');

      if (error) {
        throw new Error(`Failed to get active sessions: ${error.message}`);
      }

      return data.map((item: any) => ({
        id: item.id,
        conversationId: item.conversation_id,
        userId: item.user_id,
        goal: JSON.parse(item.goal),
        steps: JSON.parse(item.steps),
        strategy: { name: 'adaptive', description: 'Loaded session' },
        status: item.status,
        progress: 0,
        startedAt: new Date(item.started_at),
        completedAt: item.completed_at ? new Date(item.completed_at) : undefined
      }));
    } catch (error: any) {
      logger.error(`[PlanningEngine] Error getting active sessions: ${error.message}`);
      return [];
    }
  }

  /**
   * Cancel a planning session
   */
  async cancelPlanningSession(sessionId: string): Promise<void> {
    try {
      const session = this.activeSessions.get(sessionId) || await this.loadPlanningSession(sessionId);
      if (session) {
        session.status = 'cancelled';
        session.completedAt = new Date();
        await this.storePlanningSession(session);
        this.activeSessions.delete(sessionId);
      }
    } catch (error: any) {
      logger.error(`[PlanningEngine] Error cancelling session: ${error.message}`);
    }
  }
}
