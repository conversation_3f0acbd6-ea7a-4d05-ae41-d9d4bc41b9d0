-- Agent System Database Schema Migration
-- This migration creates the necessary tables for the AI agent system
-- including agent states, memories, and related functionality

-- Enable pgvector extension if not already enabled (for agent memories)
CREATE EXTENSION IF NOT EXISTS vector;

-- Create agent_states table for persistent agent state management
CREATE TABLE IF NOT EXISTS agent_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id TEXT UNIQUE NOT NULL,
    user_id TEXT NOT NULL,
    context JSONB NOT NULL DEFAULT '{}',
    memory JSONB NOT NULL DEFAULT '{"shortTerm": [], "workingMemory": [], "longTermSummary": null, "userPreferences": null}',
    current_goal TEXT,
    planning_steps JSONB DEFAULT '[]',
    tool_results JSONB DEFAULT '[]',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_conversation_id CHECK (length(conversation_id) > 0),
    CONSTRAINT valid_user_id CHECK (length(user_id) > 0)
);

-- Create agent_memories table for long-term memory storage with vector embeddings
CREATE TABLE IF NOT EXISTS agent_memories (
    id TEXT PRIMARY KEY,
    conversation_id TEXT NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI text-embedding-ada-002 dimensions (compatible with pgvector indexes)
    memory_type VARCHAR(20) NOT NULL DEFAULT 'conversation' CHECK (memory_type IN ('conversation', 'insight', 'preference', 'outcome', 'working')),
    importance INTEGER NOT NULL DEFAULT 5 CHECK (importance >= 1 AND importance <= 10),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_memory_content CHECK (length(content) > 0),
    CONSTRAINT valid_conversation_id_memory CHECK (length(conversation_id) > 0)
);

-- Create tool_executions table for tracking tool usage and results
CREATE TABLE IF NOT EXISTS tool_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    tool_name VARCHAR(100) NOT NULL,
    parameters JSONB NOT NULL DEFAULT '{}',
    result JSONB,
    success BOOLEAN NOT NULL DEFAULT false,
    error_message TEXT,
    execution_time_ms INTEGER,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_tool_name CHECK (length(tool_name) > 0),
    CONSTRAINT valid_execution_time CHECK (execution_time_ms IS NULL OR execution_time_ms >= 0)
);

-- Create planning_sessions table for tracking agent planning activities
CREATE TABLE IF NOT EXISTS planning_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    goal TEXT NOT NULL,
    steps JSONB NOT NULL DEFAULT '[]',
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed', 'cancelled')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT valid_planning_goal CHECK (length(goal) > 0)
);

-- Create indexes for performance optimization

-- Agent States indexes
CREATE INDEX IF NOT EXISTS idx_agent_states_conversation_id ON agent_states(conversation_id);
CREATE INDEX IF NOT EXISTS idx_agent_states_user_id ON agent_states(user_id);
CREATE INDEX IF NOT EXISTS idx_agent_states_last_updated ON agent_states(last_updated DESC);
CREATE INDEX IF NOT EXISTS idx_agent_states_created_at ON agent_states(created_at DESC);

-- Agent Memories indexes
CREATE INDEX IF NOT EXISTS idx_agent_memories_conversation_id ON agent_memories(conversation_id);
CREATE INDEX IF NOT EXISTS idx_agent_memories_type ON agent_memories(memory_type);
CREATE INDEX IF NOT EXISTS idx_agent_memories_importance ON agent_memories(importance DESC);
CREATE INDEX IF NOT EXISTS idx_agent_memories_created_at ON agent_memories(created_at DESC);

-- Vector similarity search index for agent memories (using ivfflat for high dimensions)
CREATE INDEX IF NOT EXISTS idx_agent_memories_embedding ON agent_memories
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Tool Executions indexes
CREATE INDEX IF NOT EXISTS idx_tool_executions_conversation_id ON tool_executions(conversation_id);
CREATE INDEX IF NOT EXISTS idx_tool_executions_user_id ON tool_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_tool_executions_tool_name ON tool_executions(tool_name);
CREATE INDEX IF NOT EXISTS idx_tool_executions_executed_at ON tool_executions(executed_at DESC);
CREATE INDEX IF NOT EXISTS idx_tool_executions_success ON tool_executions(success);

-- Planning Sessions indexes
CREATE INDEX IF NOT EXISTS idx_planning_sessions_conversation_id ON planning_sessions(conversation_id);
CREATE INDEX IF NOT EXISTS idx_planning_sessions_user_id ON planning_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_planning_sessions_status ON planning_sessions(status);
CREATE INDEX IF NOT EXISTS idx_planning_sessions_started_at ON planning_sessions(started_at DESC);

-- Create stored procedures for agent memory operations

-- Function to search agent memories with vector similarity
CREATE OR REPLACE FUNCTION search_agent_memories(
    query_embedding vector(1536),
    conversation_id_filter text DEFAULT NULL,
    similarity_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    memory_type_filter text DEFAULT NULL,
    importance_threshold int DEFAULT 1
)
RETURNS TABLE (
    id text,
    content text,
    memory_type text,
    importance int,
    similarity float,
    conversation_id text,
    created_at timestamptz,
    metadata jsonb
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.content,
        m.memory_type,
        m.importance,
        (m.embedding <=> query_embedding) * -1 + 1 AS similarity,
        m.conversation_id,
        m.created_at,
        m.metadata
    FROM agent_memories m
    WHERE 
        (conversation_id_filter IS NULL OR m.conversation_id = conversation_id_filter)
        AND (memory_type_filter IS NULL OR m.memory_type = memory_type_filter)
        AND m.importance >= importance_threshold
        AND (m.embedding <=> query_embedding) * -1 + 1 >= similarity_threshold
    ORDER BY m.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Function to clean up old agent states (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_agent_states(
    retention_days int DEFAULT 30
)
RETURNS int
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count int;
BEGIN
    DELETE FROM agent_states 
    WHERE last_updated < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- Function to get agent state with memory context
CREATE OR REPLACE FUNCTION get_agent_state_with_context(
    conversation_id_param text
)
RETURNS TABLE (
    state_data jsonb,
    recent_memories jsonb
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        to_jsonb(s.*) as state_data,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'id', m.id,
                    'content', m.content,
                    'type', m.memory_type,
                    'importance', m.importance,
                    'created_at', m.created_at
                )
                ORDER BY m.created_at DESC
            ) FILTER (WHERE m.id IS NOT NULL),
            '[]'::jsonb
        ) as recent_memories
    FROM agent_states s
    LEFT JOIN agent_memories m ON s.conversation_id = m.conversation_id 
        AND m.created_at > NOW() - INTERVAL '24 hours'
    WHERE s.conversation_id = conversation_id_param
    GROUP BY s.id, s.conversation_id, s.user_id, s.context, s.memory, 
             s.current_goal, s.planning_steps, s.tool_results, 
             s.last_updated, s.created_at;
END;
$$;

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_last_updated_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to agent_states table
CREATE TRIGGER update_agent_states_last_updated 
    BEFORE UPDATE ON agent_states 
    FOR EACH ROW 
    EXECUTE FUNCTION update_last_updated_column();

-- Grant appropriate permissions (adjust based on your user setup)
-- These would typically be customized based on your specific user roles
-- GRANT SELECT, INSERT, UPDATE, DELETE ON agent_states TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON agent_memories TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON tool_executions TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON planning_sessions TO your_app_user;

-- Add comments for documentation
COMMENT ON TABLE agent_states IS 'Stores persistent state for AI agents across conversations';
COMMENT ON TABLE agent_memories IS 'Stores agent memories with vector embeddings for semantic search';
COMMENT ON TABLE tool_executions IS 'Tracks tool usage and execution results for agents';
COMMENT ON TABLE planning_sessions IS 'Stores agent planning sessions and goal tracking';

COMMENT ON COLUMN agent_states.conversation_id IS 'Unique identifier for the conversation/session';
COMMENT ON COLUMN agent_states.context IS 'Current conversation context and metadata';
COMMENT ON COLUMN agent_states.memory IS 'Short-term and working memory for the agent';
COMMENT ON COLUMN agent_memories.embedding IS 'Vector embedding for semantic similarity search';
COMMENT ON COLUMN agent_memories.importance IS 'Importance score from 1-10 for memory prioritization';
