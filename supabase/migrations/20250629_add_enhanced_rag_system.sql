-- Enhanced RAG System Migration
-- This adds the new RAG tables alongside the existing ones

-- Update document_chunks table to support the new RAG system
-- Keep embedding dimension at 1536 for compatibility with vector indexes
-- (OpenAI text-embedding-3-large can be configured to output 1536 dimensions)
ALTER TABLE public.document_chunks
DROP COLUMN IF EXISTS embedding;

ALTER TABLE public.document_chunks
ADD COLUMN embedding vector(1536);

-- Add new columns for enhanced document processing
ALTER TABLE public.document_chunks 
ADD COLUMN IF NOT EXISTS chunk_index INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS document_id UUID REFERENCES public.documents(id) ON DELETE CASCADE;

-- Update documents table for enhanced RAG functionality
ALTER TABLE public.documents 
ADD COLUMN IF NOT EXISTS filename VARCHAR(255),
ADD COLUMN IF NOT EXISTS mimetype VARCHAR(100),
ADD COLUMN IF NOT EXISTS size BIGINT,
ADD COLUMN IF NOT EXISTS processing_status VARCHAR(20) DEFAULT 'queued' CHECK (processing_status IN ('queued', 'processing', 'completed', 'failed')),
ADD COLUMN IF NOT EXISTS uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS chunk_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS error_message TEXT;

-- Create search analytics table for RAG system monitoring
CREATE TABLE IF NOT EXISTS public.search_analytics (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    results_count INTEGER NOT NULL DEFAULT 0,
    processing_time_ms INTEGER NOT NULL DEFAULT 0,
    similarity_threshold FLOAT NOT NULL DEFAULT 0.7,
    document_ids_filter UUID[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_documents_user_id ON public.documents(user_id);
CREATE INDEX IF NOT EXISTS idx_documents_status ON public.documents(processing_status);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_at ON public.documents(uploaded_at DESC);

CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id ON public.document_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_document_chunks_user_id ON public.document_chunks(user_id);

-- Drop old vector index and create new index for vector similarity search
DROP INDEX IF EXISTS document_chunks_embedding_idx;
-- Use ivfflat index for 1536 dimensions
CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding ON public.document_chunks
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

CREATE INDEX IF NOT EXISTS idx_search_analytics_user_id ON public.search_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_created_at ON public.search_analytics(created_at DESC);

-- Update the vector search function for the new schema
DROP FUNCTION IF EXISTS match_documents(vector, float, int);

CREATE OR REPLACE FUNCTION search_document_chunks(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    user_id_filter UUID DEFAULT NULL,
    document_ids_filter UUID[] DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    similarity float,
    document_id UUID,
    filename TEXT,
    mimetype TEXT,
    chunk_index int,
    uploaded_at timestamptz,
    processed_at timestamptz
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.content,
        1 - (dc.embedding <=> query_embedding) as similarity,
        dc.document_id,
        d.filename::TEXT,
        d.mimetype::TEXT,
        dc.chunk_index,
        d.uploaded_at,
        d.processed_at
    FROM public.document_chunks dc
    JOIN public.documents d ON dc.document_id = d.id
    WHERE 
        d.processing_status = 'completed'
        AND (user_id_filter IS NULL OR dc.user_id = user_id_filter)
        AND (document_ids_filter IS NULL OR d.id = ANY(document_ids_filter))
        AND (1 - (dc.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY dc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Create function to log search analytics
CREATE OR REPLACE FUNCTION log_search_analytics(
    p_user_id UUID,
    p_query text,
    p_results_count int,
    p_processing_time_ms int,
    p_similarity_threshold float DEFAULT 0.7,
    p_document_ids_filter UUID[] DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.search_analytics (
        user_id,
        query,
        results_count,
        processing_time_ms,
        similarity_threshold,
        document_ids_filter
    ) VALUES (
        p_user_id,
        p_query,
        p_results_count,
        p_processing_time_ms,
        p_similarity_threshold,
        p_document_ids_filter
    );
END;
$$;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_documents_updated_at ON public.documents;
CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON public.documents 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_document_chunks_updated_at ON public.document_chunks;
CREATE TRIGGER update_document_chunks_updated_at 
    BEFORE UPDATE ON public.document_chunks 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies for new table
ALTER TABLE public.search_analytics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own search analytics" ON public.search_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own search analytics" ON public.search_analytics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON public.search_analytics TO anon, authenticated;
GRANT EXECUTE ON FUNCTION search_document_chunks TO anon, authenticated;
GRANT EXECUTE ON FUNCTION log_search_analytics TO anon, authenticated;
