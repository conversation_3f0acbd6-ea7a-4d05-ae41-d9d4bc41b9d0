# End-to-End AI Pipeline Implementation Guide

**Priority:** Critical (Week 3)  
**Estimated Time:** 5 days  
**Dependencies:** RAG system, audio transcription, existing AI service

## Feature Overview

Create a complete AI pipeline that combines screenshot context, live transcription, and RAG-retrieved knowledge to provide real-time, contextually-aware sales assistance. This represents the core value proposition of Closezly.

## Technical Requirements

### Dependencies (Already Available)
- RAG system from Week 1
- Audio transcription from Week 2
- Existing AI service with Gemini integration
- Desktop app screenshot capabilities

### Environment Variables
Add to `packages/backend-services/.env`:
```env
# AI Pipeline Configuration
CONTEXT_WINDOW_SIZE=4000
MAX_CONTEXT_TOKENS=8000
SUGGESTION_COOLDOWN_MS=5000
CONTEXT_RELEVANCE_THRESHOLD=0.7

# Performance Settings
SCREENSHOT_PROCESSING_TIMEOUT=3000
TRANSCRIPT_BUFFER_DURATION=30000
RAG_SEARCH_TIMEOUT=2000
AI_RESPONSE_TIMEOUT=10000
```

## Step-by-Step Implementation

### Step 1: Create Context Assembly Service

Create `packages/backend-services/src/services/contextAssemblyService.ts`:

```typescript
import VectorSearchService from './vectorSearchService'
import { logger } from '../utils/logger'

interface ContextData {
  screenshot?: {
    imageData: string
    timestamp: number
    extractedText?: string
  }
  transcript?: {
    recentSegments: string[]
    currentSpeaker?: string
    timestamp: number
  }
  ragContext?: {
    relevantChunks: string[]
    sources: string[]
    searchQuery: string
  }
  userQuery?: string
  crmData?: any
}

interface AssembledContext {
  visualContext: string
  conversationContext: string
  knowledgeContext: string
  userIntent: string
  priority: 'high' | 'medium' | 'low'
  contextTokens: number
}

class ContextAssemblyService {
  private maxTokens: number
  private relevanceThreshold: number

  constructor() {
    this.maxTokens = parseInt(process.env.MAX_CONTEXT_TOKENS || '8000')
    this.relevanceThreshold = parseFloat(process.env.CONTEXT_RELEVANCE_THRESHOLD || '0.7')
  }

  /**
   * Assemble context from multiple sources for AI processing
   */
  async assembleContext(data: ContextData, userId: string): Promise<AssembledContext> {
    const startTime = Date.now()
    
    try {
      // Process visual context (screenshot)
      const visualContext = await this.processVisualContext(data.screenshot)
      
      // Process conversation context (transcript)
      const conversationContext = this.processConversationContext(data.transcript)
      
      // Process knowledge context (RAG)
      const knowledgeContext = await this.processKnowledgeContext(
        data.userQuery || conversationContext,
        userId
      )
      
      // Determine user intent and priority
      const userIntent = this.extractUserIntent(data)
      const priority = this.determinePriority(data, conversationContext, knowledgeContext)
      
      // Calculate token usage
      const contextTokens = this.estimateTokens(
        visualContext + conversationContext + knowledgeContext
      )
      
      // Optimize context if it exceeds token limit
      const optimizedContext = this.optimizeContext({
        visualContext,
        conversationContext,
        knowledgeContext,
        userIntent,
        priority,
        contextTokens
      })

      logger.info(`[ContextAssembly] Context assembled in ${Date.now() - startTime}ms`, {
        userId,
        priority,
        contextTokens: optimizedContext.contextTokens,
        hasVisual: !!data.screenshot,
        hasTranscript: !!data.transcript,
        hasRAG: !!knowledgeContext
      })

      return optimizedContext
    } catch (error) {
      logger.error('[ContextAssembly] Error assembling context:', error)
      throw new Error(`Failed to assemble context: ${error.message}`)
    }
  }

  /**
   * Process screenshot data and extract visual context
   */
  private async processVisualContext(screenshot?: ContextData['screenshot']): Promise<string> {
    if (!screenshot) {
      return ''
    }

    try {
      // If we have extracted text from screenshot, use it
      if (screenshot.extractedText) {
        return `[Visual Context - Screen Content]:\n${screenshot.extractedText}\n`
      }

      // Otherwise, indicate we have visual data for multimodal processing
      return `[Visual Context - Screenshot Available]:\nTimestamp: ${new Date(screenshot.timestamp).toISOString()}\n`
    } catch (error) {
      logger.error('[ContextAssembly] Error processing visual context:', error)
      return ''
    }
  }

  /**
   * Process transcript data and create conversation context
   */
  private processConversationContext(transcript?: ContextData['transcript']): string {
    if (!transcript || !transcript.recentSegments.length) {
      return ''
    }

    const segments = transcript.recentSegments
      .filter(segment => segment.trim().length > 0)
      .slice(-10) // Keep last 10 segments

    if (segments.length === 0) {
      return ''
    }

    let context = '[Conversation Context - Recent Discussion]:\n'
    
    if (transcript.currentSpeaker) {
      context += `Current Speaker: ${transcript.currentSpeaker}\n`
    }
    
    context += segments.join('\n') + '\n'
    
    return context
  }

  /**
   * Process knowledge context using RAG
   */
  private async processKnowledgeContext(query: string, userId: string): Promise<string> {
    if (!query || query.trim().length === 0) {
      return ''
    }

    try {
      const ragContext = await VectorSearchService.getContextForPrompt(
        query,
        userId,
        2000 // Max tokens for RAG context
      )

      if (!ragContext) {
        return ''
      }

      return `[Knowledge Base Context - Relevant Information]:\n${ragContext}\n`
    } catch (error) {
      logger.error('[ContextAssembly] Error processing knowledge context:', error)
      return ''
    }
  }

  /**
   * Extract user intent from context data
   */
  private extractUserIntent(data: ContextData): string {
    if (data.userQuery) {
      return `User Query: ${data.userQuery}`
    }

    if (data.transcript?.recentSegments.length) {
      const lastSegment = data.transcript.recentSegments[data.transcript.recentSegments.length - 1]
      
      // Simple intent detection based on keywords
      const lowerSegment = lastSegment.toLowerCase()
      
      if (lowerSegment.includes('objection') || lowerSegment.includes('concern') || lowerSegment.includes('but')) {
        return 'Handling objection or concern'
      }
      
      if (lowerSegment.includes('price') || lowerSegment.includes('cost') || lowerSegment.includes('expensive')) {
        return 'Discussing pricing'
      }
      
      if (lowerSegment.includes('competitor') || lowerSegment.includes('alternative')) {
        return 'Competitive positioning needed'
      }
      
      if (lowerSegment.includes('question') || lowerSegment.includes('?')) {
        return 'Answering prospect question'
      }
    }

    return 'General sales assistance'
  }

  /**
   * Determine priority level for the context
   */
  private determinePriority(
    data: ContextData,
    conversationContext: string,
    knowledgeContext: string
  ): 'high' | 'medium' | 'low' {
    let score = 0

    // High priority indicators
    if (data.userQuery) score += 3
    if (conversationContext.toLowerCase().includes('objection')) score += 3
    if (conversationContext.toLowerCase().includes('price')) score += 2
    if (conversationContext.toLowerCase().includes('competitor')) score += 2
    if (knowledgeContext.length > 0) score += 1
    if (data.screenshot) score += 1

    if (score >= 5) return 'high'
    if (score >= 2) return 'medium'
    return 'low'
  }

  /**
   * Estimate token count for text
   */
  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4)
  }

  /**
   * Optimize context to fit within token limits
   */
  private optimizeContext(context: AssembledContext): AssembledContext {
    if (context.contextTokens <= this.maxTokens) {
      return context
    }

    // Prioritize context based on importance
    let optimized = { ...context }
    let currentTokens = context.contextTokens

    // Trim knowledge context first if needed
    if (currentTokens > this.maxTokens && optimized.knowledgeContext.length > 0) {
      const maxKnowledgeTokens = Math.floor(this.maxTokens * 0.4) // 40% for knowledge
      const targetLength = maxKnowledgeTokens * 4
      
      if (optimized.knowledgeContext.length > targetLength) {
        optimized.knowledgeContext = optimized.knowledgeContext.substring(0, targetLength) + '...'
        currentTokens = this.estimateTokens(
          optimized.visualContext + optimized.conversationContext + optimized.knowledgeContext
        )
      }
    }

    // Trim conversation context if still too long
    if (currentTokens > this.maxTokens && optimized.conversationContext.length > 0) {
      const maxConversationTokens = Math.floor(this.maxTokens * 0.4) // 40% for conversation
      const targetLength = maxConversationTokens * 4
      
      if (optimized.conversationContext.length > targetLength) {
        // Keep the most recent part of the conversation
        const lines = optimized.conversationContext.split('\n')
        let trimmedContext = lines[0] + '\n' // Keep header
        let currentLength = trimmedContext.length
        
        for (let i = lines.length - 1; i >= 1; i--) {
          if (currentLength + lines[i].length + 1 <= targetLength) {
            trimmedContext = lines[0] + '\n' + lines.slice(i).join('\n')
            break
          }
        }
        
        optimized.conversationContext = trimmedContext
        currentTokens = this.estimateTokens(
          optimized.visualContext + optimized.conversationContext + optimized.knowledgeContext
        )
      }
    }

    optimized.contextTokens = currentTokens
    return optimized
  }
}

export default new ContextAssemblyService()
```

### Step 2: Create AI Pipeline Orchestrator

Create `packages/backend-services/src/services/aiPipelineService.ts`:

```typescript
import ContextAssemblyService from './contextAssemblyService'
import { LLMOrchestrationService } from './llmOrchestrationService'
import { SalesPromptTemplates } from '../prompts/salesPromptTemplates'
import { logger } from '../utils/logger'

interface PipelineRequest {
  userId: string
  assistanceType: string
  screenshot?: {
    imageData: string
    timestamp: number
  }
  transcript?: {
    recentSegments: string[]
    currentSpeaker?: string
    timestamp: number
  }
  userQuery?: string
  crmData?: any
}

interface PipelineResponse {
  suggestions: string[]
  confidence: number
  assistanceType: string
  contextUsed: {
    hasVisual: boolean
    hasTranscript: boolean
    hasKnowledge: boolean
  }
  processingTime: number
  rawResponse?: string
}

class AIPipelineService {
  private cooldownMap: Map<string, number> = new Map()
  private cooldownDuration: number

  constructor() {
    this.cooldownDuration = parseInt(process.env.SUGGESTION_COOLDOWN_MS || '5000')
  }

  /**
   * Process complete AI pipeline request
   */
  async processRequest(request: PipelineRequest): Promise<PipelineResponse> {
    const startTime = Date.now()
    const requestId = `${request.userId}-${Date.now()}`

    try {
      // Check cooldown to prevent spam
      if (this.isInCooldown(request.userId)) {
        throw new Error('Request too frequent, please wait before next suggestion')
      }

      logger.info(`[AIPipeline] Processing request ${requestId}`, {
        assistanceType: request.assistanceType,
        hasScreenshot: !!request.screenshot,
        hasTranscript: !!request.transcript,
        hasUserQuery: !!request.userQuery
      })

      // Step 1: Assemble context from all sources
      const assembledContext = await ContextAssemblyService.assembleContext({
        screenshot: request.screenshot,
        transcript: request.transcript,
        userQuery: request.userQuery,
        crmData: request.crmData
      }, request.userId)

      // Step 2: Generate appropriate prompt based on assistance type
      const prompt = this.generatePrompt(request.assistanceType, assembledContext, request.userQuery)

      // Step 3: Determine if multimodal processing is needed
      const useMultimodal = !!request.screenshot?.imageData

      // Step 4: Generate AI response
      let aiResponse
      if (useMultimodal) {
        aiResponse = await LLMOrchestrationService.generateMultimodalContent({
          content: {
            text: prompt,
            imageData: request.screenshot!.imageData,
            imageMimeType: 'image/png'
          },
          temperature: 0.7,
          maxOutputTokens: 500
        })
      } else {
        aiResponse = await LLMOrchestrationService.generateText({
          prompt,
          temperature: 0.7,
          maxOutputTokens: 500
        })
      }

      if (!aiResponse.success || !aiResponse.text) {
        throw new Error('Failed to generate AI response')
      }

      // Step 5: Parse and format suggestions
      const suggestions = this.parseSuggestions(aiResponse.text)
      const confidence = this.calculateConfidence(assembledContext, aiResponse)

      // Step 6: Set cooldown
      this.setCooldown(request.userId)

      const processingTime = Date.now() - startTime

      const response: PipelineResponse = {
        suggestions,
        confidence,
        assistanceType: request.assistanceType,
        contextUsed: {
          hasVisual: !!request.screenshot,
          hasTranscript: !!request.transcript,
          hasKnowledge: assembledContext.knowledgeContext.length > 0
        },
        processingTime,
        rawResponse: aiResponse.text
      }

      logger.info(`[AIPipeline] Request processed successfully`, {
        requestId,
        processingTime,
        suggestionsCount: suggestions.length,
        confidence
      })

      return response
    } catch (error) {
      logger.error(`[AIPipeline] Error processing request ${requestId}:`, error)
      throw new Error(`AI pipeline failed: ${error.message}`)
    }
  }

  /**
   * Generate appropriate prompt based on assistance type and context
   */
  private generatePrompt(assistanceType: string, context: any, userQuery?: string): string {
    const contextData = {
      currentTranscriptSegment: context.conversationContext,
      onScreenText: context.visualContext,
      userQuery: userQuery || context.userIntent
    }

    // Add knowledge context as a prefix if available
    let prompt = ''
    if (context.knowledgeContext) {
      prompt += `${context.knowledgeContext}\n---\n\n`
    }

    // Generate specific prompt based on assistance type
    switch (assistanceType) {
      case 'objection':
        if (!userQuery && !context.conversationContext.includes('objection')) {
          // Auto-detect objection from conversation
          const objectionQuery = this.extractObjectionFromContext(context.conversationContext)
          prompt += SalesPromptTemplates.getObjectionHandlingPrompt(objectionQuery, contextData)
        } else {
          prompt += SalesPromptTemplates.getObjectionHandlingPrompt(userQuery || 'General objection', contextData)
        }
        break

      case 'product_info':
        const productQuery = userQuery || this.extractProductQueryFromContext(context.conversationContext)
        prompt += SalesPromptTemplates.getProductInfoPrompt(productQuery, contextData)
        break

      case 'competitive_positioning':
        const competitor = userQuery || this.extractCompetitorFromContext(context.conversationContext)
        prompt += SalesPromptTemplates.getCompetitivePositioningPrompt(competitor, contextData)
        break

      case 'price_objection':
        prompt += SalesPromptTemplates.getPriceObjectionPrompt(contextData)
        break

      case 'closing':
        prompt += SalesPromptTemplates.getClosingPrompt(contextData)
        break

      case 'discovery':
        prompt += SalesPromptTemplates.getDiscoveryPrompt(contextData)
        break

      default:
        prompt += SalesPromptTemplates.getRealtimeAssistancePrompt(contextData)
        break
    }

    return prompt
  }

  /**
   * Parse AI response into structured suggestions
   */
  private parseSuggestions(response: string): string[] {
    // Split response into suggestions
    const suggestions = response
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .filter(line => !line.startsWith('#') && !line.startsWith('---'))
      .map(line => line.replace(/^\d+\.\s*/, '').replace(/^[-*]\s*/, ''))
      .filter(line => line.length > 10) // Filter out very short suggestions

    // Limit to top 5 suggestions
    return suggestions.slice(0, 5)
  }

  /**
   * Calculate confidence score based on context quality and AI response
   */
  private calculateConfidence(context: any, aiResponse: any): number {
    let confidence = 0.5 // Base confidence

    // Boost confidence based on available context
    if (context.knowledgeContext.length > 0) confidence += 0.2
    if (context.conversationContext.length > 0) confidence += 0.15
    if (context.visualContext.length > 0) confidence += 0.1

    // Boost confidence based on AI response quality
    if (aiResponse.usage?.totalTokens > 100) confidence += 0.05

    // Ensure confidence is between 0 and 1
    return Math.min(Math.max(confidence, 0), 1)
  }

  /**
   * Extract objection from conversation context
   */
  private extractObjectionFromContext(conversationContext: string): string {
    const objectionKeywords = ['but', 'however', 'concern', 'worried', 'expensive', 'too much', 'not sure']
    const lines = conversationContext.split('\n')
    
    for (const line of lines.reverse()) {
      const lowerLine = line.toLowerCase()
      if (objectionKeywords.some(keyword => lowerLine.includes(keyword))) {
        return line.trim()
      }
    }
    
    return 'General objection or concern'
  }

  /**
   * Extract product query from conversation context
   */
  private extractProductQueryFromContext(conversationContext: string): string {
    const questionKeywords = ['how', 'what', 'why', 'when', 'where', 'can you', 'does it', 'will it']
    const lines = conversationContext.split('\n')
    
    for (const line of lines.reverse()) {
      const lowerLine = line.toLowerCase()
      if (questionKeywords.some(keyword => lowerLine.includes(keyword))) {
        return line.trim()
      }
    }
    
    return 'Product information request'
  }

  /**
   * Extract competitor from conversation context
   */
  private extractCompetitorFromContext(conversationContext: string): string {
    const competitorKeywords = ['competitor', 'alternative', 'other option', 'compared to', 'versus']
    const lines = conversationContext.split('\n')
    
    for (const line of lines.reverse()) {
      const lowerLine = line.toLowerCase()
      if (competitorKeywords.some(keyword => lowerLine.includes(keyword))) {
        // Try to extract competitor name
        const words = line.split(' ')
        const keywordIndex = words.findIndex(word => 
          competitorKeywords.some(keyword => word.toLowerCase().includes(keyword))
        )
        
        if (keywordIndex >= 0 && keywordIndex < words.length - 1) {
          return words.slice(keywordIndex + 1).join(' ').trim()
        }
        
        return line.trim()
      }
    }
    
    return 'Competitor comparison'
  }

  /**
   * Check if user is in cooldown period
   */
  private isInCooldown(userId: string): boolean {
    const lastRequest = this.cooldownMap.get(userId)
    if (!lastRequest) return false
    
    return Date.now() - lastRequest < this.cooldownDuration
  }

  /**
   * Set cooldown for user
   */
  private setCooldown(userId: string): void {
    this.cooldownMap.set(userId, Date.now())
  }
}

export default new AIPipelineService()
```

### Step 3: Create Pipeline API Endpoint

Create `packages/backend-services/src/api/v1/pipeline.ts`:

```typescript
import express, { Request, Response } from 'express'
import { authMiddleware } from '../../authMiddleware'
import AIPipelineService from '../../services/aiPipelineService'
import { asyncHandler } from '../../utils/errorHandler'
import { logger } from '../../utils/logger'

const router = express.Router()

/**
 * POST /api/v1/pipeline/process
 * Process complete AI pipeline request with context assembly
 */
router.post('/process', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id
  const {
    assistanceType = 'general_assistance',
    screenshot,
    transcript,
    userQuery,
    crmData
  } = req.body

  // Validate required fields
  if (!assistanceType) {
    return res.status(400).json({
      success: false,
      error: 'assistanceType is required'
    })
  }

  // Validate that we have at least some context
  if (!screenshot && !transcript && !userQuery) {
    return res.status(400).json({
      success: false,
      error: 'At least one context source (screenshot, transcript, or userQuery) is required'
    })
  }

  try {
    const pipelineRequest = {
      userId,
      assistanceType,
      screenshot,
      transcript,
      userQuery,
      crmData
    }

    const result = await AIPipelineService.processRequest(pipelineRequest)

    res.status(200).json({
      success: true,
      ...result
    })
  } catch (error) {
    logger.error('[Pipeline] Error processing request:', error)
    
    // Handle specific error types
    if (error.message.includes('too frequent')) {
      return res.status(429).json({
        success: false,
        error: error.message,
        code: 'RATE_LIMITED'
      })
    }

    res.status(500).json({
      success: false,
      error: error.message,
      code: 'PIPELINE_ERROR'
    })
  }
}))

/**
 * POST /api/v1/pipeline/quick-assist
 * Quick assistance endpoint for simple queries
 */
router.post('/quick-assist', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id
  const { query, assistanceType = 'general_assistance' } = req.body

  if (!query) {
    return res.status(400).json({
      success: false,
      error: 'Query is required'
    })
  }

  try {
    const result = await AIPipelineService.processRequest({
      userId,
      assistanceType,
      userQuery: query
    })

    res.status(200).json({
      success: true,
      ...result
    })
  } catch (error) {
    logger.error('[Pipeline] Error processing quick assist:', error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
}))

export default router
```

### Step 4: Update Desktop App Integration

Update `apps/desktop/electron/helpers/AIInteractionService.ts`:

```typescript
/**
 * Process complete multimodal assistance with full pipeline
 */
public async processFullPipeline(
  assistanceType: string = 'general_assistance',
  userQuery?: string
): Promise<AssistanceResponse> {
  if (this.isProcessing) {
    return {
      success: false,
      error: 'Another AI request is already in progress'
    }
  }

  this.isProcessing = true
  AppState.setProcessing(true)

  try {
    // Capture screenshot
    const screenshot = await ScreenshotHelper.captureActiveWindow()
    
    // Get recent transcript
    const transcript = AppState.getRecentTranscript()
    
    // Get CRM context if available
    const crmData = AppState.getCRMContext()

    // Prepare request payload
    const requestPayload = {
      assistanceType,
      screenshot: screenshot ? {
        imageData: screenshot,
        timestamp: Date.now()
      } : undefined,
      transcript: transcript ? {
        recentSegments: transcript.segments || [],
        currentSpeaker: transcript.currentSpeaker,
        timestamp: Date.now()
      } : undefined,
      userQuery,
      crmData
    }

    // Make API request to pipeline endpoint
    const response = await this.makeAuthenticatedRequest('/api/v1/pipeline/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestPayload)
    })

    if (!response.success) {
      throw new Error(response.error || 'Pipeline processing failed')
    }

    // Update app state with suggestions
    AppState.setSuggestions(response.suggestions)
    AppState.setLastAssistanceType(assistanceType)

    return {
      success: true,
      suggestions: response.suggestions,
      confidence: response.confidence,
      processingTime: response.processingTime,
      contextUsed: response.contextUsed
    }
  } catch (error) {
    console.error('[AIInteraction] Error in full pipeline processing:', error)
    return {
      success: false,
      error: error.message
    }
  } finally {
    this.isProcessing = false
    AppState.setProcessing(false)
  }
}
```

## Testing Strategy

### Unit Tests
```typescript
// Test context assembly
describe('ContextAssemblyService', () => {
  test('should assemble context from multiple sources', async () => {
    const context = await ContextAssemblyService.assembleContext({
      screenshot: { imageData: 'base64...', timestamp: Date.now() },
      transcript: { recentSegments: ['Hello', 'How are you?'], timestamp: Date.now() },
      userQuery: 'Help with objection'
    }, 'user-123')

    expect(context.visualContext).toContain('Visual Context')
    expect(context.conversationContext).toContain('Hello')
    expect(context.userIntent).toContain('objection')
  })
})
```

### Integration Tests
```bash
# Test complete pipeline
curl -X POST http://localhost:4000/api/v1/pipeline/process \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "assistanceType": "objection",
    "userQuery": "They said our price is too high",
    "transcript": {
      "recentSegments": ["I think your solution is too expensive"],
      "timestamp": 1640995200000
    }
  }'
```

## Integration Points

### With RAG System
- Automatic knowledge retrieval based on conversation context
- Document-based suggestions for specific queries
- Context-aware search optimization

### With Audio Transcription
- Real-time transcript integration
- Speaker-aware context assembly
- Conversation flow analysis

### With Desktop App
- Screenshot-based visual context
- Real-time suggestion delivery
- User interaction tracking

## Success Criteria

### Functional Requirements
- [ ] Complete pipeline processes requests end-to-end
- [ ] Context assembly from multiple sources works correctly
- [ ] AI suggestions are relevant and actionable
- [ ] Real-time performance meets user expectations
- [ ] Error handling covers all failure scenarios

### Performance Requirements
- [ ] Total pipeline processing time <5 seconds
- [ ] Context assembly completes within 2 seconds
- [ ] AI response generation within 3 seconds
- [ ] Memory usage remains stable during operation

### Quality Requirements
- [ ] Suggestion relevance score >80%
- [ ] Context accuracy maintained across sources
- [ ] Graceful degradation when sources unavailable
- [ ] Consistent user experience across scenarios

## Next Steps

1. **Week 3 Day 1**: Implement context assembly service
2. **Week 3 Day 2**: Create AI pipeline orchestrator
3. **Week 3 Day 3**: Build pipeline API endpoints
4. **Week 3 Day 4**: Update desktop app integration
5. **Week 3 Day 5**: End-to-end testing and optimization

After completion, proceed to `crm-integration-implementation.md` for CRM connectivity implementation.
