# CRM Integration Implementation Guide

**Priority:** Medium (Week 4-5)
**Estimated Time:** 7 days
**Dependencies:** Backend services, web portal authentication

## Feature Overview

Implement OAuth-based integrations with Salesforce and HubSpot CRMs to provide contextual customer data during sales calls, enable automatic call logging, and enhance AI suggestions with prospect information.

## Technical Requirements

### Dependencies to Add
```bash
# Backend services dependencies
cd packages/backend-services
npm install jsforce @hubspot/api-client passport passport-oauth2 express-session

# Web portal dependencies (if needed)
cd apps/web-portal
npm install @salesforce/core
```

### Environment Variables
Add to `packages/backend-services/.env`:
```env
# Salesforce OAuth Configuration
SALESFORCE_CLIENT_ID=your_salesforce_consumer_key
SALESFORCE_CLIENT_SECRET=your_salesforce_consumer_secret
SALESFORCE_REDIRECT_URI=http://localhost:4000/api/v1/crm/callback/salesforce
SALESFORCE_SANDBOX=true  # Set to false for production

# HubSpot OAuth Configuration
HUBSPOT_CLIENT_ID=your_hubspot_client_id
HUBSPOT_CLIENT_SECRET=your_hubspot_client_secret
HUBSPOT_REDIRECT_URI=http://localhost:4000/api/v1/crm/callback/hubspot

# CRM Configuration
CRM_TOKEN_ENCRYPTION_KEY=your_32_character_encryption_key_here
CRM_SYNC_INTERVAL_HOURS=24
CRM_RATE_LIMIT_REQUESTS=100
CRM_RATE_LIMIT_WINDOW_MS=60000
```

### Database Schema Updates
Add to Supabase migration:
```sql
-- Create CRM connections table
CREATE TABLE IF NOT EXISTS public.crm_connections (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    crm_type TEXT NOT NULL CHECK (crm_type IN ('salesforce', 'hubspot')),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    instance_url TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'expired', 'revoked')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, crm_type)
);

-- Create CRM contacts cache table
CREATE TABLE IF NOT EXISTS public.crm_contacts (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    connection_id UUID REFERENCES public.crm_connections(id) ON DELETE CASCADE,
    crm_contact_id TEXT NOT NULL,
    email TEXT,
    name TEXT,
    company TEXT,
    phone TEXT,
    contact_data JSONB DEFAULT '{}'::JSONB,
    last_synced TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(connection_id, crm_contact_id)
);

-- Enable RLS
ALTER TABLE public.crm_connections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crm_contacts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can manage their own CRM connections" ON public.crm_connections
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own CRM contacts" ON public.crm_contacts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.crm_connections
            WHERE crm_connections.id = crm_contacts.connection_id
            AND crm_connections.user_id = auth.uid()
        )
    );
```

## Step-by-Step Implementation

### Step 1: Create CRM Service Interface

Create `packages/backend-services/src/services/crmService.ts`:

```typescript
export interface CRMContact {
  id: string
  email?: string
  name?: string
  company?: string
  phone?: string
  title?: string
  lastActivity?: Date
  customFields?: Record<string, any>
}

export interface CRMOpportunity {
  id: string
  name: string
  stage: string
  amount?: number
  closeDate?: Date
  probability?: number
  contactId?: string
  accountId?: string
}

export interface CRMActivity {
  id: string
  type: 'call' | 'email' | 'meeting' | 'task'
  subject: string
  description?: string
  date: Date
  contactId?: string
  opportunityId?: string
}

export interface CRMConnection {
  id: string
  userId: string
  crmType: 'salesforce' | 'hubspot'
  accessToken: string
  refreshToken?: string
  instanceUrl?: string
  expiresAt?: Date
  status: 'active' | 'expired' | 'revoked'
}

export abstract class CRMService {
  protected connection: CRMConnection

  constructor(connection: CRMConnection) {
    this.connection = connection
  }

  // Authentication methods
  abstract refreshAccessToken(): Promise<void>
  abstract validateConnection(): Promise<boolean>

  // Contact methods
  abstract searchContacts(query: string): Promise<CRMContact[]>
  abstract getContact(contactId: string): Promise<CRMContact | null>
  abstract getContactByEmail(email: string): Promise<CRMContact | null>

  // Opportunity methods
  abstract getOpportunities(contactId?: string): Promise<CRMOpportunity[]>
  abstract getOpportunity(opportunityId: string): Promise<CRMOpportunity | null>

  // Activity methods
  abstract createActivity(activity: Omit<CRMActivity, 'id'>): Promise<string>
  abstract getRecentActivities(contactId?: string, limit?: number): Promise<CRMActivity[]>

  // Utility methods
  abstract getInstanceInfo(): Promise<{ name: string; url: string }>
}
```

### Step 2: Implement Salesforce Service

Create `packages/backend-services/src/services/salesforceService.ts`:

```typescript
import jsforce from 'jsforce'
import { CRMService, CRMContact, CRMOpportunity, CRMActivity, CRMConnection } from './crmService'
import { logger } from '../utils/logger'

class SalesforceService extends CRMService {
  private conn: jsforce.Connection

  constructor(connection: CRMConnection) {
    super(connection)
    this.conn = new jsforce.Connection({
      instanceUrl: connection.instanceUrl,
      accessToken: connection.accessToken,
      version: '58.0'
    })
  }

  async refreshAccessToken(): Promise<void> {
    if (!this.connection.refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const oauth2 = new jsforce.OAuth2({
        clientId: process.env.SALESFORCE_CLIENT_ID!,
        clientSecret: process.env.SALESFORCE_CLIENT_SECRET!,
        redirectUri: process.env.SALESFORCE_REDIRECT_URI!
      })

      const result = await oauth2.refreshToken(this.connection.refreshToken)

      this.connection.accessToken = result.access_token
      this.connection.instanceUrl = result.instance_url

      // Update connection in database
      await this.updateConnectionInDB()

      // Update jsforce connection
      this.conn = new jsforce.Connection({
        instanceUrl: this.connection.instanceUrl,
        accessToken: this.connection.accessToken,
        version: '58.0'
      })

      logger.info('[Salesforce] Access token refreshed successfully')
    } catch (error) {
      logger.error('[Salesforce] Error refreshing access token:', error)
      throw new Error('Failed to refresh Salesforce access token')
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.conn.identity()
      return true
    } catch (error) {
      logger.error('[Salesforce] Connection validation failed:', error)

      // Try to refresh token if validation fails
      if (this.connection.refreshToken) {
        try {
          await this.refreshAccessToken()
          await this.conn.identity()
          return true
        } catch (refreshError) {
          logger.error('[Salesforce] Token refresh failed:', refreshError)
          return false
        }
      }

      return false
    }
  }

  async searchContacts(query: string): Promise<CRMContact[]> {
    try {
      const soqlQuery = `
        SELECT Id, Name, Email, Phone, Title, Account.Name, LastActivityDate
        FROM Contact
        WHERE Name LIKE '%${query}%' OR Email LIKE '%${query}%' OR Account.Name LIKE '%${query}%'
        LIMIT 20
      `

      const result = await this.conn.query(soqlQuery)

      return result.records.map(record => this.mapSalesforceContact(record))
    } catch (error) {
      logger.error('[Salesforce] Error searching contacts:', error)
      throw new Error('Failed to search Salesforce contacts')
    }
  }

  async getContact(contactId: string): Promise<CRMContact | null> {
    try {
      const result = await this.conn.sobject('Contact').retrieve(contactId, [
        'Id', 'Name', 'Email', 'Phone', 'Title', 'Account.Name', 'LastActivityDate'
      ])

      return this.mapSalesforceContact(result)
    } catch (error) {
      logger.error('[Salesforce] Error getting contact:', error)
      return null
    }
  }

  async getContactByEmail(email: string): Promise<CRMContact | null> {
    try {
      const result = await this.conn.query(`
        SELECT Id, Name, Email, Phone, Title, Account.Name, LastActivityDate
        FROM Contact
        WHERE Email = '${email}'
        LIMIT 1
      `)

      if (result.records.length === 0) {
        return null
      }

      return this.mapSalesforceContact(result.records[0])
    } catch (error) {
      logger.error('[Salesforce] Error getting contact by email:', error)
      return null
    }
  }

  async getOpportunities(contactId?: string): Promise<CRMOpportunity[]> {
    try {
      let soqlQuery = `
        SELECT Id, Name, StageName, Amount, CloseDate, Probability, AccountId
        FROM Opportunity
      `

      if (contactId) {
        // Get opportunities related to the contact's account
        const contact = await this.getContact(contactId)
        if (contact?.company) {
          soqlQuery += ` WHERE Account.Name = '${contact.company}'`
        }
      }

      soqlQuery += ' ORDER BY CloseDate DESC LIMIT 10'

      const result = await this.conn.query(soqlQuery)

      return result.records.map(record => this.mapSalesforceOpportunity(record))
    } catch (error) {
      logger.error('[Salesforce] Error getting opportunities:', error)
      throw new Error('Failed to get Salesforce opportunities')
    }
  }

  async getOpportunity(opportunityId: string): Promise<CRMOpportunity | null> {
    try {
      const result = await this.conn.sobject('Opportunity').retrieve(opportunityId, [
        'Id', 'Name', 'StageName', 'Amount', 'CloseDate', 'Probability', 'AccountId'
      ])

      return this.mapSalesforceOpportunity(result)
    } catch (error) {
      logger.error('[Salesforce] Error getting opportunity:', error)
      return null
    }
  }

  async createActivity(activity: Omit<CRMActivity, 'id'>): Promise<string> {
    try {
      const taskData: any = {
        Subject: activity.subject,
        Description: activity.description,
        ActivityDate: activity.date.toISOString().split('T')[0],
        Status: 'Completed',
        Type: this.mapActivityType(activity.type)
      }

      if (activity.contactId) {
        taskData.WhoId = activity.contactId
      }

      const result = await this.conn.sobject('Task').create(taskData)

      if (result.success) {
        logger.info(`[Salesforce] Activity created: ${result.id}`)
        return result.id
      } else {
        throw new Error('Failed to create activity')
      }
    } catch (error) {
      logger.error('[Salesforce] Error creating activity:', error)
      throw new Error('Failed to create Salesforce activity')
    }
  }

  async getRecentActivities(contactId?: string, limit: number = 10): Promise<CRMActivity[]> {
    try {
      let soqlQuery = `
        SELECT Id, Subject, Description, ActivityDate, Type, WhoId
        FROM Task
        WHERE ActivityDate != null
      `

      if (contactId) {
        soqlQuery += ` AND WhoId = '${contactId}'`
      }

      soqlQuery += ` ORDER BY ActivityDate DESC LIMIT ${limit}`

      const result = await this.conn.query(soqlQuery)

      return result.records.map(record => this.mapSalesforceActivity(record))
    } catch (error) {
      logger.error('[Salesforce] Error getting recent activities:', error)
      throw new Error('Failed to get Salesforce activities')
    }
  }

  async getInstanceInfo(): Promise<{ name: string; url: string }> {
    try {
      const identity = await this.conn.identity()
      return {
        name: identity.organization_id,
        url: this.connection.instanceUrl || ''
      }
    } catch (error) {
      logger.error('[Salesforce] Error getting instance info:', error)
      throw new Error('Failed to get Salesforce instance info')
    }
  }

  // Helper methods
  private mapSalesforceContact(record: any): CRMContact {
    return {
      id: record.Id,
      name: record.Name,
      email: record.Email,
      phone: record.Phone,
      title: record.Title,
      company: record.Account?.Name,
      lastActivity: record.LastActivityDate ? new Date(record.LastActivityDate) : undefined,
      customFields: {
        accountId: record.AccountId
      }
    }
  }

  private mapSalesforceOpportunity(record: any): CRMOpportunity {
    return {
      id: record.Id,
      name: record.Name,
      stage: record.StageName,
      amount: record.Amount,
      closeDate: record.CloseDate ? new Date(record.CloseDate) : undefined,
      probability: record.Probability,
      accountId: record.AccountId
    }
  }

  private mapSalesforceActivity(record: any): CRMActivity {
    return {
      id: record.Id,
      type: this.mapSalesforceActivityType(record.Type),
      subject: record.Subject,
      description: record.Description,
      date: new Date(record.ActivityDate),
      contactId: record.WhoId
    }
  }

  private mapActivityType(type: CRMActivity['type']): string {
    const typeMap = {
      'call': 'Call',
      'email': 'Email',
      'meeting': 'Meeting',
      'task': 'Other'
    }
    return typeMap[type] || 'Other'
  }

  private mapSalesforceActivityType(type: string): CRMActivity['type'] {
    const typeMap: Record<string, CRMActivity['type']> = {
      'Call': 'call',
      'Email': 'email',
      'Meeting': 'meeting'
    }
    return typeMap[type] || 'task'
  }

  private async updateConnectionInDB(): Promise<void> {
    // Implementation to update connection in database
    // This would use Supabase to update the crm_connections table
    const { supabase } = require('../supabaseClient')

    await supabase
      .from('crm_connections')
      .update({
        access_token: this.connection.accessToken,
        instance_url: this.connection.instanceUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', this.connection.id)
  }
}

export default SalesforceService
```

### Step 3: Implement HubSpot Service

Create `packages/backend-services/src/services/hubspotService.ts`:

```typescript
import { Client } from '@hubspot/api-client'
import { CRMService, CRMContact, CRMOpportunity, CRMActivity, CRMConnection } from './crmService'
import { logger } from '../utils/logger'

class HubSpotService extends CRMService {
  private client: Client

  constructor(connection: CRMConnection) {
    super(connection)
    this.client = new Client({ accessToken: connection.accessToken })
  }

  async refreshAccessToken(): Promise<void> {
    if (!this.connection.refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const refreshTokenRequest = {
        grant_type: 'refresh_token',
        client_id: process.env.HUBSPOT_CLIENT_ID!,
        client_secret: process.env.HUBSPOT_CLIENT_SECRET!,
        refresh_token: this.connection.refreshToken
      }

      const response = await fetch('https://api.hubapi.com/oauth/v1/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams(refreshTokenRequest)
      })

      if (!response.ok) {
        throw new Error('Failed to refresh token')
      }

      const data = await response.json()

      this.connection.accessToken = data.access_token
      this.connection.refreshToken = data.refresh_token
      this.connection.expiresAt = new Date(Date.now() + data.expires_in * 1000)

      // Update connection in database
      await this.updateConnectionInDB()

      // Update HubSpot client
      this.client = new Client({ accessToken: this.connection.accessToken })

      logger.info('[HubSpot] Access token refreshed successfully')
    } catch (error) {
      logger.error('[HubSpot] Error refreshing access token:', error)
      throw new Error('Failed to refresh HubSpot access token')
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.client.oauth.accessTokensApi.get(this.connection.accessToken)
      return true
    } catch (error) {
      logger.error('[HubSpot] Connection validation failed:', error)

      // Try to refresh token if validation fails
      if (this.connection.refreshToken) {
        try {
          await this.refreshAccessToken()
          await this.client.oauth.accessTokensApi.get(this.connection.accessToken)
          return true
        } catch (refreshError) {
          logger.error('[HubSpot] Token refresh failed:', refreshError)
          return false
        }
      }

      return false
    }
  }

  async searchContacts(query: string): Promise<CRMContact[]> {
    try {
      const searchRequest = {
        query,
        limit: 20,
        properties: ['firstname', 'lastname', 'email', 'phone', 'company', 'jobtitle', 'lastmodifieddate']
      }

      const response = await this.client.crm.contacts.searchApi.doSearch(searchRequest)

      return response.results.map(contact => this.mapHubSpotContact(contact))
    } catch (error) {
      logger.error('[HubSpot] Error searching contacts:', error)
      throw new Error('Failed to search HubSpot contacts')
    }
  }

  async getContact(contactId: string): Promise<CRMContact | null> {
    try {
      const response = await this.client.crm.contacts.basicApi.getById(
        contactId,
        ['firstname', 'lastname', 'email', 'phone', 'company', 'jobtitle', 'lastmodifieddate']
      )

      return this.mapHubSpotContact(response)
    } catch (error) {
      logger.error('[HubSpot] Error getting contact:', error)
      return null
    }
  }

  async getContactByEmail(email: string): Promise<CRMContact | null> {
    try {
      const searchRequest = {
        filterGroups: [{
          filters: [{
            propertyName: 'email',
            operator: 'EQ',
            value: email
          }]
        }],
        properties: ['firstname', 'lastname', 'email', 'phone', 'company', 'jobtitle', 'lastmodifieddate'],
        limit: 1
      }

      const response = await this.client.crm.contacts.searchApi.doSearch(searchRequest)

      if (response.results.length === 0) {
        return null
      }

      return this.mapHubSpotContact(response.results[0])
    } catch (error) {
      logger.error('[HubSpot] Error getting contact by email:', error)
      return null
    }
  }

  async getOpportunities(contactId?: string): Promise<CRMOpportunity[]> {
    try {
      let searchRequest: any = {
        properties: ['dealname', 'dealstage', 'amount', 'closedate', 'hs_probability'],
        limit: 10
      }

      if (contactId) {
        searchRequest.filterGroups = [{
          filters: [{
            propertyName: 'associations.contact',
            operator: 'EQ',
            value: contactId
          }]
        }]
      }

      const response = await this.client.crm.deals.searchApi.doSearch(searchRequest)

      return response.results.map(deal => this.mapHubSpotOpportunity(deal))
    } catch (error) {
      logger.error('[HubSpot] Error getting opportunities:', error)
      throw new Error('Failed to get HubSpot opportunities')
    }
  }

  async getOpportunity(opportunityId: string): Promise<CRMOpportunity | null> {
    try {
      const response = await this.client.crm.deals.basicApi.getById(
        opportunityId,
        ['dealname', 'dealstage', 'amount', 'closedate', 'hs_probability']
      )

      return this.mapHubSpotOpportunity(response)
    } catch (error) {
      logger.error('[HubSpot] Error getting opportunity:', error)
      return null
    }
  }

  async createActivity(activity: Omit<CRMActivity, 'id'>): Promise<string> {
    try {
      const engagementData = {
        properties: {
          hs_engagement_type: this.mapActivityType(activity.type),
          hs_engagement_subject: activity.subject,
          hs_engagement_body: activity.description,
          hs_timestamp: activity.date.getTime().toString()
        }
      }

      const response = await this.client.crm.objects.calls.basicApi.create(engagementData)

      logger.info(`[HubSpot] Activity created: ${response.id}`)
      return response.id
    } catch (error) {
      logger.error('[HubSpot] Error creating activity:', error)
      throw new Error('Failed to create HubSpot activity')
    }
  }

  async getRecentActivities(contactId?: string, limit: number = 10): Promise<CRMActivity[]> {
    try {
      let searchRequest: any = {
        properties: ['hs_engagement_type', 'hs_engagement_subject', 'hs_engagement_body', 'hs_timestamp'],
        limit
      }

      if (contactId) {
        searchRequest.filterGroups = [{
          filters: [{
            propertyName: 'associations.contact',
            operator: 'EQ',
            value: contactId
          }]
        }]
      }

      const response = await this.client.crm.objects.calls.searchApi.doSearch(searchRequest)

      return response.results.map(engagement => this.mapHubSpotActivity(engagement))
    } catch (error) {
      logger.error('[HubSpot] Error getting recent activities:', error)
      throw new Error('Failed to get HubSpot activities')
    }
  }

  async getInstanceInfo(): Promise<{ name: string; url: string }> {
    try {
      const response = await this.client.oauth.accessTokensApi.get(this.connection.accessToken)
      return {
        name: response.hubDomain || 'HubSpot',
        url: `https://${response.hubDomain || 'app.hubspot.com'}`
      }
    } catch (error) {
      logger.error('[HubSpot] Error getting instance info:', error)
      throw new Error('Failed to get HubSpot instance info')
    }
  }

  // Helper methods
  private mapHubSpotContact(contact: any): CRMContact {
    const props = contact.properties
    return {
      id: contact.id,
      name: `${props.firstname || ''} ${props.lastname || ''}`.trim(),
      email: props.email,
      phone: props.phone,
      title: props.jobtitle,
      company: props.company,
      lastActivity: props.lastmodifieddate ? new Date(props.lastmodifieddate) : undefined
    }
  }

  private mapHubSpotOpportunity(deal: any): CRMOpportunity {
    const props = deal.properties
    return {
      id: deal.id,
      name: props.dealname,
      stage: props.dealstage,
      amount: props.amount ? parseFloat(props.amount) : undefined,
      closeDate: props.closedate ? new Date(props.closedate) : undefined,
      probability: props.hs_probability ? parseFloat(props.hs_probability) : undefined
    }
  }

  private mapHubSpotActivity(engagement: any): CRMActivity {
    const props = engagement.properties
    return {
      id: engagement.id,
      type: this.mapHubSpotActivityType(props.hs_engagement_type),
      subject: props.hs_engagement_subject,
      description: props.hs_engagement_body,
      date: new Date(parseInt(props.hs_timestamp))
    }
  }

  private mapActivityType(type: CRMActivity['type']): string {
    const typeMap = {
      'call': 'CALL',
      'email': 'EMAIL',
      'meeting': 'MEETING',
      'task': 'TASK'
    }
    return typeMap[type] || 'TASK'
  }

  private mapHubSpotActivityType(type: string): CRMActivity['type'] {
    const typeMap: Record<string, CRMActivity['type']> = {
      'CALL': 'call',
      'EMAIL': 'email',
      'MEETING': 'meeting',
      'TASK': 'task'
    }
    return typeMap[type] || 'task'
  }

  private async updateConnectionInDB(): Promise<void> {
    // Implementation to update connection in database
    const { supabase } = require('../supabaseClient')

    await supabase
      .from('crm_connections')
      .update({
        access_token: this.connection.accessToken,
        refresh_token: this.connection.refreshToken,
        expires_at: this.connection.expiresAt?.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', this.connection.id)
  }
}

export default HubSpotService

### Step 4: Create CRM OAuth API Endpoints

Create `packages/backend-services/src/api/v1/crm.ts`:

```typescript
import express, { Request, Response } from 'express'
import { authMiddleware } from '../../authMiddleware'
import { asyncHandler } from '../../utils/errorHandler'
import { logger } from '../../utils/logger'
import { supabase } from '../../supabaseClient'
import SalesforceService from '../../services/salesforceService'
import HubSpotService from '../../services/hubspotService'
import crypto from 'crypto'

const router = express.Router()

/**
 * GET /api/v1/crm/connect/:provider
 * Initiate OAuth flow for CRM provider
 */
router.get('/connect/:provider', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { provider } = req.params
  const userId = (req as any).user?.id

  if (!['salesforce', 'hubspot'].includes(provider)) {
    return res.status(400).json({
      success: false,
      error: 'Unsupported CRM provider'
    })
  }

  try {
    // Generate state parameter for security
    const state = crypto.randomBytes(32).toString('hex')

    // Store state in session or database for validation
    // For simplicity, we'll use a temporary storage (in production, use Redis or database)

    let authUrl: string

    if (provider === 'salesforce') {
      const baseUrl = process.env.SALESFORCE_SANDBOX === 'true'
        ? 'https://test.salesforce.com'
        : 'https://login.salesforce.com'

      authUrl = `${baseUrl}/services/oauth2/authorize?` +
        `response_type=code&` +
        `client_id=${process.env.SALESFORCE_CLIENT_ID}&` +
        `redirect_uri=${encodeURIComponent(process.env.SALESFORCE_REDIRECT_URI!)}&` +
        `state=${state}&` +
        `scope=api refresh_token`
    } else {
      authUrl = `https://app.hubspot.com/oauth/authorize?` +
        `client_id=${process.env.HUBSPOT_CLIENT_ID}&` +
        `redirect_uri=${encodeURIComponent(process.env.HUBSPOT_REDIRECT_URI!)}&` +
        `scope=crm.objects.contacts.read crm.objects.deals.read crm.objects.companies.read&` +
        `state=${state}`
    }

    res.status(200).json({
      success: true,
      authUrl,
      state
    })
  } catch (error) {
    logger.error(`[CRM] Error initiating ${provider} OAuth:`, error)
    res.status(500).json({
      success: false,
      error: 'Failed to initiate OAuth flow'
    })
  }
}))

/**
 * GET /api/v1/crm/callback/:provider
 * Handle OAuth callback from CRM provider
 */
router.get('/callback/:provider', asyncHandler(async (req: Request, res: Response) => {
  const { provider } = req.params
  const { code, state, error } = req.query

  if (error) {
    logger.error(`[CRM] OAuth error for ${provider}:`, error)
    return res.redirect(`${process.env.WEB_PORTAL_URL}/dashboard/crm?error=oauth_denied`)
  }

  if (!code) {
    return res.redirect(`${process.env.WEB_PORTAL_URL}/dashboard/crm?error=no_code`)
  }

  try {
    let tokenData: any

    if (provider === 'salesforce') {
      tokenData = await exchangeSalesforceToken(code as string)
    } else if (provider === 'hubspot') {
      tokenData = await exchangeHubSpotToken(code as string)
    } else {
      return res.redirect(`${process.env.WEB_PORTAL_URL}/dashboard/crm?error=invalid_provider`)
    }

    // Store connection in database
    // Note: In a real implementation, you'd need to get the userId from the state parameter
    // For now, this is a simplified version
    const { data: connection, error: dbError } = await supabase
      .from('crm_connections')
      .upsert({
        user_id: 'user-id-from-state', // Get from state parameter
        crm_type: provider,
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        instance_url: tokenData.instance_url,
        expires_at: tokenData.expires_at ? new Date(tokenData.expires_at) : null,
        status: 'active'
      })
      .select()
      .single()

    if (dbError) throw dbError

    logger.info(`[CRM] ${provider} connection established for user`)
    res.redirect(`${process.env.WEB_PORTAL_URL}/dashboard/crm?success=connected`)
  } catch (error) {
    logger.error(`[CRM] Error handling ${provider} callback:`, error)
    res.redirect(`${process.env.WEB_PORTAL_URL}/dashboard/crm?error=connection_failed`)
  }
}))

/**
 * GET /api/v1/crm/connections
 * Get user's CRM connections
 */
router.get('/connections', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id

  try {
    const { data: connections, error } = await supabase
      .from('crm_connections')
      .select('id, crm_type, status, created_at, updated_at')
      .eq('user_id', userId)

    if (error) throw error

    res.status(200).json({
      success: true,
      connections
    })
  } catch (error) {
    logger.error('[CRM] Error fetching connections:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch CRM connections'
    })
  }
}))

/**
 * DELETE /api/v1/crm/connections/:id
 * Disconnect CRM connection
 */
router.delete('/connections/:id', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params
  const userId = (req as any).user?.id

  try {
    const { error } = await supabase
      .from('crm_connections')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) throw error

    res.status(200).json({
      success: true,
      message: 'CRM connection removed successfully'
    })
  } catch (error) {
    logger.error('[CRM] Error removing connection:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to remove CRM connection'
    })
  }
}))

// Helper functions for token exchange
async function exchangeSalesforceToken(code: string): Promise<any> {
  const tokenUrl = process.env.SALESFORCE_SANDBOX === 'true'
    ? 'https://test.salesforce.com/services/oauth2/token'
    : 'https://login.salesforce.com/services/oauth2/token'

  const response = await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: process.env.SALESFORCE_CLIENT_ID!,
      client_secret: process.env.SALESFORCE_CLIENT_SECRET!,
      redirect_uri: process.env.SALESFORCE_REDIRECT_URI!,
      code
    })
  })

  if (!response.ok) {
    throw new Error('Failed to exchange Salesforce authorization code')
  }

  return await response.json()
}

async function exchangeHubSpotToken(code: string): Promise<any> {
  const response = await fetch('https://api.hubapi.com/oauth/v1/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: process.env.HUBSPOT_CLIENT_ID!,
      client_secret: process.env.HUBSPOT_CLIENT_SECRET!,
      redirect_uri: process.env.HUBSPOT_REDIRECT_URI!,
      code
    })
  })

  if (!response.ok) {
    throw new Error('Failed to exchange HubSpot authorization code')
  }

  return await response.json()
}

export default router
```

### Step 5: Create CRM Context Service

Create `packages/backend-services/src/services/crmContextService.ts`:

```typescript
import { supabase } from '../supabaseClient'
import SalesforceService from './salesforceService'
import HubSpotService from './hubspotService'
import { CRMService, CRMContact, CRMOpportunity } from './crmService'
import { logger } from '../utils/logger'

interface CRMContext {
  contact?: CRMContact
  opportunities: CRMOpportunity[]
  recentActivities: any[]
  companyInfo?: {
    name: string
    industry?: string
    size?: string
  }
}

class CRMContextService {
  /**
   * Get CRM context for a contact (by email or phone)
   */
  async getContactContext(userId: string, identifier: string, type: 'email' | 'phone' = 'email'): Promise<CRMContext | null> {
    try {
      // Get user's CRM connections
      const connections = await this.getUserCRMConnections(userId)

      if (connections.length === 0) {
        return null
      }

      // Try each connection to find the contact
      for (const connection of connections) {
        try {
          const crmService = this.createCRMService(connection)

          if (!await crmService.validateConnection()) {
            continue
          }

          let contact: CRMContact | null = null

          if (type === 'email') {
            contact = await crmService.getContactByEmail(identifier)
          } else {
            // For phone, we'd need to implement searchContacts with phone filter
            const contacts = await crmService.searchContacts(identifier)
            contact = contacts.find(c => c.phone === identifier) || null
          }

          if (contact) {
            // Get additional context
            const [opportunities, activities] = await Promise.all([
              crmService.getOpportunities(contact.id).catch(() => []),
              crmService.getRecentActivities(contact.id, 5).catch(() => [])
            ])

            return {
              contact,
              opportunities,
              recentActivities: activities,
              companyInfo: contact.company ? {
                name: contact.company
              } : undefined
            }
          }
        } catch (error) {
          logger.error(`[CRMContext] Error getting context from ${connection.crm_type}:`, error)
          continue
        }
      }

      return null
    } catch (error) {
      logger.error('[CRMContext] Error getting contact context:', error)
      return null
    }
  }

  /**
   * Log a call activity to CRM
   */
  async logCallActivity(
    userId: string,
    contactEmail: string,
    callData: {
      subject: string
      description: string
      duration: number
      outcome?: string
    }
  ): Promise<boolean> {
    try {
      const connections = await this.getUserCRMConnections(userId)

      if (connections.length === 0) {
        return false
      }

      let success = false

      for (const connection of connections) {
        try {
          const crmService = this.createCRMService(connection)

          if (!await crmService.validateConnection()) {
            continue
          }

          const contact = await crmService.getContactByEmail(contactEmail)

          if (contact) {
            await crmService.createActivity({
              type: 'call',
              subject: callData.subject,
              description: `${callData.description}\n\nDuration: ${callData.duration} minutes${callData.outcome ? `\nOutcome: ${callData.outcome}` : ''}`,
              date: new Date(),
              contactId: contact.id
            })

            success = true
            logger.info(`[CRMContext] Call logged to ${connection.crm_type} for contact: ${contactEmail}`)
          }
        } catch (error) {
          logger.error(`[CRMContext] Error logging call to ${connection.crm_type}:`, error)
        }
      }

      return success
    } catch (error) {
      logger.error('[CRMContext] Error logging call activity:', error)
      return false
    }
  }

  /**
   * Sync contacts from CRM to local cache
   */
  async syncContacts(userId: string): Promise<number> {
    try {
      const connections = await this.getUserCRMConnections(userId)
      let totalSynced = 0

      for (const connection of connections) {
        try {
          const crmService = this.createCRMService(connection)

          if (!await crmService.validateConnection()) {
            continue
          }

          // Get recent contacts (this is a simplified version)
          // In production, you'd implement pagination and incremental sync
          const contacts = await crmService.searchContacts('')

          for (const contact of contacts) {
            await supabase
              .from('crm_contacts')
              .upsert({
                connection_id: connection.id,
                crm_contact_id: contact.id,
                email: contact.email,
                name: contact.name,
                company: contact.company,
                phone: contact.phone,
                contact_data: {
                  title: contact.title,
                  lastActivity: contact.lastActivity,
                  customFields: contact.customFields
                },
                last_synced: new Date().toISOString()
              })
          }

          totalSynced += contacts.length
          logger.info(`[CRMContext] Synced ${contacts.length} contacts from ${connection.crm_type}`)
        } catch (error) {
          logger.error(`[CRMContext] Error syncing contacts from ${connection.crm_type}:`, error)
        }
      }

      return totalSynced
    } catch (error) {
      logger.error('[CRMContext] Error syncing contacts:', error)
      return 0
    }
  }

  // Helper methods
  private async getUserCRMConnections(userId: string): Promise<any[]> {
    const { data: connections, error } = await supabase
      .from('crm_connections')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')

    if (error) {
      logger.error('[CRMContext] Error fetching user connections:', error)
      return []
    }

    return connections || []
  }

  private createCRMService(connection: any): CRMService {
    if (connection.crm_type === 'salesforce') {
      return new SalesforceService(connection)
    } else if (connection.crm_type === 'hubspot') {
      return new HubSpotService(connection)
    } else {
      throw new Error(`Unsupported CRM type: ${connection.crm_type}`)
    }
  }
}

export default new CRMContextService()
```

## Testing Strategy

### Unit Tests
```typescript
describe('SalesforceService', () => {
  test('should search contacts successfully', async () => {
    const mockConnection = {
      id: 'test-id',
      userId: 'user-123',
      crmType: 'salesforce' as const,
      accessToken: 'test-token',
      instanceUrl: 'https://test.salesforce.com',
      status: 'active' as const
    }

    const service = new SalesforceService(mockConnection)
    // Mock jsforce connection
    // Test contact search functionality
  })
})
```

### Integration Tests
```bash
# Test OAuth flow
curl "http://localhost:4000/api/v1/crm/connect/salesforce" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test contact search
curl "http://localhost:4000/api/v1/crm/contacts/search?q=<EMAIL>" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Integration Points

### With AI Pipeline
- Provides contact context for personalized suggestions
- Enriches conversation context with CRM data
- Enables opportunity-aware assistance

### With Web Portal
- CRM connection management interface
- Contact and opportunity display
- Activity logging and sync status

### With Desktop App
- Real-time contact identification during calls
- Automatic call logging after conversations
- CRM data overlay in suggestions

## Success Criteria

### Functional Requirements
- [ ] OAuth flows work for both Salesforce and HubSpot
- [ ] Contact search and retrieval functions correctly
- [ ] Call activities are logged to CRM systems
- [ ] Token refresh handles expired credentials
- [ ] Error handling covers all failure scenarios

### Performance Requirements
- [ ] Contact lookup completes within 2 seconds
- [ ] OAuth flow completes without errors
- [ ] Token refresh is transparent to users
- [ ] CRM API rate limits are respected

### Security Requirements
- [ ] OAuth tokens are encrypted in database
- [ ] API credentials are properly secured
- [ ] User data isolation is maintained
- [ ] Refresh tokens are handled securely

## Next Steps

1. **Week 4 Day 1-2**: Implement CRM service interfaces and Salesforce integration
2. **Week 4 Day 3**: Implement HubSpot integration and OAuth flows
3. **Week 4 Day 4**: Create CRM context service and API endpoints
4. **Week 4 Day 5**: Testing and error handling
5. **Week 5**: Web portal integration and user interface updates

This completes the core CRM integration functionality, enabling Closezly to provide context-aware sales assistance with real customer data.
```