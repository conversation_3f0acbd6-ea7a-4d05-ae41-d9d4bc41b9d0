# RAG System Implementation Guide

**Priority:** Critical (Week 1)  
**Estimated Time:** 5 days  
**Dependencies:** Supabase database with pgvector extension

## Feature Overview

Implement a complete Retrieval Augmented Generation (RAG) system that allows users to upload documents (PDF, DOCX, TXT) and enables the AI to retrieve relevant context from these documents during sales conversations. This system will provide domain-specific knowledge to enhance AI responses.

## Technical Requirements

### Dependencies to Add
```bash
# Backend services dependencies
cd packages/backend-services
npm install pdf-parse mammoth multer @google/genai

# Web portal dependencies (if needed)
cd apps/web-portal
npm install react-dropzone
```

### Environment Variables
Add to `packages/backend-services/.env`:
```env
# Document storage
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_TYPES=application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain

# Vector embeddings
EMBEDDING_MODEL=text-embedding-004
EMBEDDING_DIMENSIONS=768
CHUNK_SIZE=500
CHUNK_OVERLAP=50
```

## Step-by-Step Implementation

### Step 1: Create Document Processing Service

Create `packages/backend-services/src/services/documentProcessingService.ts`:

```typescript
import fs from 'fs'
import path from 'path'
import pdf from 'pdf-parse'
import mammoth from 'mammoth'
import { GoogleGenAI } from '@google/genai'
import { supabase } from '../supabaseClient'

interface ProcessedDocument {
  chunks: DocumentChunk[]
  metadata: DocumentMetadata
}

interface DocumentChunk {
  content: string
  metadata: {
    page?: number
    section?: string
    chunkIndex: number
    totalChunks: number
  }
}

interface DocumentMetadata {
  title: string
  pageCount?: number
  wordCount: number
  fileType: string
  processingTime: number
}

class DocumentProcessingService {
  private genAI: GoogleGenAI
  private chunkSize: number
  private chunkOverlap: number

  constructor() {
    this.genAI = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY! })
    this.chunkSize = parseInt(process.env.CHUNK_SIZE || '500')
    this.chunkOverlap = parseInt(process.env.CHUNK_OVERLAP || '50')
  }

  /**
   * Process uploaded document and extract text
   */
  async processDocument(filePath: string, fileType: string): Promise<ProcessedDocument> {
    const startTime = Date.now()
    
    try {
      let extractedText: string
      let pageCount: number | undefined

      switch (fileType) {
        case 'application/pdf':
          const pdfResult = await this.extractPdfText(filePath)
          extractedText = pdfResult.text
          pageCount = pdfResult.pageCount
          break
        
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          extractedText = await this.extractDocxText(filePath)
          break
        
        case 'text/plain':
          extractedText = await this.extractPlainText(filePath)
          break
        
        default:
          throw new Error(`Unsupported file type: ${fileType}`)
      }

      // Create chunks
      const chunks = this.createTextChunks(extractedText)
      
      // Calculate metadata
      const metadata: DocumentMetadata = {
        title: path.basename(filePath, path.extname(filePath)),
        pageCount,
        wordCount: extractedText.split(/\s+/).length,
        fileType,
        processingTime: Date.now() - startTime
      }

      return { chunks, metadata }
    } catch (error) {
      console.error('[DocumentProcessing] Error processing document:', error)
      throw new Error(`Failed to process document: ${error.message}`)
    }
  }

  /**
   * Extract text from PDF using pdf-parse
   */
  private async extractPdfText(filePath: string): Promise<{ text: string; pageCount: number }> {
    const dataBuffer = fs.readFileSync(filePath)
    const data = await pdf(dataBuffer)
    
    return {
      text: data.text,
      pageCount: data.numpages
    }
  }

  /**
   * Extract text from DOCX using mammoth
   */
  private async extractDocxText(filePath: string): Promise<string> {
    const result = await mammoth.extractRawText({ path: filePath })
    return result.value
  }

  /**
   * Extract text from plain text file
   */
  private async extractPlainText(filePath: string): Promise<string> {
    return fs.readFileSync(filePath, 'utf-8')
  }

  /**
   * Split text into overlapping chunks
   */
  private createTextChunks(text: string): DocumentChunk[] {
    const words = text.split(/\s+/)
    const chunks: DocumentChunk[] = []
    
    for (let i = 0; i < words.length; i += this.chunkSize - this.chunkOverlap) {
      const chunkWords = words.slice(i, i + this.chunkSize)
      const content = chunkWords.join(' ')
      
      if (content.trim().length > 0) {
        chunks.push({
          content: content.trim(),
          metadata: {
            chunkIndex: chunks.length,
            totalChunks: 0 // Will be updated after all chunks are created
          }
        })
      }
    }

    // Update total chunks count
    chunks.forEach(chunk => {
      chunk.metadata.totalChunks = chunks.length
    })

    return chunks
  }

  /**
   * Generate embeddings for text chunks
   */
  async generateEmbeddings(chunks: DocumentChunk[]): Promise<number[][]> {
    const embeddings: number[][] = []
    
    for (const chunk of chunks) {
      try {
        const result = await this.genAI.models.embedContent({
          model: 'text-embedding-004',
          content: { parts: [{ text: chunk.content }] }
        })
        
        embeddings.push(result.embedding.values)
      } catch (error) {
        console.error('[DocumentProcessing] Error generating embedding:', error)
        throw new Error(`Failed to generate embedding: ${error.message}`)
      }
    }

    return embeddings
  }

  /**
   * Store document and chunks in database
   */
  async storeDocument(
    userId: string,
    documentData: ProcessedDocument,
    filePath: string,
    embeddings: number[][]
  ): Promise<string> {
    try {
      // Insert document record
      const { data: document, error: docError } = await supabase
        .from('documents')
        .insert({
          user_id: userId,
          title: documentData.metadata.title,
          description: `${documentData.metadata.fileType} document with ${documentData.chunks.length} chunks`,
          file_path: filePath,
          file_type: documentData.metadata.fileType,
          status: 'processed'
        })
        .select()
        .single()

      if (docError) throw docError

      // Insert document chunks with embeddings
      const chunkInserts = documentData.chunks.map((chunk, index) => ({
        user_id: userId,
        content: chunk.content,
        metadata: {
          document_id: document.id,
          ...chunk.metadata
        },
        embedding: embeddings[index]
      }))

      const { error: chunksError } = await supabase
        .from('document_chunks')
        .insert(chunkInserts)

      if (chunksError) throw chunksError

      return document.id
    } catch (error) {
      console.error('[DocumentProcessing] Error storing document:', error)
      throw new Error(`Failed to store document: ${error.message}`)
    }
  }
}

export default new DocumentProcessingService()
```

### Step 2: Create Document Upload API Endpoint

Create `packages/backend-services/src/api/v1/knowledge.ts`:

```typescript
import express, { Request, Response } from 'express'
import multer from 'multer'
import path from 'path'
import fs from 'fs'
import { authMiddleware } from '../../authMiddleware'
import DocumentProcessingService from '../../services/documentProcessingService'
import { asyncHandler } from '../../utils/errorHandler'
import { logger } from '../../utils/logger'

const router = express.Router()

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads')
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.UPLOAD_MAX_SIZE || '10485760') // 10MB default
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = process.env.UPLOAD_ALLOWED_TYPES?.split(',') || [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ]
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed`))
    }
  }
})

/**
 * POST /api/v1/knowledge/upload
 * Upload and process a document for RAG
 */
router.post('/upload', authMiddleware, upload.single('document'), asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id
  const file = req.file

  if (!file) {
    return res.status(400).json({
      success: false,
      error: 'No file uploaded'
    })
  }

  try {
    logger.info(`[Knowledge] Processing document upload: ${file.originalname}`)

    // Process document
    const processedDocument = await DocumentProcessingService.processDocument(
      file.path,
      file.mimetype
    )

    // Generate embeddings
    const embeddings = await DocumentProcessingService.generateEmbeddings(
      processedDocument.chunks
    )

    // Store in database
    const documentId = await DocumentProcessingService.storeDocument(
      userId,
      processedDocument,
      file.path,
      embeddings
    )

    logger.info(`[Knowledge] Document processed successfully: ${documentId}`)

    res.status(200).json({
      success: true,
      documentId,
      metadata: processedDocument.metadata,
      chunksCount: processedDocument.chunks.length
    })
  } catch (error) {
    logger.error(`[Knowledge] Error processing upload:`, error)
    
    // Clean up uploaded file on error
    if (fs.existsSync(file.path)) {
      fs.unlinkSync(file.path)
    }

    res.status(500).json({
      success: false,
      error: error.message
    })
  }
}))

export default router
```

### Step 3: Create Vector Search Service

Create `packages/backend-services/src/services/vectorSearchService.ts`:

```typescript
import { GoogleGenAI } from '@google/genai'
import { supabase } from '../supabaseClient'

interface SearchResult {
  id: string
  content: string
  metadata: any
  similarity: number
}

interface SearchOptions {
  threshold?: number
  limit?: number
  userId?: string
}

class VectorSearchService {
  private genAI: GoogleGenAI

  constructor() {
    this.genAI = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY! })
  }

  /**
   * Search for relevant document chunks using semantic similarity
   */
  async searchDocuments(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    const {
      threshold = 0.7,
      limit = 5,
      userId
    } = options

    try {
      // Generate embedding for the query
      const queryEmbedding = await this.generateQueryEmbedding(query)

      // Search using the database function
      const { data, error } = await supabase.rpc('match_documents', {
        query_embedding: queryEmbedding,
        match_threshold: threshold,
        match_count: limit
      })

      if (error) throw error

      // Filter by user if specified
      let results = data || []
      if (userId) {
        const { data: userChunks, error: userError } = await supabase
          .from('document_chunks')
          .select('id')
          .eq('user_id', userId)

        if (userError) throw userError

        const userChunkIds = new Set(userChunks.map(chunk => chunk.id))
        results = results.filter(result => userChunkIds.has(result.id))
      }

      return results
    } catch (error) {
      console.error('[VectorSearch] Error searching documents:', error)
      throw new Error(`Failed to search documents: ${error.message}`)
    }
  }

  /**
   * Generate embedding for search query
   */
  private async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      const result = await this.genAI.models.embedContent({
        model: 'text-embedding-004',
        content: { parts: [{ text: query }] }
      })

      return result.embedding.values
    } catch (error) {
      console.error('[VectorSearch] Error generating query embedding:', error)
      throw new Error(`Failed to generate query embedding: ${error.message}`)
    }
  }

  /**
   * Get context for AI prompts by combining search results
   */
  async getContextForPrompt(query: string, userId: string, maxTokens: number = 2000): Promise<string> {
    const results = await this.searchDocuments(query, {
      userId,
      limit: 10,
      threshold: 0.6
    })

    if (results.length === 0) {
      return ''
    }

    // Combine results into context, respecting token limit
    let context = ''
    let tokenCount = 0

    for (const result of results) {
      const chunkText = `[Document: ${result.metadata.document_id}]\n${result.content}\n\n`
      const estimatedTokens = chunkText.length / 4 // Rough estimation

      if (tokenCount + estimatedTokens > maxTokens) {
        break
      }

      context += chunkText
      tokenCount += estimatedTokens
    }

    return context.trim()
  }
}

export default new VectorSearchService()
```

### Step 4: Add Knowledge Search Endpoint

Add to `packages/backend-services/src/api/v1/knowledge.ts`:

```typescript
/**
 * POST /api/v1/knowledge/search
 * Search documents using semantic similarity
 */
router.post('/search', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id
  const { query, limit = 5, threshold = 0.7 } = req.body

  if (!query) {
    return res.status(400).json({
      success: false,
      error: 'Query is required'
    })
  }

  try {
    const results = await VectorSearchService.searchDocuments(query, {
      userId,
      limit,
      threshold
    })

    res.status(200).json({
      success: true,
      results,
      count: results.length
    })
  } catch (error) {
    logger.error(`[Knowledge] Error searching documents:`, error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
}))

/**
 * GET /api/v1/knowledge/documents
 * List user's uploaded documents
 */
router.get('/documents', authMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const userId = (req as any).user?.id

  try {
    const { data: documents, error } = await supabase
      .from('documents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) throw error

    res.status(200).json({
      success: true,
      documents
    })
  } catch (error) {
    logger.error(`[Knowledge] Error fetching documents:`, error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
}))
```

### Step 5: Update Main API Router

Update `packages/backend-services/src/api/v1/index.ts`:

```typescript
import knowledgeRouter from './knowledge'

// Add the knowledge router
router.use('/knowledge', knowledgeRouter)
```

### Step 6: Integrate RAG with AI Service

Update `packages/backend-services/src/services/llmOrchestrationService.ts`:

```typescript
// Add import at the top
import VectorSearchService from './vectorSearchService'

// Add new function for RAG-enhanced generation
async function generateTextWithRAG(
  options: GenerateTextOptions & { userId?: string; ragQuery?: string }
): Promise<GenerateTextResponse> {
  const { prompt, userId, ragQuery, ...genOptions } = options

  try {
    let enhancedPrompt = prompt

    // Add RAG context if query and user provided
    if (ragQuery && userId) {
      const ragContext = await VectorSearchService.getContextForPrompt(ragQuery, userId)

      if (ragContext) {
        enhancedPrompt = `Context from knowledge base:
${ragContext}

---

${prompt}`
      }
    }

    // Use existing generateText function with enhanced prompt
    return await generateText({
      ...genOptions,
      prompt: enhancedPrompt
    })
  } catch (error) {
    console.error('[LLMService] Error in RAG-enhanced generation:', error)
    throw error
  }
}

// Export the new function
export { generateText, generateTextStream, generateMultimodalContent, generateTextWithRAG }
```

## Testing Strategy

### Unit Tests
Create `packages/backend-services/src/__tests__/documentProcessing.test.ts`:

```typescript
import DocumentProcessingService from '../services/documentProcessingService'
import fs from 'fs'
import path from 'path'

describe('DocumentProcessingService', () => {
  const testFilesDir = path.join(__dirname, 'fixtures')

  beforeAll(() => {
    // Create test files directory and sample files
    if (!fs.existsSync(testFilesDir)) {
      fs.mkdirSync(testFilesDir, { recursive: true })
    }
    
    // Create a sample text file
    fs.writeFileSync(
      path.join(testFilesDir, 'sample.txt'),
      'This is a sample document for testing. It contains multiple sentences to test chunking.'
    )
  })

  test('should process plain text file', async () => {
    const filePath = path.join(testFilesDir, 'sample.txt')
    const result = await DocumentProcessingService.processDocument(filePath, 'text/plain')
    
    expect(result.chunks).toHaveLength(1)
    expect(result.metadata.fileType).toBe('text/plain')
    expect(result.metadata.wordCount).toBeGreaterThan(0)
  })

  test('should generate embeddings for chunks', async () => {
    const chunks = [
      { content: 'Test content', metadata: { chunkIndex: 0, totalChunks: 1 } }
    ]
    
    const embeddings = await DocumentProcessingService.generateEmbeddings(chunks)
    
    expect(embeddings).toHaveLength(1)
    expect(embeddings[0]).toHaveLength(768) // text-embedding-004 dimensions
  })

  afterAll(() => {
    // Clean up test files
    if (fs.existsSync(testFilesDir)) {
      fs.rmSync(testFilesDir, { recursive: true })
    }
  })
})
```

### Integration Tests
Create test for the complete upload flow:

```bash
# Test document upload
curl -X POST http://localhost:4000/api/v1/knowledge/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "document=@test-document.pdf"

# Test search functionality
curl -X POST http://localhost:4000/api/v1/knowledge/search \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"query": "sales objections", "limit": 5}'
```

## Integration Points

### With Existing AI Service
- Enhances existing prompt templates with relevant document context
- Maintains compatibility with current AI assistance endpoints
- Adds optional RAG context to all assistance types

### With Web Portal
- Document upload interface in `/apps/web-portal/app/dashboard/knowledge/page.tsx`
- Document management and status display
- Upload progress and error handling

### With Desktop App
- Future integration for document upload from desktop
- Context-aware suggestions based on uploaded knowledge

## Success Criteria

### Functional Requirements
- [ ] Users can upload PDF, DOCX, and TXT files
- [ ] Documents are processed and chunked correctly
- [ ] Vector embeddings are generated and stored
- [ ] Semantic search returns relevant results
- [ ] AI responses include relevant document context
- [ ] Upload errors are handled gracefully

### Performance Requirements
- [ ] Document processing completes within 30 seconds for 10MB files
- [ ] Vector search returns results within 500ms
- [ ] Embedding generation handles batch processing efficiently
- [ ] Database queries are optimized with proper indexing

### Quality Requirements
- [ ] Text extraction accuracy > 95% for well-formatted documents
- [ ] Search relevance score > 0.7 for related content
- [ ] No memory leaks during large document processing
- [ ] Proper error handling and logging throughout

### Security Requirements
- [ ] File uploads are validated and sanitized
- [ ] User documents are isolated (RLS policies)
- [ ] Temporary files are cleaned up after processing
- [ ] API endpoints require authentication

## Next Steps

1. **Week 1 Day 1-2**: Implement document processing service and upload endpoint
2. **Week 1 Day 3**: Create vector search service and database integration
3. **Week 1 Day 4**: Integrate RAG with existing AI service
4. **Week 1 Day 5**: Testing, optimization, and web portal integration

After completion, proceed to `audio-transcription-integration.md` for real-time audio processing implementation.
