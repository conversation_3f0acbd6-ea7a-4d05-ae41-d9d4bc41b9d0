# Audio Transcription Integration Guide

**Priority:** Critical (Week 2)  
**Estimated Time:** 5 days  
**Dependencies:** Desktop app audio capture, backend services

## Feature Overview

Implement real-time audio transcription that captures live sales conversations, processes them through speech-to-text services, and provides contextual transcripts to the AI system for generating relevant suggestions during calls.

## Technical Requirements

### Dependencies to Add
```bash
# Backend services dependencies
cd packages/backend-services
npm install @deepgram/sdk ws socket.io

# Desktop app dependencies (already available)
# - nodejs-whisper (already in package.json)
# - @napi-rs/whisper (already in package.json)
# - ws (already in package.json)
```

### Environment Variables
Add to `packages/backend-services/.env`:
```env
# Transcription service configuration
TRANSCRIPTION_SERVICE=deepgram  # or 'whisper'
DEEPGRAM_API_KEY=your_deepgram_api_key_here

# Audio processing
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
CHUNK_DURATION_MS=1000
TRANSCRIPT_BUFFER_SIZE=10

# WebSocket configuration
WS_PORT=4001
WS_HEARTBEAT_INTERVAL=30000
```

### Service Selection
Choose between two transcription services:
1. **Deepgram** (Recommended for production): Real-time, high accuracy, speaker diarization
2. **Local Whisper** (For development/privacy): Runs locally, no API costs, slower processing

## Step-by-Step Implementation

### Step 1: Create Transcription Service Interface

Create `packages/backend-services/src/services/transcriptionService.ts`:

```typescript
export interface TranscriptionResult {
  text: string
  confidence: number
  timestamp: number
  speaker?: string
  isFinal: boolean
}

export interface TranscriptionOptions {
  language?: string
  enableSpeakerDiarization?: boolean
  enablePunctuation?: boolean
  model?: string
}

export abstract class TranscriptionService {
  abstract startTranscription(options?: TranscriptionOptions): Promise<void>
  abstract stopTranscription(): Promise<void>
  abstract processAudioChunk(audioData: Buffer): Promise<TranscriptionResult[]>
  abstract isConnected(): boolean
}
```

### Step 2: Implement Deepgram Transcription Service

Create `packages/backend-services/src/services/deepgramTranscriptionService.ts`:

```typescript
import { createClient, LiveTranscriptionEvents } from '@deepgram/sdk'
import { TranscriptionService, TranscriptionResult, TranscriptionOptions } from './transcriptionService'
import { EventEmitter } from 'events'

class DeepgramTranscriptionService extends TranscriptionService {
  private deepgram: any
  private liveTranscription: any
  private isActive: boolean = false
  private eventEmitter: EventEmitter

  constructor() {
    super()
    this.deepgram = createClient(process.env.DEEPGRAM_API_KEY!)
    this.eventEmitter = new EventEmitter()
  }

  async startTranscription(options: TranscriptionOptions = {}): Promise<void> {
    if (this.isActive) {
      console.log('[Deepgram] Transcription already active')
      return
    }

    try {
      const transcriptionOptions = {
        model: options.model || 'nova-2',
        language: options.language || 'en-US',
        smart_format: options.enablePunctuation !== false,
        diarize: options.enableSpeakerDiarization || false,
        punctuate: options.enablePunctuation !== false,
        interim_results: true,
        endpointing: 300,
        utterance_end_ms: 1000,
        vad_events: true,
        encoding: 'linear16',
        sample_rate: parseInt(process.env.AUDIO_SAMPLE_RATE || '16000'),
        channels: parseInt(process.env.AUDIO_CHANNELS || '1')
      }

      this.liveTranscription = this.deepgram.listen.live(transcriptionOptions)

      // Set up event listeners
      this.liveTranscription.on(LiveTranscriptionEvents.Open, () => {
        console.log('[Deepgram] Connection opened')
        this.isActive = true
      })

      this.liveTranscription.on(LiveTranscriptionEvents.Transcript, (data: any) => {
        this.handleTranscriptionResult(data)
      })

      this.liveTranscription.on(LiveTranscriptionEvents.Error, (error: any) => {
        console.error('[Deepgram] Error:', error)
        this.eventEmitter.emit('error', error)
      })

      this.liveTranscription.on(LiveTranscriptionEvents.Close, () => {
        console.log('[Deepgram] Connection closed')
        this.isActive = false
      })

      console.log('[Deepgram] Transcription service started')
    } catch (error) {
      console.error('[Deepgram] Failed to start transcription:', error)
      throw error
    }
  }

  async stopTranscription(): Promise<void> {
    if (!this.isActive || !this.liveTranscription) {
      return
    }

    try {
      this.liveTranscription.finish()
      this.isActive = false
      console.log('[Deepgram] Transcription service stopped')
    } catch (error) {
      console.error('[Deepgram] Error stopping transcription:', error)
      throw error
    }
  }

  async processAudioChunk(audioData: Buffer): Promise<TranscriptionResult[]> {
    if (!this.isActive || !this.liveTranscription) {
      return []
    }

    try {
      // Send audio data to Deepgram
      this.liveTranscription.send(audioData)
      return [] // Results come through event listeners
    } catch (error) {
      console.error('[Deepgram] Error processing audio chunk:', error)
      return []
    }
  }

  isConnected(): boolean {
    return this.isActive
  }

  private handleTranscriptionResult(data: any): void {
    const channel = data.channel
    const alternatives = channel?.alternatives

    if (!alternatives || alternatives.length === 0) {
      return
    }

    const alternative = alternatives[0]
    const transcript = alternative.transcript

    if (!transcript || transcript.trim().length === 0) {
      return
    }

    const result: TranscriptionResult = {
      text: transcript,
      confidence: alternative.confidence || 0,
      timestamp: Date.now(),
      isFinal: channel.is_final || false
    }

    // Add speaker information if available
    if (data.metadata?.speaker !== undefined) {
      result.speaker = `Speaker ${data.metadata.speaker}`
    }

    this.eventEmitter.emit('transcription', result)
  }

  // Event emitter methods
  on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener)
  }

  off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener)
  }
}

export default DeepgramTranscriptionService
```

### Step 3: Create WebSocket Server for Real-time Communication

Create `packages/backend-services/src/services/websocketService.ts`:

```typescript
import { Server as SocketIOServer } from 'socket.io'
import { Server as HTTPServer } from 'http'
import DeepgramTranscriptionService from './deepgramTranscriptionService'
import { authMiddleware } from '../authMiddleware'
import { logger } from '../utils/logger'

interface AudioSession {
  userId: string
  sessionId: string
  transcriptionService: DeepgramTranscriptionService
  transcript: string[]
  startTime: number
}

class WebSocketService {
  private io: SocketIOServer
  private activeSessions: Map<string, AudioSession> = new Map()

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: ["http://localhost:5173", "http://localhost:3000"],
        methods: ["GET", "POST"]
      },
      transports: ['websocket', 'polling']
    })

    this.setupSocketHandlers()
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket) => {
      logger.info(`[WebSocket] Client connected: ${socket.id}`)

      // Authenticate socket connection
      socket.on('authenticate', async (token: string) => {
        try {
          // Verify JWT token (implement based on your auth system)
          const user = await this.verifyToken(token)
          socket.data.userId = user.id
          socket.emit('authenticated', { success: true })
          logger.info(`[WebSocket] User authenticated: ${user.id}`)
        } catch (error) {
          socket.emit('authenticated', { success: false, error: 'Invalid token' })
          socket.disconnect()
        }
      })

      // Start audio transcription session
      socket.on('start-transcription', async (options = {}) => {
        if (!socket.data.userId) {
          socket.emit('error', { message: 'Not authenticated' })
          return
        }

        try {
          const sessionId = `${socket.data.userId}-${Date.now()}`
          const transcriptionService = new DeepgramTranscriptionService()

          // Set up transcription event listeners
          transcriptionService.on('transcription', (result) => {
            socket.emit('transcription-result', result)
            
            // Store transcript segment
            const session = this.activeSessions.get(sessionId)
            if (session && result.isFinal) {
              session.transcript.push(result.text)
            }
          })

          transcriptionService.on('error', (error) => {
            socket.emit('transcription-error', { error: error.message })
          })

          await transcriptionService.startTranscription(options)

          const session: AudioSession = {
            userId: socket.data.userId,
            sessionId,
            transcriptionService,
            transcript: [],
            startTime: Date.now()
          }

          this.activeSessions.set(sessionId, session)
          socket.data.sessionId = sessionId

          socket.emit('transcription-started', { sessionId })
          logger.info(`[WebSocket] Transcription started for user: ${socket.data.userId}`)
        } catch (error) {
          logger.error('[WebSocket] Error starting transcription:', error)
          socket.emit('error', { message: 'Failed to start transcription' })
        }
      })

      // Process audio data
      socket.on('audio-data', async (audioData: Buffer) => {
        const sessionId = socket.data.sessionId
        const session = this.activeSessions.get(sessionId)

        if (!session) {
          socket.emit('error', { message: 'No active transcription session' })
          return
        }

        try {
          await session.transcriptionService.processAudioChunk(audioData)
        } catch (error) {
          logger.error('[WebSocket] Error processing audio data:', error)
          socket.emit('error', { message: 'Failed to process audio data' })
        }
      })

      // Stop transcription session
      socket.on('stop-transcription', async () => {
        const sessionId = socket.data.sessionId
        const session = this.activeSessions.get(sessionId)

        if (session) {
          try {
            await session.transcriptionService.stopTranscription()
            
            // Save transcript to database
            await this.saveTranscript(session)
            
            this.activeSessions.delete(sessionId)
            socket.emit('transcription-stopped', { 
              sessionId,
              transcript: session.transcript.join(' ')
            })
            
            logger.info(`[WebSocket] Transcription stopped for user: ${session.userId}`)
          } catch (error) {
            logger.error('[WebSocket] Error stopping transcription:', error)
            socket.emit('error', { message: 'Failed to stop transcription' })
          }
        }
      })

      // Handle disconnection
      socket.on('disconnect', async () => {
        logger.info(`[WebSocket] Client disconnected: ${socket.id}`)
        
        const sessionId = socket.data.sessionId
        const session = this.activeSessions.get(sessionId)
        
        if (session) {
          await session.transcriptionService.stopTranscription()
          await this.saveTranscript(session)
          this.activeSessions.delete(sessionId)
        }
      })
    })
  }

  private async verifyToken(token: string): Promise<{ id: string }> {
    // Implement JWT verification based on your auth system
    // This is a placeholder - replace with actual implementation
    try {
      // Use your existing auth verification logic
      return { id: 'user-id' } // Replace with actual user data
    } catch (error) {
      throw new Error('Invalid token')
    }
  }

  private async saveTranscript(session: AudioSession): Promise<void> {
    try {
      // Save transcript to database using Supabase
      const { supabase } = require('../supabaseClient')
      
      const { error } = await supabase
        .from('call_transcripts')
        .insert({
          user_id: session.userId,
          call_start_time: new Date(session.startTime),
          call_end_time: new Date(),
          full_transcript: session.transcript.join(' '),
          transcript_segments: session.transcript.map((text, index) => ({
            index,
            text,
            timestamp: session.startTime + (index * 1000) // Rough estimation
          }))
        })

      if (error) {
        logger.error('[WebSocket] Error saving transcript:', error)
      } else {
        logger.info(`[WebSocket] Transcript saved for session: ${session.sessionId}`)
      }
    } catch (error) {
      logger.error('[WebSocket] Error saving transcript:', error)
    }
  }
}

export default WebSocketService
```

### Step 4: Update Desktop App Audio Integration

Update `apps/desktop/electron/helpers/AudioCaptureService.ts`:

```typescript
// Add WebSocket connection for real-time transcription
import WebSocket from 'ws'

class AudioCaptureService {
  private ws: WebSocket | null = null
  private isTranscribing: boolean = false

  // Add method to connect to transcription service
  public async connectToTranscriptionService(authToken: string): Promise<boolean> {
    try {
      this.ws = new WebSocket('ws://localhost:4001')
      
      this.ws.on('open', () => {
        console.log('[AudioCapture] Connected to transcription service')
        // Authenticate
        this.ws?.send(JSON.stringify({
          type: 'authenticate',
          token: authToken
        }))
      })

      this.ws.on('message', (data) => {
        const message = JSON.parse(data.toString())
        this.handleTranscriptionMessage(message)
      })

      this.ws.on('error', (error) => {
        console.error('[AudioCapture] WebSocket error:', error)
      })

      this.ws.on('close', () => {
        console.log('[AudioCapture] Disconnected from transcription service')
        this.isTranscribing = false
      })

      return true
    } catch (error) {
      console.error('[AudioCapture] Failed to connect to transcription service:', error)
      return false
    }
  }

  // Add method to start real-time transcription
  public async startRealTimeTranscription(): Promise<boolean> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('[AudioCapture] Not connected to transcription service')
      return false
    }

    try {
      this.ws.send(JSON.stringify({
        type: 'start-transcription',
        options: {
          language: 'en-US',
          enableSpeakerDiarization: true,
          enablePunctuation: true
        }
      }))

      this.isTranscribing = true
      return true
    } catch (error) {
      console.error('[AudioCapture] Failed to start transcription:', error)
      return false
    }
  }

  // Add method to send audio data
  private sendAudioData(audioBuffer: Buffer): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN && this.isTranscribing) {
      this.ws.send(audioBuffer)
    }
  }

  // Handle transcription messages
  private handleTranscriptionMessage(message: any): void {
    switch (message.type) {
      case 'authenticated':
        if (message.success) {
          console.log('[AudioCapture] Successfully authenticated with transcription service')
        } else {
          console.error('[AudioCapture] Authentication failed:', message.error)
        }
        break
      
      case 'transcription-result':
        // Forward to main process
        AppState.updateTranscript(message.data)
        break
      
      case 'transcription-started':
        console.log('[AudioCapture] Transcription session started:', message.sessionId)
        break
      
      case 'error':
        console.error('[AudioCapture] Transcription error:', message.message)
        break
    }
  }
}
```

### Step 5: Update Backend Server to Include WebSocket

Update `packages/backend-services/src/index.ts`:

```typescript
import { createServer } from 'http'
import WebSocketService from './services/websocketService'

// Create HTTP server
const server = createServer(app)

// Initialize WebSocket service
const wsService = new WebSocketService(server)

// Start server
const PORT = process.env.PORT ? Number(process.env.PORT) : 4000
const WS_PORT = process.env.WS_PORT ? Number(process.env.WS_PORT) : 4001

server.listen(PORT, () => {
  logStartup()
  logger.info(`Closezly Backend API running on port ${PORT}`)
  logger.info(`WebSocket service running on port ${PORT}`)
})
```

## Testing Strategy

### Unit Tests
Create `packages/backend-services/src/__tests__/transcription.test.ts`:

```typescript
import DeepgramTranscriptionService from '../services/deepgramTranscriptionService'

describe('DeepgramTranscriptionService', () => {
  let service: DeepgramTranscriptionService

  beforeEach(() => {
    service = new DeepgramTranscriptionService()
  })

  test('should initialize without errors', () => {
    expect(service).toBeDefined()
    expect(service.isConnected()).toBe(false)
  })

  test('should handle transcription results', (done) => {
    service.on('transcription', (result) => {
      expect(result).toHaveProperty('text')
      expect(result).toHaveProperty('confidence')
      expect(result).toHaveProperty('timestamp')
      done()
    })

    // Simulate transcription result
    service['handleTranscriptionResult']({
      channel: {
        alternatives: [{
          transcript: 'Hello world',
          confidence: 0.95
        }],
        is_final: true
      }
    })
  })
})
```

### Integration Tests
```bash
# Test WebSocket connection
wscat -c ws://localhost:4001

# Test audio streaming (requires audio file)
node test-audio-stream.js
```

## Integration Points

### With Desktop App
- Real-time audio streaming from microphone capture
- WebSocket connection for live transcription
- Transcript display in overlay UI

### With AI Service
- Transcript context for AI prompt generation
- Real-time suggestion triggers based on conversation
- Speaker diarization for context awareness

### With RAG System
- Transcript-based knowledge retrieval
- Context-aware document search
- Historical conversation analysis

## Success Criteria

### Functional Requirements
- [ ] Real-time audio transcription with <2 second latency
- [ ] Speaker diarization accuracy >85%
- [ ] WebSocket connection stability during long calls
- [ ] Transcript storage and retrieval
- [ ] Integration with existing AI assistance

### Performance Requirements
- [ ] Audio processing latency <500ms
- [ ] Transcription accuracy >90% for clear speech
- [ ] WebSocket handles 60+ minute sessions
- [ ] Memory usage remains stable during long calls

### Quality Requirements
- [ ] Graceful handling of network interruptions
- [ ] Audio quality adaptation based on input
- [ ] Proper error handling and recovery
- [ ] Clean transcript formatting and punctuation

## Next Steps

1. **Week 2 Day 1**: Implement Deepgram transcription service
2. **Week 2 Day 2**: Create WebSocket server and real-time communication
3. **Week 2 Day 3**: Update desktop app audio integration
4. **Week 2 Day 4**: Testing and optimization
5. **Week 2 Day 5**: Integration with AI service and transcript storage

After completion, proceed to `end-to-end-ai-pipeline.md` for complete AI pipeline integration.
